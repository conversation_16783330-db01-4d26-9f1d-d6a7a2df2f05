package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"decodemed/ai/quizzes"
	"decodemed/api/handlers/async"
	"decodemed/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"
)

// HandleAsyncQuizGeneration initiates async quiz generation
func HandleAsyncQuizGeneration(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			UnitID uint `json:"unit_id" binding:"required"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		userID, _ := c.Get("user_id")

		// Verify unit belongs to user
		var unit models.Unit
		if err := db.Joins("JOIN projects ON units.project_id = projects.id").
			Where("units.id = ? AND projects.user_id = ?", req.UnitID, userID).
			First(&unit).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found"})
			return
		}

		// Create job ID and initial status
		jobID := uuid.New().String()
		jobStatus := &async.JobStatus{
			Status:    "pending",
			Progress:  0,
			CreatedAt: time.Now(),
		}

		async.JobMutex.Lock()
		async.JobStatuses[jobID] = jobStatus
		async.JobMutex.Unlock()

		// Start async processing
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("Recovered from panic in quiz generation: %v", r)
					async.JobMutex.Lock()
					jobStatus.Status = "failed"
					jobStatus.Error = "Internal server error"
					async.JobMutex.Unlock()
				}
			}()

			// Update job status
			async.JobMutex.Lock()
			jobStatus.Status = "processing"
			jobStatus.Progress = 10
			async.JobMutex.Unlock()

			// Delete existing quiz if it exists
			if err := db.Where("unit_id = ?", unit.Model.ID).Delete(&models.Quiz{}).Error; err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to delete existing quiz"
				return
			}

			async.JobMutex.Lock()
			jobStatus.Progress = 30
			async.JobMutex.Unlock()

			// Create new quiz
			quiz := models.Quiz{
				UnitID:    unit.Model.ID,
				Questions: GenerateQuizContent(unit),
			}

			// Save quiz to database
			if err := db.Create(&quiz).Error; err != nil {
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to save quiz"
				async.JobMutex.Unlock()
				return
			}

			// Update job status to complete
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			async.JobMutex.Unlock()
		}()

		c.JSON(http.StatusOK, gin.H{
			"job_id": jobID,
			"status": "pending",
		})
	}
}

// GetQuizStatus returns the status of an async quiz generation job
func GetQuizStatus(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("jobId")

		async.JobMutex.Lock()
		status, exists := async.JobStatuses[jobID]
		async.JobMutex.Unlock()

		if !exists {
			c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
			return
		}

		c.JSON(http.StatusOK, status)
	}
}

// GetQuiz returns a specific quiz
func GetQuiz(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		unitID := c.Param("id")
		userID, _ := c.Get("user_id")

		// First verify the unit exists and belongs to the user
		var unit models.Unit
		if err := db.Joins("JOIN projects ON units.project_id = projects.id").
			Where("units.id = ? AND projects.user_id = ?", unitID, userID).
			First(&unit).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found"})
			return
		}

		// Try to get existing quiz
		var quiz models.Quiz
		err := db.Where("unit_id = ?", unitID).First(&quiz).Error
		if err != nil {
			// If quiz doesn't exist, create a new one
			quiz = models.Quiz{
				UnitID:       unit.Model.ID,
				Questions:    GenerateQuizContent(unit),
				MaxScore:     100, // Default max score
				PassingScore: 70,  // Default passing score
			}

			if err := db.Create(&quiz).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz"})
				return
			}
		}

		c.JSON(http.StatusOK, quiz)
	}
}

// UpdateQuizProgress saves the user's progress on a quiz
func UpdateQuizProgress(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		type QuizProgressRequest struct {
			UserProgress string `json:"user_progress" binding:"required"`
			MaxScore     int    `json:"max_score"`
			CurrentScore int    `json:"current_score"`
		}

		var req QuizProgressRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		unitID := c.Param("id")
		userID, _ := c.Get("user_id")

		// Verify the user has access to this quiz
		var quiz models.Quiz
		if err := db.Joins("JOIN units ON quizzes.unit_id = units.id").
			Joins("JOIN projects ON units.project_id = projects.id").
			Where("units.id = ? AND projects.user_id = ?", unitID, userID).
			First(&quiz).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
			return
		}

		// Update the quiz progress
		quiz.UserProgress = req.UserProgress
		if req.MaxScore > 0 {
			quiz.MaxScore = req.MaxScore
		}

		if err := db.Save(&quiz).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save quiz progress"})
			return
		}

		c.JSON(http.StatusOK, quiz)
	}
}

// AI function to generate quiz content
func GenerateQuizContent(unit models.Unit) string {
	ctx := context.Background()

	// Combine content based on unit type
	var content string
	if unit.Type == models.TypeYouTube {
		content = unit.YoutubeSummary
	} else {
		content = unit.Content
	}

	// Generate quiz using both source text and content
	quizContent, err := quizzes.GenerateContentQuiz(ctx, unit.Title, content, unit.SourceText)
	if err != nil {
		log.Printf("Error generating quiz: %v", err)
		return "[]" // Return empty quiz array on error
	}

	// Clean up markdown code block formatting from AI response
	quizContent = cleanMarkdownCodeBlocks(quizContent)

	// Validate the cleaned content is a valid JSON array
	var questions []map[string]interface{}
	if err := json.Unmarshal([]byte(quizContent), &questions); err != nil {
		log.Printf("Error validating quiz content: %v", err)
		return "[]" // Return empty quiz array if validation fails
	}

	// Remove source validation from the quiz content before saving
	quizContent = removeSourceValidation(quizContent)

	// Final validation before returning
	if err := json.Unmarshal([]byte(quizContent), &questions); err != nil {
		log.Printf("Error in final quiz content validation: %v", err)
		return "[]" // Return empty quiz array if final validation fails
	}

	return quizContent
}

// Helper function to clean markdown code blocks from AI responses
func cleanMarkdownCodeBlocks(content string) string {
	// Remove markdown code fence markers
	content = strings.TrimPrefix(content, "```json")
	content = strings.TrimPrefix(content, "```")
	content = strings.TrimSuffix(content, "```")

	// Trim whitespace
	content = strings.TrimSpace(content)

	// Validate JSON
	var js interface{}
	if err := json.Unmarshal([]byte(content), &js); err != nil {
		log.Printf("Warning: Generated content is not valid JSON after cleaning: %v", err)
		return "[]" // Return empty array if JSON is invalid
	}

	return content
}

// Helper function to remove source validation from quiz content
func removeSourceValidation(content string) string {
	var questions []map[string]interface{}
	if err := json.Unmarshal([]byte(content), &questions); err != nil {
		log.Printf("Error unmarshaling quiz content: %v", err)
		return content
	}

	// Remove source_validation field from each question
	for _, question := range questions {
		delete(question, "source_validation")
	}

	// Convert back to JSON
	cleanedContent, err := json.Marshal(questions)
	if err != nil {
		log.Printf("Error marshaling cleaned quiz content: %v", err)
		return content
	}

	return string(cleanedContent)
}

// QuizWebSocketHandler manages WebSocket connections for quiz updates
// This replaces the previous handleWebSocket function to avoid redeclaration
func QuizWebSocketHandler(c *gin.Context) {
	conn, err := async.Upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	// Store active connections for broadcasting
	clientUUID := uuid.New().String()

	// Map to track which projects this connection is subscribed to
	subscribedProjects := make(map[uint]bool)

	// Register connection
	async.ConnectionsMutex.Lock()
	async.QuizConnections[clientUUID] = conn
	async.ConnectionsMutex.Unlock()

	// Ensure we remove this connection when done
	defer func() {
		async.ConnectionsMutex.Lock()
		delete(async.QuizConnections, clientUUID)
		async.ConnectionsMutex.Unlock()
	}()

	// Handle WebSocket connection
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			log.Printf("Error reading message: %v", err)
			break
		}

		// Handle different message types
		var request struct {
			Type    string      `json:"type"`
			Payload interface{} `json:"payload"`
		}
		if err := json.Unmarshal(message, &request); err != nil {
			log.Printf("Error unmarshaling message: %v", err)
			continue
		}

		// Handle different request types
		switch request.Type {
		case "subscribe_quiz":
			// Handle quiz subscription
			var projectPayload struct {
				ProjectID uint `json:"project_id"`
			}

			// Try to extract project_id from the payload
			if payloadMap, ok := request.Payload.(map[string]interface{}); ok {
				if pid, exists := payloadMap["project_id"]; exists {
					// Try converting to uint
					if pidFloat, ok := pid.(float64); ok {
						projectPayload.ProjectID = uint(pidFloat)
					} else if pidStr, ok := pid.(string); ok {
						if pidUint, err := strconv.ParseUint(pidStr, 10, 32); err == nil {
							projectPayload.ProjectID = uint(pidUint)
						}
					}
				}
			}

			if projectPayload.ProjectID > 0 {
				// Add this project to subscription list
				subscribedProjects[projectPayload.ProjectID] = true

				// Send confirmation
				response := map[string]interface{}{
					"type": "subscription_confirmed",
					"payload": map[string]interface{}{
						"project_id": projectPayload.ProjectID,
						"message":    "Subscribed to quiz updates for this project",
					},
				}

				respBytes, _ := json.Marshal(response)
				if err := conn.WriteMessage(websocket.TextMessage, respBytes); err != nil {
					log.Printf("Error sending subscription confirmation: %v", err)
				}
			}

		case "unsubscribe_quiz":
			// Handle quiz unsubscription
			var projectPayload struct {
				ProjectID uint `json:"project_id"`
			}

			// Try to extract project_id from the payload
			if payloadMap, ok := request.Payload.(map[string]interface{}); ok {
				if pid, exists := payloadMap["project_id"]; exists {
					// Try converting to uint
					if pidFloat, ok := pid.(float64); ok {
						projectPayload.ProjectID = uint(pidFloat)
					} else if pidStr, ok := pid.(string); ok {
						if pidUint, err := strconv.ParseUint(pidStr, 10, 32); err == nil {
							projectPayload.ProjectID = uint(pidUint)
						}
					}
				}
			}

			if projectPayload.ProjectID > 0 {
				// Remove this project from subscription list
				delete(subscribedProjects, projectPayload.ProjectID)

				// Send confirmation
				response := map[string]interface{}{
					"type": "unsubscription_confirmed",
					"payload": map[string]interface{}{
						"project_id": projectPayload.ProjectID,
						"message":    "Unsubscribed from quiz updates for this project",
					},
				}

				respBytes, _ := json.Marshal(response)
				if err := conn.WriteMessage(websocket.TextMessage, respBytes); err != nil {
					log.Printf("Error sending unsubscription confirmation: %v", err)
				}
			}
		}
	}
}

// GenerateAllQuizzes handles batch generation of quizzes for all units
func GenerateAllQuizzes(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, _ := c.Get("user_id")

		// Get project ID from query parameters
		projectIDStr := c.Query("project_id")
		if projectIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
			return
		}

		var projectID uint
		if _, err := fmt.Sscanf(projectIDStr, "%d", &projectID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid project_id format"})
			return
		}

		// Verify project belongs to user
		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Check if units are generated and if quizzes need to be generated
		if !project.UnitsGenerated && !project.YouTubeUnitsGenerated {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No units have been generated for this project yet"})
			return
		}

		// Skip generation if quizzes are already generated and force flag is not set
		forceRegenerate := c.Query("force") == "true"
		if project.QuizzesGenerated && !forceRegenerate {
			c.JSON(http.StatusOK, gin.H{
				"message": "Quizzes already generated for this project",
				"status":  "completed",
			})
			return
		}

		// Create job ID and initial status
		jobID := uuid.New().String()
		jobStatus := &async.JobStatus{
			Status:    "pending",
			Progress:  0,
			CreatedAt: time.Now(),
		}

		async.JobMutex.Lock()
		async.JobStatuses[jobID] = jobStatus
		async.JobMutex.Unlock()

		// Broadcast initial status via WebSocket
		async.BroadcastQuizUpdate(projectID, "pending", 0)

		// Start async processing
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("Recovered from panic in batch quiz generation: %v", r)
					async.JobMutex.Lock()
					jobStatus.Status = "failed"
					jobStatus.Error = "Internal server error"
					async.JobMutex.Unlock()

					// Broadcast failure via WebSocket
					async.BroadcastQuizUpdate(projectID, "failed", 0)
				}
			}()

			// Update job status
			async.JobMutex.Lock()
			jobStatus.Status = "processing"
			jobStatus.Progress = 5
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket
			async.BroadcastQuizUpdate(projectID, "processing", 5)

			// Query to get all units for the project
			var units []models.Unit
			if err := db.Where("project_id = ?", projectID).Find(&units).Error; err != nil {
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to fetch units"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastQuizUpdate(projectID, "failed", 0)
				return
			}

			// Update progress based on number of units
			totalUnits := len(units)
			if totalUnits == 0 {
				async.JobMutex.Lock()
				jobStatus.Status = "completed"
				jobStatus.Progress = 100
				jobStatus.Error = "No units found"
				async.JobMutex.Unlock()

				// Broadcast completion via WebSocket
				async.BroadcastQuizUpdate(projectID, "completed", 100)
				return
			}

			// Generate quizzes for each unit
			for i, unit := range units {
				// Delete existing quiz if it exists
				if err := db.Where("unit_id = ?", unit.Model.ID).Delete(&models.Quiz{}).Error; err != nil {
					log.Printf("Warning: Failed to delete existing quiz for unit %d: %v", unit.Model.ID, err)
					// Continue with generation even if delete fails
				}

				// Create new quiz
				quiz := models.Quiz{
					UnitID:       unit.Model.ID,
					Questions:    GenerateQuizContent(unit),
					MaxScore:     100,
					PassingScore: 70,
				}

				// Save quiz to database
				if err := db.Create(&quiz).Error; err != nil {
					log.Printf("Failed to save quiz for unit %d: %v", unit.Model.ID, err)
					continue
				}

				// Update progress
				progress := int((float64(i+1) / float64(totalUnits)) * 100)
				async.JobMutex.Lock()
				jobStatus.Progress = progress
				jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
				async.JobMutex.Unlock()

				// Broadcast progress via WebSocket every 10% or for the last unit
				if (i+1)%maximum(1, totalUnits/10) == 0 || i == totalUnits-1 {
					async.BroadcastQuizUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
				}
			}

			// Update job status to complete
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			async.JobMutex.Unlock()

			// Update project to mark quizzes as generated
			if err := db.Model(&models.Project{}).Where("id = ?", projectID).Update("quizzes_generated", true).Error; err != nil {
				log.Printf("Failed to update project quizzes_generated status: %v", err)
			}

			// Broadcast completion via WebSocket
			async.BroadcastQuizUpdate(projectID, "completed", 100)
		}()

		c.JSON(http.StatusOK, gin.H{
			"job_id": jobID,
			"status": "pending",
		})
	}
}

// Helper function for calculating progress intervals
func maximum(a, b int) int {
	if a > b {
		return a
	}
	return b
}
