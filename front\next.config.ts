import type { Configuration } from "webpack";

const nextConfig = {
  reactStrictMode: true,
  
  // Add transpilePackages to handle ESM modules
  transpilePackages: ['next-mdx-remote'],
  
  // Configuration for Turbopack to handle canvas dependency in react-pdf
  experimental: {
    turbo: {
      resolveAlias: {
        canvas: './empty-module.ts',
      },
    },
  },

  images: {
    domains: ['img.youtube.com', 'i.ytimg.com', 'i.imgur.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.b2bpic.net',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'i.ytimg.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'example.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        pathname: '/**',
      },
    ],
  },
  
  webpack: (config: Configuration, { isServer }: { isServer: boolean }) => {
    // Add a fallback for the canvas module in client-side builds
    if (!isServer) {
      if (!config.resolve) {
        config.resolve = {};
      }
      config.resolve.fallback = {
        ...(config.resolve.fallback || {}),
        canvas: false,
      };
    }
    
    // Add rule to handle the canvas.node binary file
    config.module = config.module || { rules: [] };
    config.module.rules = [
      ...(config.module.rules || []),
      {
        test: /node_modules[\\\/]canvas[\\\/]build[\\\/]Release[\\\/]canvas\.node$/,
        use: 'null-loader',
      }
    ];
    
    return config;
  },
};

export default nextConfig;