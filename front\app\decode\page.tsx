"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function DecodeRedirect() {
  const router = useRouter();

  useEffect(() => {
    // If accessed directly, redirect to dashboard page
    router.push('/studio');
  }, [router]);

  return (
    <div className="h-screen flex flex-col items-center justify-center gap-4" 
         style={{ 
           backgroundColor: 'var(--background)',
           color: 'var(--foreground)',
           borderColor: 'gray'
         }}>
      <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      <p className="text-xl font-medium text-gray-700 dark:text-gray-300">Preparing DecodeMed workspace...</p>
      <p className="text-sm text-gray-500 dark:text-gray-400">
        Redirecting you to your studio to select a project
      </p>
    </div>
  );
}
