package handlers

import (
	"context"
	"log"
	"net/http"
	"strconv"

	"decodemed/models"
	"decodemed/storage"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetMedSpaces returns all MedSpaces for a user
func GetMedSpaces(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		var medspaces []models.MedSpace
		if err := db.Where("user_id = ?", userID).Preload("Projects").Find(&medspaces).Error; err != nil {
			log.Printf("Error fetching MedSpaces: %v", err)
			c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch MedSpaces"})
			return
		}

		// Always return an array, even if empty
		c.JSON(http.StatusOK, gin.H{"medspaces": medspaces})
	}
}

// GetMedSpace returns a specific MedSpace with its projects
func GetMedSpace(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		medspaceID := c.Param("id")
		userID, _ := c.Get("user_id")

		var medspace models.MedSpace
		if err := db.Where("id = ? AND user_id = ?", medspaceID, userID).
			Preload("Projects").
			Preload("Projects.Units").
			First(&medspace).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "MedSpace not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"medspace": medspace})
	}
}

// CreateMedSpace creates a new MedSpace
func CreateMedSpace(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Name        string `json:"name" binding:"required"`
			Description string `json:"description"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "User ID not found"})
			return
		}

		// Convert userID to uint safely
		var userIDUint uint
		switch v := userID.(type) {
		case uint:
			userIDUint = v
		case int:
			userIDUint = uint(v)
		case string:
			id, err := strconv.ParseUint(v, 10, 32)
			if err != nil {
				log.Printf("Error converting user ID string to uint: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
				return
			}
			userIDUint = uint(id)
		default:
			log.Printf("Error: user_id is of unexpected type: %T", userID)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID type"})
			return
		}

		medspace := models.MedSpace{
			Name:        req.Name,
			UserID:      userIDUint,
			Description: req.Description,
		}

		if err := db.Create(&medspace).Error; err != nil {
			log.Printf("Error creating MedSpace: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create MedSpace"})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"medspace": medspace})
	}
}

// UpdateMedSpace updates a MedSpace
func UpdateMedSpace(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		medspaceID := c.Param("id")
		userID, _ := c.Get("user_id")

		var req struct {
			Name        string `json:"name"`
			Description string `json:"description"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		var medspace models.MedSpace
		if err := db.Where("id = ? AND user_id = ?", medspaceID, userID).First(&medspace).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "MedSpace not found"})
			return
		}

		// Update fields if provided
		if req.Name != "" {
			medspace.Name = req.Name
		}
		if req.Description != "" {
			medspace.Description = req.Description
		}

		if err := db.Save(&medspace).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update MedSpace"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"medspace": medspace})
	}
}

// DeleteMedSpace deletes a MedSpace
func DeleteMedSpace(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		medspaceID := c.Param("id")
		userID, _ := c.Get("user_id")

		var medspace models.MedSpace
		if err := db.Where("id = ? AND user_id = ?", medspaceID, userID).First(&medspace).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "MedSpace not found"})
			return
		}

		log.Printf("Starting deletion of MedSpace ID %d", medspace.ID)

		// Get all projects in this MedSpace
		var projects []models.Project
		if err := db.Where("med_space_id = ?", medspaceID).Find(&projects).Error; err != nil {
			log.Printf("Error fetching projects for MedSpace deletion: %v", err)
		}

		log.Printf("Found %d projects to delete for MedSpace %d", len(projects), medspace.ID)

		// Collect all project IDs
		var projectIDs []uint
		for _, p := range projects {
			projectIDs = append(projectIDs, p.ID)
		}

		// Debug: Count flashcards before deletion
		if len(projectIDs) > 0 {
			var flashcardCount int64
			countSQL := `SELECT COUNT(*) FROM flashcards WHERE unit_id IN (SELECT id FROM units WHERE project_id IN ?)`
			if err := db.Raw(countSQL, projectIDs).Count(&flashcardCount).Error; err != nil {
				log.Printf("Error counting flashcards for MedSpace %d: %v", medspace.ID, err)
			} else {
				log.Printf("Found %d flashcards to delete for MedSpace %d", flashcardCount, medspace.ID)
			}
		}

		// Initialize storage client for deleting files
		ctx := context.Background()
		storageClient, err := storage.GetStorage(ctx)
		if err != nil {
			log.Printf("Failed to initialize storage client: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize storage client"})
			return
		}
		defer storageClient.Close()

		// Begin a transaction
		tx := db.Begin()
		if tx.Error != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to begin transaction"})
			return
		}

		if len(projectIDs) > 0 {
			// Get all unit IDs for these projects
			var unitIDs []uint
			if err := tx.Model(&models.Unit{}).Where("project_id IN ?", projectIDs).Pluck("id", &unitIDs).Error; err != nil {
				log.Printf("Error finding unit IDs: %v", err)
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find units"})
				return
			}

			log.Printf("Found %d units to delete for MedSpace %d: %v", len(unitIDs), medspace.ID, unitIDs)

			if len(unitIDs) > 0 {
				// Get all flashcard IDs for these units (for debugging)
				var flashcardIDs []uint
				if err := tx.Model(&models.Flashcard{}).Where("unit_id IN ?", unitIDs).Pluck("id", &flashcardIDs).Error; err != nil {
					log.Printf("Error finding flashcard IDs: %v", err)
				} else {
					log.Printf("Found %d flashcard IDs to delete: %v", len(flashcardIDs), flashcardIDs)
				}

				// Delete flashcard reviews first
				reviewResult := tx.Exec("DELETE FROM flashcard_reviews WHERE flashcard_id IN (SELECT id FROM flashcards WHERE unit_id IN ?)", unitIDs)
				if reviewResult.Error != nil {
					log.Printf("Error deleting flashcard reviews: %v", reviewResult.Error)
					tx.Rollback()
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete flashcard reviews"})
					return
				}
				log.Printf("Deleted %d flashcard reviews", reviewResult.RowsAffected)

				// Delete flashcards
				cardResult := tx.Exec("DELETE FROM flashcards WHERE unit_id IN ?", unitIDs)
				if cardResult.Error != nil {
					log.Printf("Error deleting flashcards: %v", cardResult.Error)
					tx.Rollback()
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete flashcards"})
					return
				}
				log.Printf("Deleted %d flashcards", cardResult.RowsAffected)

				// Also use direct model deletion as a backup
				if err := tx.Where("unit_id IN ?", unitIDs).Delete(&models.Flashcard{}).Error; err != nil {
					log.Printf("Warning: Backup flashcard deletion had error: %v", err)
				}

				// Delete mindmaps and quizzes
				if err := tx.Where("unit_id IN ?", unitIDs).Delete(&models.Mindmap{}).Error; err != nil {
					log.Printf("Error deleting mindmaps: %v", err)
					tx.Rollback()
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete mindmaps"})
					return
				}

				if err := tx.Where("unit_id IN ?", unitIDs).Delete(&models.Quiz{}).Error; err != nil {
					log.Printf("Error deleting quizzes: %v", err)
					tx.Rollback()
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quizzes"})
					return
				}
			}

			// Delete all units for all projects in this MedSpace
			if err := tx.Where("project_id IN ?", projectIDs).Delete(&models.Unit{}).Error; err != nil {
				log.Printf("Error deleting units: %v", err)
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete units"})
				return
			}
			log.Printf("Deleted units for projects %v", projectIDs)
		}

		// Delete files from storage for each project
		for _, project := range projects {
			if project.FileURL != "" {
				objectName := storage.ExtractObjectNameFromURL(project.FileURL)
				if err := storageClient.DeleteFile(ctx, objectName); err != nil {
					log.Printf("Warning: Failed to delete file %s: %v", objectName, err)
				}
			}
		}

		// Delete all projects in the MedSpace
		if err := tx.Where("med_space_id = ?", medspaceID).Delete(&models.Project{}).Error; err != nil {
			log.Printf("Error deleting projects for MedSpace %d: %v", medspace.ID, err)
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete projects"})
			return
		}

		// Finally, delete the MedSpace
		if err := tx.Delete(&medspace).Error; err != nil {
			log.Printf("Error deleting MedSpace %d: %v", medspace.ID, err)
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete MedSpace"})
			return
		}

		// Commit the transaction
		if err := tx.Commit().Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
			return
		}

		log.Printf("Successfully deleted MedSpace %d and all related data", medspace.ID)
		c.JSON(http.StatusOK, gin.H{"message": "MedSpace deleted successfully"})
	}
}

// AddProjectToMedSpace adds an existing project to a MedSpace
func AddProjectToMedSpace(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		medspaceID := c.Param("id")
		userID, _ := c.Get("user_id")

		var req struct {
			ProjectID uint `json:"project_id" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Verify MedSpace exists and belongs to user
		var medspace models.MedSpace
		if err := db.Where("id = ? AND user_id = ?", medspaceID, userID).First(&medspace).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "MedSpace not found"})
			return
		}

		// Verify Project exists and belongs to user
		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", req.ProjectID, userID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Convert medspaceID to uint
		msID, err := strconv.ParseUint(medspaceID, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid MedSpace ID"})
			return
		}
		msIDUint := uint(msID)

		// Update project to belong to MedSpace
		project.MedSpaceID = &msIDUint
		if err := db.Save(&project).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add project to MedSpace"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Project added to MedSpace successfully"})
	}
}

// RemoveProjectFromMedSpace removes a project from a MedSpace
func RemoveProjectFromMedSpace(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		medspaceID := c.Param("id")
		projectID := c.Param("project_id")
		userID, _ := c.Get("user_id")

		// Verify MedSpace exists and belongs to user
		var medspace models.MedSpace
		if err := db.Where("id = ? AND user_id = ?", medspaceID, userID).First(&medspace).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "MedSpace not found"})
			return
		}

		// Verify Project exists, belongs to user, and is in the specified MedSpace
		var project models.Project
		if err := db.Where("id = ? AND user_id = ? AND med_space_id = ?", projectID, userID, medspaceID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found in MedSpace"})
			return
		}

		// Remove project from MedSpace by setting MedSpaceID to nil
		project.MedSpaceID = nil
		if err := db.Save(&project).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove project from MedSpace"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Project removed from MedSpace successfully"})
	}
}
