export const API_URL = process.env.NEXT_PUBLIC_EVIDENCE_API_URL;


export const getWebSocketUrl = (endpoint: string) => {
    const isProduction = process.env.ENVIRONMENT_EVIDENCE === 'production';
    const apiUrl = process.env.NEXT_PUBLIC_EVIDENCE_API_URL || '';
    
    // Remove http(s):// from API URL if present
    const domain = apiUrl.replace(/^https?:\/\//, '');
    
    // For production URLs, always use wss://
    // For local development, use ws:// unless the API URL explicitly uses https
    const protocol = isProduction || apiUrl.startsWith('https') ? 'wss' : 'ws';
    
    return `${protocol}://${domain}${endpoint}`;
};