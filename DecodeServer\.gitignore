# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
decodemed.exe

# Test binary, built with `go test -c`
*.test
*.test.out

# Output of the go coverage tool
*.out
*.prof
coverage.txt

# Go workspace file
go.work

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
!.env.example

# Dependency directories
vendor/
go.sum

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Log files
*.log
logs/
log/
*.log.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# Build output
bin/
/dist/
build/
out/

# Debug files
__debug_bin
debug
debug.test

# Go specific
*.o
*.a
*.lib
*.test
*.out
go.work

# Air live reload
tmp/

# Docker
.docker/
docker-compose.override.yml

# Kubernetes
kubeconfig
*.kubeconfig

# SSL/TLS certificates
*.pem
*.key
*.crt
*.csr

# Backup files
*.bak
*.backup
*~

# Local development
local/
dev/
