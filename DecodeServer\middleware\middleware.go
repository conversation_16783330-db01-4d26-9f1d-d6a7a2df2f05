package middleware

import (
	"decodemed/config"
	"log"
	"net/http"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"
)

// ErrorHandler is a middleware that recovers from panics and returns a 500 response
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Log the error with more details
				log.Printf("Panic recovered in %s %s: %v", c.Request.Method, c.Request.URL.Path, err)

				// Print stack trace for better debugging
				debug.PrintStack()

				// Return a 500 error
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal Server Error",
				})
			}
		}()
		c.Next()
	}
}

// AuthErrorHandler is a middleware that catches auth errors and adds CORS headers
func AuthErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// If there was an error and it's a 401 or 403, add CORS headers
		if c.Writer.Status() == http.StatusUnauthorized || c.Writer.Status() == http.StatusForbidden {
			log.Printf("Auth error: %d", c.Writer.Status())
		}
	}
}

// CORSMiddleware handles CORS headers for all requests
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		cfg, err := config.GetInstance()

		// Allow configured origins in production, or all in development
		if err == nil && len(cfg.AllowedOrigins) > 0 && !cfg.IsDevelopment() {
			// Check if the origin is in the allowed list
			allowed := false
			for _, allowedOrigin := range cfg.AllowedOrigins {
				if allowedOrigin == "*" || allowedOrigin == origin {
					allowed = true
					break
				}

				// Handle wildcard subdomains like *.example.com
				if strings.HasPrefix(allowedOrigin, "*.") {
					domain := strings.TrimPrefix(allowedOrigin, "*")
					if strings.HasSuffix(origin, domain) {
						allowed = true
						break
					}
				}
			}

			if allowed {
				c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
			}
		} else if origin != "" {
			// In development or if no allowed origins configured, allow the requesting origin
			c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		} else {
			// Fallback for no origin header
			c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		}

		// Essential CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS, HEAD")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Authorization, Content-Type, Content-Length, Accept, Accept-Encoding, X-CSRF-Token, X-User-ID, Origin, Cache-Control, Pragma")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type, Authorization, Location")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight OPTIONS requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK) // Return 200 instead of 204
			return
		}

		// Handle redirects - ensure CORS headers are preserved
		c.Writer.Header().Set("Vary", "Origin")

		c.Next()
	}
}
