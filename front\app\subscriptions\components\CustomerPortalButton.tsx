'use client';

import { useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Loader2, ExternalLink } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';
import { createCustomerPortalSession } from '@/app/utils/subscription-api';

interface CustomerPortalButtonProps {
  className?: string;
  fullWidth?: boolean;
}

export default function CustomerPortalButton({ 
  className = '',
  fullWidth = false 
}: CustomerPortalButtonProps) {
  const { getToken } = useAuth();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);

  const handleOpenPortal = async () => {
    try {
      setLoading(true);
      const { url } = await createCustomerPortalSession(getToken);
      
      // Redirect to the Stripe Customer Portal
      window.location.href = url;
    } catch (error) {
      console.error('Error opening customer portal:', error);
      alert('Failed to open customer portal. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleOpenPortal}
      disabled={loading}
      className={`${className} ${fullWidth ? 'w-full' : ''} inline-flex items-center justify-center rounded-md py-2 px-4 transition-colors ${
        theme === 'dark' 
          ? 'bg-blue-700 text-white hover:bg-blue-600' 
          : 'bg-blue-600 text-white hover:bg-blue-700'
      } disabled:opacity-50 disabled:cursor-not-allowed`}
    >
      {loading ? (
        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
      ) : (
        <ExternalLink className="w-5 h-5 mr-2" />
      )}
      Manage Billing
    </button>
  );
} 