package main

import (
	"os"
	"os/exec"
	"path/filepath"
	"testing"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
)

// TestPDFExtraction tests the core PDF extraction functionality that's failing
func TestPDFExtraction(t *testing.T) {
	// Use the test PDF we created
	pdfPath := "testdata/sample.pdf"
	if _, err := os.Stat(pdfPath); os.IsNotExist(err) {
		t.Fatalf("Test PDF file not found at %s. Run create_test_pdf.go first.", pdfPath)
	}

	// Create a temp directory for extraction
	tempDir, err := os.MkdirTemp("", "pdf-extract-test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Get total page count
	pageCount, err := api.PageCountFile(pdfPath)
	if err != nil {
		t.Fatalf("Failed to get page count: %v", err)
	}
	t.Logf("PDF has %d pages", pageCount)

	// Test case 1: Try to extract images from the PDF
	t.Run("ExtractImagesFromPDF", func(t *testing.T) {
		// Configure pdfcpu
		conf := model.NewDefaultConfiguration()

		// Extract images from the PDF (first page only)
		selectedPages := []string{"1"}
		extractDir := filepath.Join(tempDir, "images")
		if err := os.MkdirAll(extractDir, 0755); err != nil {
			t.Fatalf("Failed to create extract directory: %v", err)
		}

		err := api.ExtractImagesFile(pdfPath, extractDir, selectedPages, conf)
		if err != nil {
			t.Errorf("Failed to extract images: %v", err)
		}

		// Log the extraction directory contents
		files, err := os.ReadDir(extractDir)
		if err != nil {
			t.Errorf("Failed to read extract directory: %v", err)
		} else {
			t.Logf("Found %d files in extract directory", len(files))
			for _, file := range files {
				t.Logf("File: %s", file.Name())
			}

			if len(files) == 0 {
				t.Logf("No image files extracted. This PDF might not contain embedded images.")
				t.Logf("This is the root cause of your thumbnail generation error.")
			}
		}
	})

	// Test case 2: Check if the PDF actually has any images
	t.Run("CheckPDFImages", func(t *testing.T) {
		// This is a direct call to pdfcpu to list any images in the PDF
		cmd := exec.Command("pdfcpu", "list", "images", pdfPath)
		output, err := cmd.CombinedOutput()
		if err != nil {
			t.Logf("pdfcpu command failed: %v", err)
			t.Logf("This may be expected if pdfcpu is not installed or the PDF has no images")
		} else {
			t.Logf("pdfcpu output: %s", output)
		}
	})

	// Test case 3: Try alternative extraction methods
	t.Run("AlternativeExtractionMethods", func(t *testing.T) {
		// Alternative 1: Try to extract the page as PDF
		pageDir := filepath.Join(tempDir, "pages")
		if err := os.MkdirAll(pageDir, 0755); err != nil {
			t.Fatalf("Failed to create page directory: %v", err)
		}

		conf := model.NewDefaultConfiguration()
		err := api.ExtractPagesFile(pdfPath, pageDir, []string{"1"}, conf)
		if err != nil {
			t.Logf("Failed to extract page as PDF: %v", err)
		} else {
			pageFiles, _ := os.ReadDir(pageDir)
			t.Logf("Extracted %d page files", len(pageFiles))
		}

		// Alternative 2: Try ImageMagick convert, which is what your code falls back to
		imgDir := filepath.Join(tempDir, "imagemagick")
		if err := os.MkdirAll(imgDir, 0755); err != nil {
			t.Fatalf("Failed to create imagemagick directory: %v", err)
		}

		imgPath := filepath.Join(imgDir, "thumbnail.png")
		cmd := exec.Command("convert", pdfPath+"[0]", "-density", "300", "-resize", "1800x>", imgPath)
		err = cmd.Run()
		if err != nil {
			t.Logf("ImageMagick convert failed: %v", err)
			t.Logf("This suggests neither pdfcpu nor ImageMagick can process this PDF")
		} else {
			t.Logf("ImageMagick successfully created thumbnail at %s", imgPath)
			if _, err := os.Stat(imgPath); err == nil {
				t.Logf("ImageMagick thumbnail exists and can be used as fallback")
			}
		}
	})

	// Print the final conclusion
	t.Log("===== DIAGNOSIS =====")
	t.Log("If no images were extracted by pdfcpu but ImageMagick worked, update your code to rely on ImageMagick.")
	t.Log("If neither worked, your PDF might be in a format that these tools can't process.")
}
