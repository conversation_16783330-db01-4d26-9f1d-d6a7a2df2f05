'use client'
import React from 'react'
import { <PERSON>Header } from '@/containers/main/hero5-header'

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white items-center text-center ">
      <HeroHeader />
      {/* Hero Section */}
      <section className="pt-40 pb-20 bg-gray-100">
        <div className="container mx-auto px-6 relative text-center">
          <div className="absolute bg-transparent"></div>
          <div className="relative z-10">
            <h1 className="text-4xl md:text-6xl font-bold text-[#091225]">
              Revolutionizing Biomedical Research with AI
            </h1>
            <p className="text-xl text-gray-500 max-w-2xl mx-auto">
              Answering biomedical research questions with a search engine powerd by artificial intelligence. 

            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 px-6 bg-[#091225] text-center ">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col items-center justify-center gap-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-100 mb-6">Our Mission</h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                DecodeMed is a biomedical search engine powered by AI that is dedicated to connect biomedical researchers, physicians, and healthcare professionals
                with the most relevant biomedical literature  using state-of-the-art artificial intelligence.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <div className="flex flex-col md:flex-row items-center space-y-8 space-x-4 md:space-y-0 md:space-x-24 justify-center">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">40M+</div>
              <div className="text-gray-600">Research Papers</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">AI-powered search</div>
              <div className="text-gray-600">Improves Search Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">Save time </div>
              <div className="text-gray-600">Answer biomedical research questions</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="bg-gray-50 py-16 text-center">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Get in Touch</h2>
          <p className="text-gray-600 mb-8">
            Interested in learning more about how we can help accelerate your research?
          </p>
          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            Contact Us
          </button>
        </div>
      </section>
    </div>
  )
}