// utils/config.ts


export const API_URL = process.env.NEXT_PUBLIC_DECODE_API_URL;


export const getWebSocketUrl = (endpoint: string) => {
    const isProduction = process.env.ENVIRONMENT_DECODE === 'production';
    const apiUrl = process.env.NEXT_PUBLIC_DECODE_API_URL || '';
    
    // Remove http(s):// from API URL if present
    const domain = apiUrl.replace(/^https?:\/\//, '');
    
    // Use wss for production/https, ws for development/http
    const protocol = isProduction ? 'wss' : 'ws';
    
    return `${protocol}://${domain}${endpoint}`;
  };
 