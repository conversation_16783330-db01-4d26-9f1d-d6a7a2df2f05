from django.shortcuts import render
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import stripe
import os
from subscriptions.models import UserSubscription, SubscriptionUsage
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from datetime import datetime
from subscriptions.middleware import AuthenticationMiddleware
import jwt
from channels.db import database_sync_to_async
from django.utils.decorators import method_decorator
import asyncio
from functools import partial
from asgiref.sync import sync_to_async
import json
from stripe import StripeClient

# Initialize Stripe with both the global API key and the client
stripe.api_key = os.getenv('STRIPE_SECRET_KEY')  # Set global API key
stripe_client = StripeClient(os.getenv('STRIPE_SECRET_KEY'))

# Add a debug print to verify the API key is being loaded
if not stripe.api_key:
    print("Warning: STRIPE_SECRET_KEY not found in environment variables")

class AsyncAPIView(APIView):
    """Base class for async API views"""
    def get_headers(self):
        """Initialize headers for the response"""
        return {}

    async def dispatch(self, request, *args, **kwargs):
        """Override dispatch to handle async responses"""
        self.headers = self.get_headers()  # Initialize headers
        
        try:
            handler = getattr(self, request.method.lower(), self.http_method_not_allowed)
            if asyncio.iscoroutinefunction(handler):
                response = await handler(request, *args, **kwargs)
            else:
                response = handler(request, *args, **kwargs)
            
            if asyncio.iscoroutine(response):
                response = await response

            return self.finalize_response(request, response, *args, **kwargs)
        except Exception as exc:
            response = self.handle_exception(exc)
            return self.finalize_response(request, response, *args, **kwargs)

# Create your views here.

# CREATE CHECKOUT SESSION
@method_decorator(csrf_exempt, name='dispatch')
class CreateCheckoutSession(AsyncAPIView):
    async def post(self, request):
        try:
            # Get authorization header
            auth_header = request.headers.get('Authorization', '')
            if not auth_header:
                return Response({'error': 'No authorization token provided'}, status=status.HTTP_401_UNAUTHORIZED)

            # Parse request body - Fix for the await error
            try:
                data = json.loads(request.body.decode('utf-8'))
            except json.JSONDecodeError:
                return Response({
                    'error': 'Invalid request format',
                    'message': 'Request body must be valid JSON'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate required fields
            subscription_type = data.get('subscriptionType')
            success_url = data.get('successUrl')
            cancel_url = data.get('cancelUrl')

            if not all([subscription_type, success_url, cancel_url]):
                return Response({
                    'error': 'Missing required fields',
                    'details': 'subscriptionType, successUrl, and cancelUrl are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Verify token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = await middleware.verify_token(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')
            
            if not user_id:
                return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

            # Get or create subscription
            get_or_create_sub = database_sync_to_async(
                lambda: UserSubscription.objects.get_or_create(user_id=user_id)
            )
            subscription, created = await get_or_create_sub()

            # Handle Stripe customer
            customer_id = subscription.stripe_customer_id
            if customer_id:
                try:
                    # Verify existing customer
                    stripe_customer_retrieve = sync_to_async(stripe.Customer.retrieve)
                    await stripe_customer_retrieve(customer_id)
                except stripe.error.InvalidRequestError:
                    customer_id = None
                    subscription.stripe_customer_id = None

            if not customer_id:
                # Create new customer using async method
                customer = await stripe_client.customers.create_async({
                    'metadata': {'user_id': user_id}
                })
                customer_id = customer.id
                subscription.stripe_customer_id = customer_id
                await database_sync_to_async(subscription.save)()

            # Set up metadata
            metadata = {
                'user_id': user_id,
                'subscription_type': subscription_type
            }

            # Get price ID based on subscription type
            price_id = os.getenv('STRIPE_MONTHLY_PRICE_ID') if subscription_type == 'monthly' else os.getenv('STRIPE_YEARLY_PRICE_ID')
            if not price_id:
                return Response({
                    'error': 'Configuration error',
                    'message': f'Price ID not found for {subscription_type} subscription'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Create Stripe checkout session using async method
            session = await stripe_client.checkout.sessions.create_async({
                'customer': customer_id,
                'success_url': success_url,
                'cancel_url': cancel_url,
                'payment_method_types': ['card'],
                'mode': 'subscription',
                'line_items': [{
                    'price': price_id,
                    'quantity': 1,
                }],
                'metadata': metadata,
                'subscription_data': {'metadata': metadata}
            })

            return Response({'sessionId': session.id})

        except stripe.error.StripeError as e:
            print(f"Stripe Error: {str(e)}")
            return Response({
                'error': 'Payment service error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print(f"Server Error: {str(e)}")
            return Response({
                'error': 'Server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# -------- CHECK SUBSCRIPTION STATUS -------- FREE TRIAL LIMITS
@method_decorator(csrf_exempt, name='dispatch')
class CheckSubscriptionStatus(AsyncAPIView):
    async def get(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = await middleware.verify_token(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')

            if not user_id:
                return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

            # Wrap database operations in sync_to_async
            get_subscription = sync_to_async(
                lambda: UserSubscription.objects.filter(user_id=user_id).first()
            )
            get_or_create_usage = sync_to_async(
                lambda: SubscriptionUsage.objects.get_or_create(user_id=user_id)[0]
            )

            subscription = await get_subscription()
            usage = await get_or_create_usage()

            if not subscription:
                return Response({
                    'subscription_type': 'none',
                    'status': 'inactive',
                    'current_period_end': None,
                    'will_cancel': False,
                    'provider': None,
                    'search': {'usage': 0, 'limit': 10},
                    'chat': {'usage': 0, 'limit': 10}
                })

            # Define limits based on subscription type
            search_limit = -1 if subscription.subscription_status == 'active' else 10
            chat_limit = -1 if subscription.subscription_status == 'active' else 10
           
            return Response({
                'subscription_type': subscription.subscription_type,
                'status': subscription.subscription_status,
                'current_period_end': subscription.subscription_end_date,
                'will_cancel': subscription.will_cancel,
                'provider': subscription.subscription_provider,
                'created_at': subscription.created_at,
                'updated_at': subscription.updated_at,
                'search': {
                    'usage': usage.search_count,
                    'limit': search_limit
                },
                'chat': {
                    'usage': usage.chat_count,
                    'limit': chat_limit
                }
            })
        except Exception as e:
            return Response({'error': str(e)}, status=400)

#-------------------CANCEL SUBSCRIPTION -------------------
@method_decorator(csrf_exempt, name='dispatch')
class CancelSubscription(AsyncAPIView):
    async def post(self, request):
        try:
            # Validate authorization
            auth_header = request.headers.get('Authorization', '')
            if not auth_header:
                return Response({'error': 'No authorization token provided'}, 
                             status=status.HTTP_401_UNAUTHORIZED)

            # Get user from token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = await middleware.verify_token(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')

            if not user_id:
                return Response({'error': 'Invalid token'}, 
                             status=status.HTTP_401_UNAUTHORIZED)

            # Get subscription using async wrapper
            get_subscription = database_sync_to_async(
                lambda: UserSubscription.objects.filter(user_id=user_id).first()
            )
            subscription = await get_subscription()

            if not subscription or not subscription.stripe_subscription_id:
                return Response({'error': 'No active subscription found'}, 
                             status=status.HTTP_400_BAD_REQUEST)

            try:
                # Update subscription with proper parameter structure
                stripe_subscription = await stripe_client.subscriptions.update_async(
                    subscription.stripe_subscription_id,
                    {'cancel_at_period_end': True}  # Wrap in dictionary
                )

                # Update subscription in database
                subscription.will_cancel = True
                subscription.subscription_status = 'active'  # Stays active until period end
                subscription.subscription_end_date = datetime.fromtimestamp(
                    stripe_subscription.current_period_end
                )
                await database_sync_to_async(subscription.save)()

                return Response({
                    'message': 'Subscription scheduled to cancel at period end',
                    'end_date': subscription.subscription_end_date
                })

            except stripe.error.StripeError as e:
                print(f"Stripe Error during cancellation: {str(e)}")
                if 'already been canceled' in str(e):
                    subscription.will_cancel = True
                    subscription.subscription_status = 'cancelled'
                    await database_sync_to_async(subscription.save)()
                    return Response({
                        'message': 'Subscription was already canceled',
                        'end_date': subscription.subscription_end_date
                    })
                raise

        except Exception as e:
            print(f"Stripe Error: {str(e)}")
            return Response({
                'error': 'Payment service error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print(f"Server Error: {str(e)}")
            return Response({
                'error': 'Server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#-------------------REACTIVATE SUBSCRIPTION -------------------
@method_decorator(csrf_exempt, name='dispatch')
class ReactivateSubscription(AsyncAPIView):
    async def post(self, request):
        try:
            # Validate authorization
            auth_header = request.headers.get('Authorization', '')
            if not auth_header:
                return Response({'error': 'No authorization token provided'}, 
                             status=status.HTTP_401_UNAUTHORIZED)

            # Get user from token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = await middleware.verify_token(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')

            if not user_id:
                return Response({'error': 'Invalid token'}, 
                             status=status.HTTP_401_UNAUTHORIZED)

            # Get subscription using async wrapper
            get_subscription = database_sync_to_async(
                lambda: UserSubscription.objects.filter(user_id=user_id).first()
            )
            subscription = await get_subscription()

            if not subscription or not subscription.stripe_subscription_id:
                return Response({'error': 'No subscription found to reactivate'}, 
                             status=status.HTTP_400_BAD_REQUEST)

            try:
                # Use update_async instead of modify_async
                stripe_subscription = await stripe_client.subscriptions.update_async(
                    subscription.stripe_subscription_id,
                    {'cancel_at_period_end': False}  # Wrap in dictionary
                )

                # Update subscription in database
                subscription.will_cancel = False
                subscription.subscription_status = 'active'
                subscription.subscription_end_date = datetime.fromtimestamp(
                    stripe_subscription.current_period_end
                )
                await database_sync_to_async(subscription.save)()

                return Response({
                    'message': 'Subscription successfully reactivated',
                    'status': subscription.subscription_status,
                    'current_period_end': subscription.subscription_end_date
                })

            except stripe.error.StripeError as e:
                print(f"Stripe Error during reactivation: {str(e)}")
                return Response({
                    'error': 'Payment service error',
                    'message': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"Server Error: {str(e)}")
            return Response({
                'error': 'Server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)