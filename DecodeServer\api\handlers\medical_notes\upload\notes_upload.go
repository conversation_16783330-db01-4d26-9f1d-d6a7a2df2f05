package upload

import (
	"decodemed/models"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const uploadsDir = "uploads"

// UploadMedicalNoteHandler handles the POST request to upload a medical note
func UploadMedicalNoteHandler(c *gin.Context) {
	// Get database from context
	db, exists := c.Get("db")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database connection not found"})
		return
	}
	gormDB := db.(*gorm.DB)

	// Parse multipart form with 32MB max memory
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Error parsing form: " + err.Error()})
		return
	}

	// Get the JSON data from the form
	noteJson := c.PostForm("noteJson")
	if noteJson == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Missing noteJson in form data"})
		return
	}

	var note models.MedicalNote
	if err := json.Unmarshal([]byte(noteJson), &note); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Error parsing JSON: " + err.Error()})
		return
	}

	// Generate a unique ID if not provided
	if note.ID == "" {
		note.ID = uuid.New().String()
	}

	// Create uploads directory if it doesn't exist
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating uploads directory: " + err.Error()})
		return
	}

	// Handle file uploads
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Error getting multipart form: " + err.Error()})
		return
	}

	for key, files := range form.File {
		if len(files) > 0 {
			file := files[0] // Take the first file if multiple are uploaded

			// Open the uploaded file
			src, err := file.Open()
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Error opening uploaded file: " + err.Error()})
				return
			}
			defer src.Close()

			// Create the destination file
			filePath := filepath.Join(uploadsDir, note.ID, file.Filename)
			if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating directory for file: " + err.Error()})
				return
			}

			dst, err := os.Create(filePath)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating destination file: " + err.Error()})
				return
			}
			defer dst.Close()

			// Copy the file contents
			if _, err = io.Copy(dst, src); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Error saving file: " + err.Error()})
				return
			}

			// Update the note's file paths based on the field name
			updateNoteFilePaths(&note, key, filePath)
		}
	}

	// Save the note to the database
	if err := gormDB.Create(&note).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error saving note to database: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"id":      note.ID,
		"message": "Note uploaded successfully",
	})
}

// updateNoteFilePaths updates the note's file paths based on the field name
func updateNoteFilePaths(note *models.MedicalNote, fieldName string, filePath string) {
	// This is a simplified version - you'll need to implement the actual path mapping
	// based on your note structure and field naming convention
	// Example: sections.etiology.MEDIA.simulate.segmentations_file -> note.Sections.Etiology.MEDIA.Simulate.Segmentations
	// You'll need to parse the fieldName and update the appropriate field in the note structure
	fmt.Printf("Updating file path for field %s: %s\n", fieldName, filePath)
}
