# Generated by Django 5.1.4 on 2024-12-29 00:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0007_remove_usersubscription_platform_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='usersubscription',
            old_name='subscription_id',
            new_name='revenuecat_product_id',
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='revenuecat_user_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='stripe_subscription_id',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='subscription_provider',
            field=models.CharField(choices=[('stripe', 'Stripe'), ('revenuecat', 'RevenueCat')], default='stripe', max_length=50),
        ),
        migrations.Alter<PERSON>ield(
            model_name='usersubscription',
            name='subscription_status',
            field=models.Char<PERSON>ield(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('past_due', 'Past Due'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='inactive', max_length=50),
        ),
    ]
