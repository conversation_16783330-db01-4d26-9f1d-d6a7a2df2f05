import asyncio
import aiohttp
import xml.etree.ElementTree as ET
from dotenv import load_dotenv
import os

load_dotenv()
API_KEY = os.getenv('PUBMED_API_KEY')


async def fetch_pubmed_articles(query):
    base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"
    query_with_date = f"{query} AND 2015:2024[PDAT]"
    params = {
        'db': 'pubmed',
        'term': query_with_date,
        'retmax': 100,
        'sort': 'relevance',
        'retmode': 'xml',
        'rettype': 'abstract',
        'api_key': API_KEY
    }

    async with aiohttp.ClientSession() as session:
        async with session.get(f"{base_url}esearch.fcgi", params=params) as response:
            if response.status != 200:
                return None, f'Failed to search PubMed: Status {response.status}'
            search_data = await response.text()

        root = ET.fromstring(search_data)
        id_list = [id_elem.text for id_elem in root.findall('.//Id')]

        if not id_list:
            return None, 'No articles found'

        fetch_params = {
            'db': 'pubmed',
            'id': ','.join(id_list),
            'retmax': 100,
            'retmode': 'xml',
            'rettype': 'abstract',
            'api_key': API_KEY
        }
        
        async with session.get(f"{base_url}efetch.fcgi", params=fetch_params) as response:
            if response.status != 200:
                return None, f'Failed to fetch PubMed: Status {response.status}'
            fetch_data = await response.text()

        return fetch_data, None

async def parse_articles(fetch_data):
    articles_data = []
    try:
        root = ET.fromstring(fetch_data)
        for pubmed_article in root.findall('.//PubmedArticle'):
            article = pubmed_article.find('.//Article')
            pmid = pubmed_article.find('.//PMID').text
            
            title = article.find('.//ArticleTitle').text if article.find('.//ArticleTitle') is not None else 'No title available'
            abstract = article.find('.//AbstractText').text if article.find('.//AbstractText') is not None else 'No abstract available'
            
            authors = []
            for author in article.findall('.//Author'):
                last_name = author.find('LastName').text if author.find('LastName') is not None else ''
                fore_name = author.find('ForeName').text if author.find('ForeName') is not None else ''
                if last_name or fore_name:
                    authors.append(f"{last_name} {fore_name}".strip())
                else:
                    authors.append('Unknown Author')
            
            journal = article.find('.//Journal')
            publisher = journal.find('.//Title').text if journal.find('.//Title') is not None else 'No publisher available'
            
            pub_date = journal.find('.//PubDate')
            publication_date = pub_date.find('Year').text if pub_date.find('Year') is not None else 'No publication date available'
            
            doi = ''
            for id_elem in pubmed_article.findall('.//ArticleId'):
                if id_elem.get('IdType') == 'doi':
                    doi = id_elem.text
                    break
            
            articles_data.append({
                'pmid': pmid,
                'title': title,
                'authors': authors,
                'abstract': abstract,
                'publisher': publisher,
                'publication_date': publication_date,
                'doi': doi,
                'article_link': f"https://doi.org/{doi}" if doi else f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/"
            })
    except ET.ParseError:
        pass
    except Exception:
        pass
    
    return articles_data



