"use client";

import React, { useState } from "react";
import FlashcardReview from './flashcard_components/FlashcardReview';
import FlashcardStatsComponent from './flashcard_components/FlashcardStats';

// Shared API utilities and interfaces

// Response object for API responses that may contain flashcards
export interface FlashcardResponse {
  flashcards?: FlashcardModel[];
  data?: FlashcardModel[];
  cards?: FlashcardModel[];
  items?: FlashcardModel[];
  results?: FlashcardModel[];
  message?: string;
}

// Match the backend flashcard model
export interface FlashcardModel {
  id?: number;       // Using optional to allow for different formats
  ID?: number;       // Supporting both camelCase and snake_case naming
  question: string;
  answer: string;
  difficulty_rank?: number;
  difficultyRank?: number;
  tags?: string[];
}

// Stats returned by the backend
export interface FlashcardStats {
  total_cards: number;
  cards_due: number;
  cards_learned: number;
  average_difficulty: number;
  review_accuracy: number;
  streak: number;
}

// Input for flashcard generation
export interface FlashcardGenerateInput {
  unit_id: number;
  type: string;  // 'document' or 'youtube'
}

// Input for flashcard review
export interface FlashcardReviewInput {
  grade: number; // 0-5 scale (0=forgot, 5=perfect)
  time?: Date;
}

// Response from flashcard review
export interface FlashcardReviewResponse {
  message: string;
  flashcard: FlashcardModel;
  next_review: string;
}

// Helper function to handle authentication errors with token refresh
export const fetchWithTokenRetry = async (url: string, options: RequestInit, getToken: () => Promise<string | null>, retryCount = 0): Promise<Response> => {
  // Max 3 retries
  if (retryCount > 3) {
    throw new Error('Max retry attempts reached');
  }
  
  // Get a fresh token on each retry
  const token = await getToken();
  if (!token) {
    throw new Error('Authentication token not available');
  }
  
  // Set the Authorization header with the fresh token
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    ...options.headers,
  };
  
  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
  });
  
  // If authentication error (401), retry with a fresh token
  if (response.status === 401 && retryCount < 3) {
    console.log(`Auth error: 401, retrying (attempt ${retryCount + 1}/3)`);
    return fetchWithTokenRetry(url, options, getToken, retryCount + 1);
  }
  
  return response;
};

interface FlashcardsProps {
  projectId: string;
}

function FlashcardsContent({ projectId }: FlashcardsProps) {
  const [activeTab, setActiveTab] = useState<'review' | 'stats'>('review');

  return (
    <div className="h-full w-full flex flex-col">
      {/* Tab Navigation */}
      <div className="bg-transparent w-full">
        <nav className="flex space-x-4 px-4">
          <button
            onClick={() => setActiveTab('review')}
            className={`py-1 px-2 text-sm font-medium rounded-2xl ${
              activeTab === 'review'
                ? 'text-foreground bg-muted'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            Review Cards
          </button>
          <button
            onClick={() => setActiveTab('stats')}
            className={`py-1 px-2 text-sm font-medium rounded-2xl ${
              activeTab === 'stats'
                ? 'text-foreground bg-muted'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            Statistics
          </button>
        </nav>
      </div>
      
      {/* Content Area */}
      <div className="flex-1 w-full overflow-auto p-4 bg-background">
        {activeTab === 'review' && <FlashcardReview projectId={projectId} />}
        {activeTab === 'stats' && <FlashcardStatsComponent projectId={projectId} />}
      </div>
    </div>
  );
}

export default function Page({ projectId }: FlashcardsProps) {
  return <FlashcardsContent projectId={projectId} />;
}
