from django.db import models

# Create your models here.

class UserSubscription(models.Model):
    SUBSCRIPTION_TYPES = [
        ('monthly', 'Monthly Subscription'),
        ('yearly', 'Yearly Subscription'),
        ('free trial', 'Free Trial')
    ]

    SUBSCRIPTION_STATUS = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('past_due', 'Past Due'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired')
    ]

    SUBSCRIPTION_PROVIDER = [
        ('stripe', 'Stripe'),
        ('revenuecat', 'RevenueCat')
    ]

    # User identification
    user_id = models.CharField(max_length=255)
    
    # Provider specific IDs
    stripe_customer_id = models.CharField(max_length=255, null=True, blank=True)
    stripe_subscription_id = models.CharField(max_length=255, null=True, blank=True)
    revenuecat_user_id = models.CharField(max_length=255, null=True, blank=True)
    revenuecat_product_id = models.CharField(max_length=255, null=True, blank=True)
    
    # Common subscription fields
    subscription_provider = models.CharField(max_length=50, choices=SUBSCRIPTION_PROVIDER, default='stripe')
    subscription_type = models.CharField(max_length=50, choices=SUBSCRIPTION_TYPES, default='free trial')
    subscription_status = models.CharField(max_length=50, choices=SUBSCRIPTION_STATUS, default='inactive')
    will_cancel = models.BooleanField(default=False)
    subscription_end_date = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_subscriptions'

    def is_active(self):
        return self.subscription_status == 'active'

class SubscriptionUsage(models.Model):
    user_id = models.CharField(max_length=255)
    search_count = models.IntegerField(default=0)
    chat_count = models.IntegerField(default=0)
    last_reset = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'subscription_usage'




