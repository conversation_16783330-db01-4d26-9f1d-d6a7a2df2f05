import unittest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from ..ai_models.articles_summary import generate_summary

class TestArticlesSummary(unittest.TestCase):
    def setUp(self):
        self.test_question = "What are the effects of aspirin?"
        self.test_articles = [
            {
                "title": "Aspirin for Primary Prevention",
                "abstract": "Aspirin reduces risk of cardiovascular events but increases bleeding risk."
            },
            {
                "title": "Pain Management with Aspirin",
                "abstract": "Aspirin effectively reduces pain and inflammation through COX inhibition."
            }
        ]
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        self.loop.close()


    def test_generate_summary_live(self):
        async def async_test():
            print("\n=== Live Article Summary Generation ===")
            print(f"Question: {self.test_question}")
            print("\nInput Articles:")
            for i, article in enumerate(self.test_articles, 1):
                print(f"{i}. {article['title']}\n   {article['abstract']}")

            print("\nGenerating Summary (streaming response):")
            summary = ''
            async for chunk in generate_summary(self.test_question, self.test_articles):
                print(chunk, end='', flush=True)
                summary += chunk
            
            print("\n\nComplete Summary:")
            print(summary)
            return summary

        return self.loop.run_until_complete(async_test())

if __name__ == '__main__':
    unittest.main(verbosity=2)