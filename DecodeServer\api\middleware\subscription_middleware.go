package middleware

import (
	"decodemed/models"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SubscriptionRequired checks if the user has an active subscription
// Use this middleware on routes that require a paid subscription
func SubscriptionRequired(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required",
			})
			c.Abort()
			return
		}

		// Find the user in the database
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to retrieve user information",
			})
			c.Abort()
			return
		}

		// Check if the user has an active subscription using the new model
		subscriptionStatus, err := models.GetSubscriptionStatusForUser(db, user.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to retrieve subscription information",
			})
			c.Abort()
			return
		}

		// Check if the subscription status is active or trialing
		if subscriptionStatus == "active" || subscriptionStatus == "trialing" {
			c.Next()
			return
		}

		// If no active subscription, check free projects count
		const FREE_PROJECTS_LIMIT = 10
		if user.FreeProjectsCount < FREE_PROJECTS_LIMIT {
			// Allow the request to proceed - we don't increment here
			// Project count will be incremented when a new project is created
			c.Next()
			return
		}

		// If we get here, user has exceeded free projects and has no subscription
		c.JSON(http.StatusPaymentRequired, gin.H{
			"error":               "Free projects limit exceeded. Please subscribe to continue.",
			"require_payment":     true,
			"subscription_status": subscriptionStatus,
			"free_projects_count": user.FreeProjectsCount,
			"free_projects_limit": FREE_PROJECTS_LIMIT,
		})
		c.Abort()
		return
	}
}

// CheckSubscription retrieves a user's subscription status without aborting the request
// Use this middleware to check subscription status but still allow the request to continue
func CheckSubscription(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get("user_id")
		if !exists {
			c.Set("subscription_status", "unauthenticated")
			c.Next()
			return
		}

		// Find the user in the database
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.Set("subscription_status", "unknown")
			c.Next()
			return
		}

		// Get the subscription status using the new model
		subscriptionStatus, err := models.GetSubscriptionStatusForUser(db, user.ID)
		if err != nil {
			c.Set("subscription_status", "unknown")
			c.Next()
			return
		}

		// Set the subscription status in the context
		c.Set("subscription_status", subscriptionStatus)

		// Add subscription info to context for use in handlers
		c.Set("has_active_subscription",
			subscriptionStatus == "active" ||
				subscriptionStatus == "trialing")

		// Continue with the request
		c.Next()
	}
}
