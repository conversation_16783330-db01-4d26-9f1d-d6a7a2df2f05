import os
from openai import Async<PERSON>penAI
from dotenv import load_dotenv
import asyncio
load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

if not api_key:
    raise ValueError("The OpenAI API key must be set in the environment variables.")

async def chat_query(question, chat_context):
    # Format the chat context
    formatted_context = "Previous interactions:\n"
    for i, entry in enumerate(chat_context, 1):
        formatted_context += f"{i}. Query: {entry['user']}\n   Summary: {entry['assistant']}\n\n"

    response = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": """You are an assistant that generates effective PubMed search queries. Follow these guidelines:
                
                
                - Return only the keywords, no other text.
                - Do not broaden the search beyond the scope of the question.
                - Return the keywords relevant to the disease and the topic.
                - Return up to 3 keywords with lower case
                - Use medical terms for PubMed search
                - Use AND operator for the keywords if necessary
                - Even for complex clinical cases or detailed scenarios questions, provide concise, relevant medical search terms to find the most relevant articles.
                - For a given clinical case, provide a concise, relevant medical search terms to find the most relevant articles that will support your answer once you solved the case.
                - For example, if your answer is anemia, provide search terms related to anemia.
                - For subsequent queries, consider the context of previous queries and summaries.
                - If no context is provided, generate a query based on the question only.
                """
            },
            {
                "role": "user",
                "content": f"""
                Context:
                {formatted_context}

                Generate a PubMed search query for this research question: {question}.
                - The new query should be able to maintain the conversation on the topic, taking into account the previous context.
                - The search query should be able to find the most relevant articles to the topic and the context.
                - Maintain the disease and topic of the previous queries and summaries with the new query
                - Generate up to 3 keywords with lower case.
                - Use medical terms for PubMed search
                - If no context is provided, generate a query based on the question only.
                """
            }
        ],
        temperature=0.1,
    )
    
    query = response.choices[0].message.content.strip()
    return query



