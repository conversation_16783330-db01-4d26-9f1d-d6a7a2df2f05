
#----web framework-----
aiohttp             
asyncio             
channels            
daphne              
django-cors-headers 
#rest api   
djangorestframework 
#websockets
websockets 

#----database and caching-----
redis
django-redis                      
channels-redis   
psycopg2 
#database url
dj-database-url

#----AI MODELS-----

openai      


#vector search
faiss-cpu  

#----PAYMENT-----
stripe      
#----UTILITIES-----
#env
python-dotenv                  

#static files
whitenoise

#-----AUTH-----
#Auth Middleware
httpx
clerk-backend-api
PyJWT
#pdf
PyPDF2
