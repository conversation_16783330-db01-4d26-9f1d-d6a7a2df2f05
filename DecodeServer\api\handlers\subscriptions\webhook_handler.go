package subscriptions

import (
	"decodemed/models"
	"encoding/json"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/subscription"
	"github.com/stripe/stripe-go/v76/webhook"
	"gorm.io/gorm"
)

// WebhookHandler processes Stripe webhook events
func WebhookHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Stripe webhook secret from environment
		endpointSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
		if endpointSecret == "" {
			log.Printf("Error: STRIPE_WEBHOOK_SECRET is not set")
			c.J<PERSON><PERSON>(http.StatusInternalServerError, gin.H{"error": "Webhook secret not configured"})
			return
		}

		// Read the request body
		payload, err := c.GetRawData()
		if err != nil {
			log.Printf("Error reading webhook body: %v", err)
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Error reading request body"})
			return
		}

		// Get the signature header
		signatureHeader := c.GetHeader("Stripe-Signature")
		if signatureHeader == "" {
			log.Printf("Error: No Stripe-Signature header")
			c.JSON(http.StatusBadRequest, gin.H{"error": "No signature header"})
			return
		}

		// Verify the event
		event, err := webhook.ConstructEvent(payload, signatureHeader, endpointSecret)
		if err != nil {
			log.Printf("Error verifying webhook signature: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature"})
			return
		}

		// Process the event based on type
		switch event.Type {
		case "checkout.session.completed":
			var session stripe.CheckoutSession
			err := json.Unmarshal(event.Data.Raw, &session)
			if err != nil {
				log.Printf("Error parsing checkout session: %v", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook payload"})
				return
			}

			// Process successful checkout
			if session.Mode == stripe.CheckoutSessionModeSubscription && session.Subscription != nil {
				// Get user ID from client reference ID
				userID, err := strconv.ParseUint(session.ClientReferenceID, 10, 64)
				if err != nil {
					log.Printf("Error parsing user ID from client reference: %v", err)
					c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client reference ID"})
					return
				}

				// Get subscription details
				sub, err := subscription.Get(session.Subscription.ID, nil)
				if err != nil {
					log.Printf("Error retrieving subscription: %v", err)
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Error retrieving subscription"})
					return
				}

				// Determine plan type
				planType := "unknown"
				if len(sub.Items.Data) > 0 {
					priceID := sub.Items.Data[0].Price.ID
					if priceID == os.Getenv("STRIPE_MONTHLY_PRICE_ID") {
						planType = "monthly"
					} else if priceID == os.Getenv("STRIPE_YEARLY_PRICE_ID") {
						planType = "yearly"
					}
				}

				// Create or update customer subscription record
				models.CreateOrUpdateSubscription(db, uint(userID), session.Customer.ID, sub.ID, string(sub.Status), planType, time.Unix(sub.CurrentPeriodEnd, 0), sub.CancelAtPeriodEnd)
			}

		case "customer.subscription.updated", "customer.subscription.deleted":
			var sub stripe.Subscription
			err := json.Unmarshal(event.Data.Raw, &sub)
			if err != nil {
				log.Printf("Error parsing subscription: %v", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook payload"})
				return
			}

			// Get customer ID
			customerID := sub.Customer.ID

			// Find the associated user through the combined table
			var customerSubscription models.StripeCustomerSubscription
			if err := db.Where("customer_id = ?", customerID).First(&customerSubscription).Error; err != nil {
				log.Printf("Could not find user for Stripe customer %s: %v", customerID, err)
				c.JSON(http.StatusOK, gin.H{"received": true}) // Return success to avoid retries
				return
			}

			// Determine plan type
			planType := "unknown"
			if len(sub.Items.Data) > 0 {
				priceID := sub.Items.Data[0].Price.ID
				if priceID == os.Getenv("STRIPE_MONTHLY_PRICE_ID") {
					planType = "monthly"
				} else if priceID == os.Getenv("STRIPE_YEARLY_PRICE_ID") {
					planType = "yearly"
				}
			}

			// Update the subscription status
			models.CreateOrUpdateSubscription(db, customerSubscription.UserID, customerID, sub.ID, string(sub.Status), planType, time.Unix(sub.CurrentPeriodEnd, 0), sub.CancelAtPeriodEnd)
		}

		// Return a success response to acknowledge receipt of the event
		c.JSON(http.StatusOK, gin.H{"received": true})
	}
}
