package storage

import (
	"context"
	"log"
	"mime/multipart"
	"os"
	"time"
)

// Storage is the interface that all storage providers must implement
type Storage interface {
	// UploadFile uploads a file to storage and returns its URL
	// File will be automatically converted to PDF if appropriate
	UploadFile(ctx context.Context, file *multipart.FileHeader, directory string) (string, error)

	// UploadFileFromPath uploads a file from a local path to storage
	// File will be automatically converted to PDF if appropriate
	UploadFileFromPath(ctx context.Context, filePath, directory string) (string, error)

	// GetSignedURL generates a signed URL for accessing a file for a limited time
	GetSignedURL(ctx context.Context, objectName string, expires time.Duration) (string, error)

	// DeleteFile deletes a file from storage
	DeleteFile(ctx context.Context, objectName string) error

	// DownloadFile downloads a file from storage to a local path
	DownloadFile(ctx context.Context, fileURL string, destPath string) error

	// GenerateThumbnail generates a thumbnail for a file and returns the thumbnail URL
	GenerateThumbnail(ctx context.Context, objectName string) (string, error)

	// GetThumbnailURL returns the URL of a thumbnail for a given file
	GetThumbnailURL(ctx context.Context, objectName string) (string, error)

	// ConvertToPDF explicitly converts a file to PDF without uploading
	ConvertToPDF(ctx context.Context, file *multipart.FileHeader) (string, error)

	// Close closes the storage client
	Close() error
}

var instance Storage

// GetStorage returns the storage instance
func GetStorage(ctx context.Context) (Storage, error) {
	if instance != nil {
		return instance, nil
	}

	// Get environment variables
	bucketName := os.Getenv("GCS_BUCKET_NAME")
	projectID := os.Getenv("GCS_PROJECT_ID")
	credentialsFile := os.Getenv("GCS_CREDENTIALS_FILE")

	// Create Google Cloud Storage client
	gcs, err := NewGoogleCloudStorage(ctx, bucketName, projectID, credentialsFile)
	if err != nil {
		log.Printf("Failed to initialize Google Cloud Storage: %v", err)
		return nil, err
	}

	instance = gcs
	return instance, nil
}
