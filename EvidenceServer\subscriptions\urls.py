from django.urls import path
from .stripe.stripe_views import (
    CreateCheckoutSession,
    CheckSubscriptionStatus,
    CancelSubscription,
    ReactivateSubscription,
    
)
from .stripe.stripe_webhook import stripe_webhook
from .revenue_cat.RevenueCat_views import RevenueCatSubscriptionStatus, VerifyRevenueCatReceipt
from .revenue_cat.RevenueCat_webhook import revenuecat_webhook

urlpatterns = [
    # Stripe endpoints
    path('create-checkout-session', CreateCheckoutSession.as_view()),
    path('webhook/', stripe_webhook, name='stripe_webhook'),
    path('status/', CheckSubscriptionStatus.as_view()),
    path('cancel', CancelSubscription.as_view(), name='cancel-subscription'),
    path('reactivate', ReactivateSubscription.as_view(), name='reactivate-subscription'),
    
    # RevenueCat endpoints
    path('revenuecat-webhook/', revenuecat_webhook, name='revenuecat_webhook'),
    path('revenuecat-status/', RevenueCatSubscriptionStatus.as_view(), name='revenuecat_status'),
    path('revenuecat-verify-receipt/', VerifyRevenueCatReceipt.as_view(), name='verify_receipt'),
]

