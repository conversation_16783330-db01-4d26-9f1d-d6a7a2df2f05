const translations = {
  en: {
    home: {
      title: "Biomedical AI search",
      description: "Evidence-based AI answers"
    },
    about: {
      title: "About Us",
      mission: "Our Mission"
    }
  },
  es: {
    home: {
      title: "Búsqueda biomédica con IA",
      description: "Respuestas de IA basadas en evidencia"
    },
    about: {
      title: "Sobre Nosotros",
      mission: "Nuestra Misión"
    }
  },
  pt: {
    home: {
      title: "Pesquisa biomédica com IA",
      description: "Respostas de IA baseadas em evidências"
    },
    about: {
      title: "Sobre Nós",
      mission: "Nossa Missão"
    }
  }
}

export const getTranslations = (locale: string) => {
  return Promise.resolve(translations[locale as keyof typeof translations] || translations.en)
} 