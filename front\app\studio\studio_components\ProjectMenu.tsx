import React, { useState } from 'react';
import { MoreH<PERSON><PERSON><PERSON>, Pen<PERSON>l, Trash2, FolderPlus } from 'lucide-react';
import { Project } from '../page';
import { MedSpace } from './MedSpacesSection';

interface ProjectActionsProps {
  project: Project;
  theme: string;
  projectWithOpenMenu: number | null;
  medSpaces: MedSpace[];
  handleProjectDropdownClick: (project: Project) => void;
  setEditingProjectId: (id: number | null) => void;
  setNewProjectName: (name: string) => void;
  setProjectWithOpenMenu: (id: number | null) => void;
  addProjectToMedSpace: (projectId: number, medSpaceId: number) => Promise<void>;
  generateYouTubeUnits?: (projectId: number) => Promise<void>;
  generateProjectUnits?: (projectId: number) => Promise<void>;
  handleProjectDelete: (projectId: number) => Promise<void>;
}

const ProjectMenu: React.FC<ProjectActionsProps> = ({
  project,
  theme,
  projectWithOpenMenu,
  medSpaces,
  handleProjectDropdownClick,
  setEditingProjectId,
  setNewProjectName,
  setProjectWithOpenMenu,
  addProjectToMedSpace,
  handleProjectDelete
}) => {
  const [showMedSpaceDropdown, setShowMedSpaceDropdown] = useState(false);

  return (
    <div className="absolute bottom-2 right-2 z-10">
      <button
        onClick={(e) => {
          e.stopPropagation();
          handleProjectDropdownClick(project);
        }}
        className={`p-1.5 rounded-full ${
          theme === 'dark' 
            ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' 
            : 'bg-white hover:bg-gray-100 text-gray-700'
        } shadow-md`}
        aria-label="Project options"
      >
        <MoreHorizontal size={20} />
      </button>

      {projectWithOpenMenu === project.id && (
        <div 
          className={`absolute right-0 bottom-full mb-1 w-48 rounded-md shadow-lg py-1 ${
            theme === 'dark' 
              ? 'bg-gray-800 border border-gray-700' 
              : 'bg-white border border-gray-200'
          } z-20`}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              setEditingProjectId(project.id);
              setNewProjectName(project.name);
              setProjectWithOpenMenu(null);
            }}
            className={`flex items-center w-full text-left px-4 py-2 text-sm ${
              theme === 'dark' 
                ? 'text-gray-200 hover:bg-gray-700' 
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <Pencil size={16} className="mr-2" />
            Edit Project
          </button>

          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowMedSpaceDropdown(!showMedSpaceDropdown);
              }}
              className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                theme === 'dark' 
                  ? 'text-gray-200 hover:bg-gray-700' 
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <FolderPlus size={16} className="mr-2" />
              Add to MedSpace
            </button>

            {showMedSpaceDropdown && (
              <div 
                className={`absolute left-full top-0 w-48 rounded-md shadow-lg py-1 ${
                  theme === 'dark' 
                    ? 'bg-gray-800 border border-gray-700' 
                    : 'bg-white border border-gray-200'
                }`}
              >
                {medSpaces.length > 0 ? (
                  medSpaces.map(medSpace => (
                    <button
                      key={medSpace.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        addProjectToMedSpace(project.id, medSpace.id);
                        setShowMedSpaceDropdown(false);
                        setProjectWithOpenMenu(null);
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm ${
                        theme === 'dark' 
                          ? 'text-gray-200 hover:bg-gray-700' 
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {medSpace.name}
                    </button>
                  ))
                ) : (
                  <div className={`px-4 py-2 text-sm ${
                    theme === 'dark' 
                      ? 'text-gray-400' 
                      : 'text-gray-500'
                  }`}>
                    No MedSpaces found
                  </div>
                )}
              </div>
            )}
          </div>

          <button
            onClick={(e) => {
              e.stopPropagation();
              handleProjectDelete(project.id);
              setProjectWithOpenMenu(null);
            }}
            className={`flex items-center w-full text-left px-4 py-2 text-sm ${
              theme === 'dark' 
                ? 'text-red-400 hover:bg-gray-700' 
                : 'text-red-500 hover:bg-gray-100'
            }`}
          >
            <Trash2 size={16} className="mr-2" />
            Delete Project
          </button>
        </div>
      )}
    </div>
  );
};

export default ProjectMenu;
