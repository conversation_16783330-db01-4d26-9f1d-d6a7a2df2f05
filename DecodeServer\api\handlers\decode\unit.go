package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"decodemed/ai/units" // Add import for our new AI units package
	"decodemed/api/handlers/async"
	"decodemed/models"
	"decodemed/storage" // Import for Redis cache

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// activeGenerations keeps track of in-progress unit generations by project ID
var (
	activeGenerationsMutex sync.RWMutex
	activeGenerations      = make(map[uint]bool)
	autoGenService         *AutoGenService // Added AutoGenService as a global variable
)

// InitAutoGenService initializes the auto-generation service
func InitAutoGenService(db *gorm.DB) {
	log.Printf("Initializing AutoGenService with database connection")
	if db == nil {
		log.Printf("ERROR: Attempted to initialize AutoGenService with nil database")
		return
	}

	// Initialize Redis cache
	err := storage.InitRedisClient()
	if err != nil {
		log.Printf("WARNING: Failed to initialize Redis client: %v", err)
		log.Printf("Temporary file storage may be used as fallback")
	} else {
		log.Printf("Redis client initialized successfully")
	}

	autoGenService = NewAutoGenService(db)
	if autoGenService == nil {
		log.Printf("ERROR: Failed to create AutoGenService instance")
	} else {
		log.Printf("AutoGenService initialized successfully")
	}
}

// ---- GenerateUnitRequest represents the request body for generating a unit-------
type GenerateUnitRequest struct {
	ProjectID uint   `json:"project_id" binding:"required"`
	File      []byte `json:"file" binding:"required"`
}

// ---- GetAllUnits returns all units for the authenticated user-------
func GetAllUnits(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, _ := c.Get("user_id")

		// Get project_id from query parameters
		projectIDStr := c.Query("project_id")

		var units []models.Unit
		query := db.Joins("JOIN projects ON units.project_id = projects.id").
			Where("projects.user_id = ?", userID)

		// Filter by project_id if provided
		if projectIDStr != "" {
			query = query.Where("units.project_id = ?", projectIDStr)
		}

		// Remove the Preload("Summary") that's causing the error
		if err := query.Find(&units).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch units"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"units": units})
	}
}

// ---- HandleAsyncUnitGeneration initiates async unit generation-------
func HandleAsyncUnitGeneration(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {

		// Get project_id from form
		projectIDStr := c.PostForm("project_id")

		if projectIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
			return
		}

		projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
			return
		}

		// Check if there's already an active generation for this project
		activeGenerationsMutex.RLock()
		isActive, exists := activeGenerations[uint(projectID)]
		activeGenerationsMutex.RUnlock()

		if exists && isActive {

			c.JSON(http.StatusOK, gin.H{
				"message":             "Unit generation already in progress for this project",
				"already_in_progress": true,
			})
			return
		}

		userID, _ := c.Get("user_id")

		// Verify project belongs to user and get its fileURL
		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Check if units have already been generated for this project
		if project.UnitsGenerated {
			c.JSON(http.StatusOK, gin.H{
				"message":           "Units have already been generated for this project",
				"already_generated": true,
			})
			return
		}

		// Check if units already exist for this project (additional safeguard)
		var unitCount int64
		if err := db.Model(&models.Unit{}).Where("project_id = ?", projectID).Count(&unitCount).Error; err != nil {
			log.Printf("Error checking unit count: %v", err)
		} else if unitCount > 0 {
			// Units exist but flag wasn't set, update the flag
			if err := db.Model(&project).Update("units_generated", true).Error; err != nil {
				log.Printf("Error updating units_generated flag: %v", err)
			}

			c.JSON(http.StatusOK, gin.H{
				"message":           "Units already exist for this project",
				"already_generated": true,
			})
			return
		}

		if project.FileURL == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No file associated with this project"})
			return
		}

		// Mark this project as having an active generation
		activeGenerationsMutex.Lock()
		activeGenerations[uint(projectID)] = true
		activeGenerationsMutex.Unlock()

		// Create job ID and initial status
		jobID := uuid.New().String()

		jobStatus := &async.JobStatus{
			Status:    "pending",
			Progress:  0,
			CreatedAt: time.Now(),
		}

		async.JobMutex.Lock()
		async.JobStatuses[jobID] = jobStatus

		async.JobMutex.Unlock()

		// Return the job ID immediately
		c.JSON(http.StatusAccepted, gin.H{
			"message": "File processing started",
			"job_id":  jobID,
		})

		// Start async processing
		go func() {
			defer func() {
				// Remove from active generations when done, regardless of success/failure
				activeGenerationsMutex.Lock()
				delete(activeGenerations, uint(projectID))
				activeGenerationsMutex.Unlock()
			}()

			// Create a new context for this goroutine
			ctx := context.Background()

			// Start processing
			jobStatus.Status = "processing"
			jobStatus.Progress = 10

			// Download and extract content from the file using Redis cache
			sourceText, fileTitle, redisKey, err := units.DownloadAndExtractContent(ctx, project.FileURL)
			if err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = fmt.Sprintf("Failed to download and extract content: %v", err)
				return
			}
			// Ensure Redis keys are cleaned up when function exits
			defer units.CleanupRedisKey(redisKey)

			// Update status
			jobStatus.Progress = 30

			// Step 2: Process the extracted content with AI
			unitGenerator, err := units.NewUnitGenerator()
			if err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to initialize AI unit generator: " + err.Error()
				return
			}

			// Pass the NumUnits stored in the project to the generation function
			enhancedContent, sourceText, err := unitGenerator.GenerateEnhancedContent(ctx, fileTitle, sourceText, project.NumUnits)
			if err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to process with AI: " + err.Error()
				return
			}

			// Sanitize content to remove null bytes before saving to database
			enhancedContent = models.SanitizeString(enhancedContent)
			sourceText = models.SanitizeString(sourceText)

			jobStatus.Progress = 70

			// Step 3: Split into units and save to database
			unitContents := units.ParseIntoUnits(enhancedContent)
			var createdUnits []models.Unit

			// Use a transaction to ensure all operations succeed or fail together
			err = db.Transaction(func(tx *gorm.DB) error {
				for i, unitContent := range unitContents {
					// Extract title from the unit content
					title := fmt.Sprintf("Unit %d", i+1)
					if strings.HasPrefix(unitContent, "Unit:") {
						if idx := strings.Index(unitContent, "\n"); idx != -1 {
							title = strings.TrimPrefix(strings.TrimSpace(unitContent[:idx]), "Unit: ")
							unitContent = strings.TrimSpace(unitContent[idx+1:])
						}
					}

					unit := models.Unit{
						Title:      title,
						Content:    models.SanitizeString(unitContent),
						SourceText: sourceText, // We already sanitized sourceText above
						ProjectID:  uint(projectID),
					}

					if err := tx.Create(&unit).Error; err != nil {
						return err
					}

					createdUnits = append(createdUnits, unit)
				}

				// Mark the project as having generated units
				if err := tx.Model(&project).Update("units_generated", true).Error; err != nil {
					return err
				}

				return nil
			})

			if err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to save units or update project: " + err.Error()
				return
			}

			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Result = createdUnits

			// Auto-trigger all content generation after units are generated
			if autoGenService != nil {
				log.Printf("Auto-triggering parallel content generation for project %d after unit creation", project.ID)
				autoGenService.TriggerAutoContentGeneration(project.ID, userID)
			} else {
				log.Printf("ERROR: Cannot auto-trigger content generation - autoGenService is nil")
			}

			// Delete job status after completion or failure
			time.Sleep(5 * time.Second) // add a delay
			async.JobMutex.Lock()
			delete(async.JobStatuses, jobID)
			async.JobMutex.Unlock()
		}()
	}
}

// GetGenerationStatus returns the status of an async unit generation job
func GetGenerationStatus(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("jobId")

		// Log all job IDs for debugging
		async.JobMutex.RLock()
		log.Printf("Current job IDs in memory: %v", func() []string {
			keys := make([]string, 0, len(async.JobStatuses))
			for k := range async.JobStatuses {
				keys = append(keys, k)
			}
			return keys
		}())
		status, exists := async.JobStatuses[jobID]
		async.JobMutex.RUnlock()

		if !exists {
			log.Printf("Job ID %s not found in memory", jobID)
			c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
			return
		}

		c.JSON(http.StatusOK, status)
	}
}

// --------------- GetUnit returns a specific unit ---------------
func GetUnit(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		unitID := c.Param("id")
		userID, _ := c.Get("user_id")

		var unit models.Unit
		if err := db.Joins("JOIN projects ON units.project_id = projects.id").
			Where("units.id = ? AND projects.user_id = ?", unitID, userID).
			First(&unit).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found"})
			return
		}

		c.JSON(http.StatusOK, unit)
	}
}

// ---- DeleteUnit deletes a specific unit-------
func DeleteUnit(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		unitID, err := strconv.ParseUint(c.Param("id"), 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid unit ID"})
			return
		}

		userID, _ := c.Get("user_id")

		// Verify unit belongs to user through project
		var unit models.Unit
		if err := db.Joins("JOIN projects ON units.project_id = projects.id").
			Where("units.id = ? AND projects.user_id = ?", unitID, userID).
			First(&unit).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found"})
			return
		}

		// Delete unit and its summary
		if err := db.Transaction(func(tx *gorm.DB) error {
			// Delete summary if exists
			if err := tx.Where("unit_id = ?", unitID).Delete(&models.Quiz{}).Error; err != nil {
				return err
			}

			// Delete the unit
			if err := tx.Delete(&unit).Error; err != nil {
				return err
			}

			return nil
		}); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete unit"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Unit deleted successfully"})
	}
}

// GetUnitsByProject returns all units for a specific project
func GetUnitsByProject(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Query("project_id")
		if projectID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
			return
		}

		// Convert projectID to uint
		projectIDUint, err := strconv.ParseUint(projectID, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
			return
		}

		var units []models.Unit
		if err := db.Where("project_id = ?", projectIDUint).Find(&units).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch units"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"units": units})
	}
}
