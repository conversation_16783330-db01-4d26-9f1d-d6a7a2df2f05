"use client"

import { useEffect, useRef } from "react"
import { motion } from "framer-motion"

interface ActionPotentialGraphProps {
  progress: number
}

export default function ActionPotentialGraph({ progress }: ActionPotentialGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const pathRef = useRef<SVGPathElement>(null)

  // Calculate the action potential curve
  const calculateCurve = (progress: number) => {
    const width = 500
    const height = 300
    const padding = 40

    // Calculate how much of the curve to draw based on progress
    const drawUpTo = Math.min(width * (progress / 100), width)

    // Points for the action potential curve
    const points = []

    for (let x = 0; x <= drawUpTo; x++) {
      const normalizedX = x / width
      let y = 0

      // Resting potential
      if (normalizedX < 0.2) {
        y = 0.7 * height
      }
      // Depolarization (rapid rise)
      else if (normalizedX < 0.3) {
        const t = (normalizedX - 0.2) / 0.1
        y = height * (0.7 - 0.5 * t)
      }
      // Peak
      else if (normalizedX < 0.35) {
        y = height * 0.2
      }
      // Repolarization (fall)
      else if (normalizedX < 0.5) {
        const t = (normalizedX - 0.35) / 0.15
        y = height * (0.2 + 0.6 * t)
      }
      // Hyperpolarization (undershoot)
      else if (normalizedX < 0.6) {
        const t = (normalizedX - 0.5) / 0.1
        y = height * (0.8 - 0.05 * Math.sin(Math.PI * t))
      }
      // Return to resting
      else {
        const t = (normalizedX - 0.6) / 0.4
        y = height * (0.75 - 0.05 * Math.exp(-5 * t))
      }

      points.push([x + padding, y + padding])
    }

    // Create SVG path
    let pathData = `M ${padding},${0.7 * height + padding} `

    points.forEach(([x, y]) => {
      pathData += `L ${x},${y} `
    })

    return pathData
  }

  useEffect(() => {
    if (pathRef.current) {
      pathRef.current.setAttribute("d", calculateCurve(progress))
    }
  }, [progress])

  // Determine current phase label based on progress
  const phaseLabel = progress >= 80
    ? 'Hyperpolarization'
    : progress >= 60
    ? 'Repolarization'
    : progress >= 20
    ? 'Depolarization'
    : ''

  return (
    <div className="bg-[hsl(240_10%_3.9%)] rounded-lg p-4 h-[400px]">
      <h2 className="text-lg font-medium mb-2 text-white">Action Potential Graph</h2>
      <div className="relative h-[320px] w-full">
        <svg
          ref={svgRef}
          width="100%"
          height="100%"
          viewBox="0 0 580 380"
          preserveAspectRatio="xMidYMid meet"
          className="overflow-visible"
        >
          {/* Axes */}
          <line x1="40" y1="40" x2="40" y2="340" stroke="#6b7280" strokeWidth="2" />
          <line x1="40" y1="340" x2="540" y2="340" stroke="#6b7280" strokeWidth="2" />

          {/* Y-axis labels */}
          <text x="35" y="50" textAnchor="end" className="text-xs fill-gray-300">
            +30
          </text>
          <text x="35" y="130" textAnchor="end" className="text-xs fill-gray-300">
            0
          </text>
          <text x="35" y="250" textAnchor="end" className="text-xs fill-gray-300">
            -70
          </text>
          <text x="35" y="340" textAnchor="end" className="text-xs fill-gray-300">
            mV
          </text>

          {/* X-axis labels */}
          <text x="40" y="360" textAnchor="middle" className="text-xs fill-gray-300">
            0
          </text>
          <text x="290" y="360" textAnchor="middle" className="text-xs fill-gray-300">
            Time (ms)
          </text>
          <text x="540" y="360" textAnchor="middle" className="text-xs fill-gray-300">
            5
          </text>

          {/* Horizontal reference lines */}
          <line x1="40" y1="50" x2="540" y2="50" stroke="#374151" strokeWidth="1" strokeDasharray="5,5" />
          <line x1="40" y1="130" x2="540" y2="130" stroke="#374151" strokeWidth="1" strokeDasharray="5,5" />
          <line x1="40" y1="250" x2="540" y2="250" stroke="#374151" strokeWidth="1" strokeDasharray="5,5" />

          {/* Action potential curve */}
          <path
            ref={pathRef}
            d={calculateCurve(progress)}
            fill="none"
            stroke="#d1d5db"
            strokeWidth="3"
            strokeLinecap="round"
            filter="drop-shadow(0 0 4px rgba(209, 213, 219, 0.5))"
          />

          {/* Current position indicator */}
          {progress > 0 && (
            <motion.circle
              cx={40 + (500 * progress) / 100}
              cy={calculateYPosition(progress)}
              r="6"
              fill="#9ca3af"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            />
          )}
        </svg>
      </div>
      {/* Dynamic phase label */}
      {phaseLabel && (
        <div className="mt-2 text-center text-white text-sm">
          {phaseLabel}
        </div>
      )}
    </div>
  )
}

// Helper function to calculate Y position for the indicator dot
function calculateYPosition(progress: number): number {
  const height = 300
  const padding = 40

  const normalizedProgress = progress / 100

  if (normalizedProgress < 0.2) {
    return 0.7 * height + padding
  } else if (normalizedProgress < 0.3) {
    const t = (normalizedProgress - 0.2) / 0.1
    return (0.7 - 0.5 * t) * height + padding
  } else if (normalizedProgress < 0.35) {
    return 0.2 * height + padding
  } else if (normalizedProgress < 0.5) {
    const t = (normalizedProgress - 0.35) / 0.15
    return (0.2 + 0.6 * t) * height + padding
  } else if (normalizedProgress < 0.6) {
    const t = (normalizedProgress - 0.5) / 0.1
    return (0.8 - 0.05 * Math.sin(Math.PI * t)) * height + padding
  } else {
    const t = (normalizedProgress - 0.6) / 0.4
    return (0.75 - 0.05 * Math.exp(-5 * t)) * height + padding
  }
}
