from channels.generic.websocket import AsyncWebsocket<PERSON>onsumer
import json
from .chat_ai.chat_query import chat_query
from .chat_ai.chat_data import fetch_pubmed_articles, parse_articles
from .chat_ai.chat_response import chat_response
from .chat_ai.chat_ranking import rank_articles
import asyncio
import uuid
from django.apps import apps
from .models import ChatEntry
from channels.db import database_sync_to_async
from django.db.models import Max, Min
from uuid import UUID
from datetime import datetime
from django.core.cache import cache
from django.conf import settings
from subscriptions.middleware import SubscriptionMiddleware, AuthenticationMiddleware
from urllib.parse import parse_qs


class ChatConsumer(AsyncWebsocketConsumer):
    
    async def connect(self):
        query_string = self.scope.get('query_string', b'').decode()
        params = parse_qs(query_string)
        token = params.get('token', [None])[0]

        if not token:
            # No token provided
            await self.close(code=4001)
            return

        # Initialize middleware for token verification
        middleware = AuthenticationMiddleware(None)
        try:
            # Verify the token and get user_id
            claims = await middleware.verify_token(token)
            user_id = claims.get('sub') or claims.get('user.id')
            if not user_id:
                await self.close(code=4003)
                return
            
            # Store the user_id in the scope for later use
            self.scope['user_id'] = user_id
            await self.accept()
        except Exception as e:
            print(f"Token verification failed: {str(e)}")
            await self.close(code=4002)
            return

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        question = text_data_json.get('question')
        conversation_id = text_data_json.get('conversationId')

         # Use the verified user_id from the scope
        user_id = self.scope.get('user_id')
        if not user_id:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Authentication required',
            }))
            return
        # Check subscription before processing the chat
        middleware = SubscriptionMiddleware(None)
        allowed, error_message = await middleware.check_subscription(user_id, 'chat')

        if not allowed:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Limit reached, please upgrade',
                'content': error_message
            }))
            return

        # Fetch chat context from the database
        chat_context = await self.get_chat_context(conversation_id)
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
       


        # fetch ranked articles
        articles = await rank_articles(question, chat_context)

        # Initialize response tracking
        full_response = ""
        
        # Get complete text response
        async for chunk in chat_response(question, chat_context, articles):
            full_response += chunk
            # Send text chunk and articles immediately
            await self.send(text_data=json.dumps({
                'type': 'response',
                'content': chunk,
                'articles': articles[:10]  # Send articles with each chunk
            }))

        # Save the chat entry
        await self.create_chat_entry(user_id, question, full_response, conversation_id, articles)
        
        # Send completion without articles since they were already sent
        await self.send(text_data=json.dumps({
            'type': 'complete',
            'conversationId': str(conversation_id)
        }))

    @database_sync_to_async
    def get_chat_context(self, conversation_id):
        if not conversation_id:
            return []
        ChatEntry = apps.get_model('chat_api', 'ChatEntry')
        entries = ChatEntry.objects.filter(conversation_id=conversation_id).order_by('created_at')
        return [{'user': entry.question, 'assistant': entry.response} for entry in entries]

    @database_sync_to_async
    def create_chat_entry(self, user_id, question, response, conversation_id, sources):
        ChatEntry = apps.get_model('chat_api', 'ChatEntry')
        entry = ChatEntry.objects.create(
            user_id=user_id,
            question=question,
            response=response,
            conversation_id=conversation_id,
            sources=sources[:10]
        )
        return entry

    async def disconnect(self, close_code):
        pass

#---------CONVERSATIONS CONSUMER---------#
#ConversationsConsumer is a consumer that handles the conversations between the user and the assistant
class ConversationsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        query_string = self.scope.get('query_string', b'').decode()
        params = parse_qs(query_string)
        token = params.get('token', [None])[0]

        if not token:
            # No token provided
            await self.close(code=4001)
            return

        # Initialize middleware for token verification
        middleware = AuthenticationMiddleware(None)
        try:
            # Verify the token and get user_id
            claims = await middleware.verify_token(token)
            user_id = claims.get('sub') or claims.get('user.id')
            if not user_id:
                await self.close(code=4003)
                return
            
            # Store the user_id in the scope for later use
            self.scope['user_id'] = user_id
            await self.accept()
        except Exception as e:
            print(f"Token verification failed: {str(e)}")
            await self.close(code=4002)
            return

    async def disconnect(self, close_code):
        pass

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        action = text_data_json.get('action')

        # Get the verified user_id from the scope
        user_id = self.scope.get('user_id')
        if not user_id:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Authentication required'
            }))
            return

        if action == 'get_conversations':
            await self.handle_get_conversations({'userId': user_id})
        elif action == 'get_chat_history':
            await self.handle_get_chat_history(text_data_json)

#-----------------CONVERSATIONS LIST-----------------#
 # handle_get_conversations gets the conversations from the database to be sent to the frontend
    async def handle_get_conversations(self, data):
        user_id = data.get('userId')
  
        conversations = await self.get_conversations(user_id)
     
        
        # Convert UUID objects to strings
        serializable_conversations = self.prepare_conversations(conversations)
        
        await self.send(text_data=json.dumps({
            'type': 'conversations',
            'conversations': serializable_conversations
        }))


 # Prepare_conversations makes the conversations serializable to be sent to the frontend
    def prepare_conversations(self, conversations):
        def serialize_conversation(conv):
            return {
                'conversation_id': str(conv['conversation_id']),
                'question': conv['question'],
                'first_created_at': conv['first_created_at'].isoformat() if conv['first_created_at'] else None
            }
        return [serialize_conversation(conv) for conv in conversations]

    # Get_conversations gets the conversations from the database to be sent to the frontend
    @database_sync_to_async
    def get_conversations(self, user_id):
        ChatEntry = apps.get_model('chat_api', 'ChatEntry')
        conversations = list(ChatEntry.objects.filter(user_id=user_id)
                             .values('conversation_id')
                             .annotate(first_created_at=Min('created_at'))
                             .order_by('-first_created_at')
                             .values('conversation_id', 'first_created_at'))
        
        # Get the first question for each conversation
        for conv in conversations:
            first_entry = ChatEntry.objects.filter(
                conversation_id=conv['conversation_id']
            ).order_by('created_at').values('question').first()
            conv['question'] = first_entry['question'] if first_entry else ''
        
        return conversations
    
#CHAT HISTORY ONCE A CONVERSATION IS SELECTED
# handle_get_chat_history gets the chat history from the database to be sent to the frontend
    async def handle_get_chat_history(self, data):
        conversation_id = data.get('conversationId')
        chat_entries = await self.get_chat_history(conversation_id)

        # Convert chat entries to a JSON-serializable format
        serializable_chat_entries = self.prepare_chat_entries(chat_entries)
        
        await self.send(text_data=json.dumps({
            'type': 'chat_history',
            'chatHistory': serializable_chat_entries,
            'conversationId': str(conversation_id),  # Convert UUID to string
            'articles': serializable_chat_entries[0]['sources'] if serializable_chat_entries else []
        }))

    # Prepare_chat_entries makes the chat entries serializable to be sent to the frontend
    def prepare_chat_entries(self, chat_entries):
        def serialize_entry(entry):
            # Convert UUID fields to strings
            for key, value in entry.items():
                if isinstance(value, UUID):
                    entry[key] = str(value)
                elif isinstance(value, datetime):
                    entry[key] = value.isoformat()
         
            
            # Handle JSON fields
            if 'sources' in entry and isinstance(entry['sources'], str):
                try:
                    entry['sources'] = json.loads(entry['sources'])
                except json.JSONDecodeError:
                    entry['sources'] = []
            
            return entry

        return [serialize_entry(entry) for entry in chat_entries]

    # Get_chat_history gets the chat history from the database to be sent to the frontend
    @database_sync_to_async
    def get_chat_history(self, conversation_id):
        ChatEntry = apps.get_model('chat_api', 'ChatEntry')
        chat_entries = list(ChatEntry.objects.filter(conversation_id=conversation_id).order_by('created_at').values())
        return chat_entries
    
