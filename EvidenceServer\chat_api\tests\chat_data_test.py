import unittest
import asyncio
from ..chat_ai.chat_data import fetch_pubmed_articles, parse_articles

class TestPubMedArticles(unittest.TestCase):
    async def run_test(self):
        # Get sample data
        fetch_data, _ = await fetch_pubmed_articles("cancer treatment")
        articles = await parse_articles(fetch_data)


        # Print articles
        for i, article in enumerate(articles[:5], 1):  # Limiting to first 5 articles
            print(f"\nArticle {i}:")
            print(f"Title: {article['title']}")
            print(f"Authors: <AUTHORS>
            print(f"PMID: {article['pmid']}")
            print(f"Publisher: {article['publisher']}")
            print(f"Publication Date: {article['publication_date']}")
            print(f"DOI: {article['doi']}")
            print(f"Link: {article['article_link']}")
            print("-" * 50)

        # Run assertions
        self.assertTrue(len(articles) > 0)
        article = articles[0]
        self.assertIn('pmid', article)
        self.assertIn('title', article)
        self.assertIn('authors', article)
        self.assertIn('abstract', article)
        self.assertIn('publisher', article)
        self.assertIn('publication_date', article)
        self.assertIn('doi', article)
        self.assertIn('article_link', article)

    def test_pubmed_articles(self):
        asyncio.run(self.run_test())

if __name__ == '__main__':
    unittest.main()