{"name": "front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.5", "@clerk/themes": "^2.2.21", "@headlessui/react": "^2.2.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.9", "@stripe/stripe-js": "^6.0.0", "@types/dagre": "^0.7.52", "@types/styled-components": "^5.1.34", "@vercel/analytics": "^1.5.0", "antd": "^5.24.5", "axios": "^1.8.3", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dagre": "^0.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.6.3", "html-to-image": "^1.11.13", "ignore-loader": "^0.1.2", "katex": "^0.16.21", "konva": "^9.3.19", "lucide-react": "^0.482.0", "mammoth": "^1.9.0", "motion": "^12.5.0", "next": "^15.2.4", "next-intl": "^4.0.2", "next-mdx-remote": "^5.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-live": "^4.1.8", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "reactflow": "^11.11.4", "rehype-katex": "^7.0.1", "remark-emoji": "^5.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.1", "styled-components": "^6.1.15", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.2.4", "null-loader": "^4.0.1", "tailwindcss": "^4", "typescript": "^5"}}