from django.http import JsonResponse
from .models import UserSubscription, SubscriptionUsage
from django.conf import settings
from channels.db import database_sync_to_async
from clerk_backend_api import Clerk
import os
import jwt
from asgiref.sync import sync_to_async
from functools import wraps
from django.http import HttpRequest
from typing import Callable, Any, Optional
from channels.middleware import BaseMiddleware

def sync_clerk_get_session(clerk_instance, session_id):
    """Synchronous function to get Clerk session"""
    try:
        return clerk_instance.sessions.get(session_id=session_id)
    except Exception as e:
        print(f"[Auth Debug] Clerk API error: {str(e)}")
        return None

# ASGI version of the middleware
class AuthenticationMiddleware(BaseMiddleware):
    def __init__(self, inner):
        super().__init__(inner)
        self.clerk = Clerk(bearer_auth=os.getenv('CLERK_SECRET_KEY'))
        
        self.exempt_paths = [
            '/',
            '/subscription-api/webhook/',
            '/subscription-api/revenuecat-webhook/',
            '/v1/models',  # OpenAI API route
            '/v1/chat/completions',  # OpenAI chat completions
            '/v1/completions',  # OpenAI completions
            '/v1/embeddings',  # OpenAI embeddings
        ]
        
        # Prefixes that should be exempt from authentication
        self.exempt_prefixes = [
            '/v1/',  # All OpenAI API routes
        ]

    async def verify_token(self, token):
        """Verify the JWT token and return the decoded claims"""
        try:
            # Decode token without verifying signature first to get session ID
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            print(f"[Auth Debug] Token claims: {unverified_claims}")
            session_id = unverified_claims.get('sid')

            if not session_id:
                raise Exception('Invalid session ID')

            # Get session using async wrapper
            session = await database_sync_to_async(sync_clerk_get_session)(self.clerk, session_id)

            if not session:
                raise Exception('Invalid session')

            # If session is valid, return the claims
            return unverified_claims
        except Exception as e:
            print(f"[Auth Debug] Token verification failed: {str(e)}")
            raise e

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            return await self.inner(scope, receive, send)

        # Get the path from the scope
        path = scope.get('path', '')
        
        # Handle OPTIONS requests for CORS
        if scope.get('method') == 'OPTIONS':
            print("[Auth Debug] Handling OPTIONS preflight request")
            return await self.send_cors_preflight_response(send)
            
        # Check if path is exempt (exact match or prefix match)
        if path in self.exempt_paths or any(path.startswith(prefix) for prefix in self.exempt_prefixes):
            print(f"[Auth Debug] Path exempt from authentication: {path}")
            return await self.inner(scope, receive, send)

        headers = dict(scope.get('headers', []))
        auth_header = headers.get(b'authorization', b'').decode()

        if not auth_header or not auth_header.startswith('Bearer '):
            print(f"[Auth Debug] Invalid or missing Bearer token: {auth_header}")
            return await self.send_error(send, 'Invalid authorization header', 401)

        token = auth_header.replace('Bearer ', '')

        try:
            # Get session ID from token claims
            unverified_claims = await self.verify_token(token)
            print(f"[Auth Debug] Token claims: {unverified_claims}")
            session_id = unverified_claims.get('sid')

            if not session_id:
                return await self.send_error(send, 'Invalid session ID', 401)

            # Get session using async wrapper
            session = await database_sync_to_async(sync_clerk_get_session)(self.clerk, session_id)

            if not session:
                return await self.send_error(send, 'Invalid session', 401)

            # Add user_id to scope
            scope['user_id'] = session.user_id
            print(f"[Auth Debug] Authentication successful for user: {session.user_id}")
            return await self.inner(scope, receive, send)

        except Exception as e:
            print(f"[Auth Debug] Token verification failed: {str(e)}")
            print(f"[Auth Debug] Exception type: {type(e)}")
            print(f"[Auth Debug] Exception details: {repr(e)}")
            return await self.send_error(send, f'Authentication failed: {str(e)}', 401)

    async def send_cors_preflight_response(self, send):
        """Send a response for CORS preflight requests"""
        await send({
            'type': 'http.response.start',
            'status': 200,
            'headers': [
                (b'content-type', b'text/plain'),
                (b'Access-Control-Allow-Origin', b'*'),
                (b'Access-Control-Allow-Methods', b'GET, POST, PUT, DELETE, OPTIONS'),
                (b'Access-Control-Allow-Headers', b'Content-Type, Authorization'),
                (b'Access-Control-Max-Age', b'86400'),
            ]
        })
        
        await send({
            'type': 'http.response.body',
            'body': b''
        })

    async def send_error(self, send, message: str, status: int):
        """Helper method to send error responses with CORS headers"""
        response = JsonResponse({'error': message}, status=status)
        
        await send({
            'type': 'http.response.start',
            'status': status,
            'headers': [
                (b'content-type', b'application/json'),
                (b'Access-Control-Allow-Origin', b'*'),
                (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'),
                (b'Access-Control-Allow-Headers', b'Content-Type, Authorization'),
            ]
        })
        
        await send({
            'type': 'http.response.body',
            'body': response.content
        })

# WSGI version of the auth middleware
class WsgiAuthenticationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.clerk = Clerk(bearer_auth=os.getenv('CLERK_SECRET_KEY'))
        
        self.exempt_paths = settings.SUBSCRIPTION_AUTH_EXEMPT_PATHS if hasattr(settings, 'SUBSCRIPTION_AUTH_EXEMPT_PATHS') else [
            '/subscription-api/webhook/',
            '/subscription-api/revenuecat-webhook/',
            '/v1/models',  # OpenAI API route
            '/v1/chat/completions',  # OpenAI chat completions
            '/v1/completions',  # OpenAI completions
            '/v1/embeddings',  # OpenAI embeddings
        ]
        
        # Prefixes that should be exempt from authentication
        self.exempt_prefixes = [
            '/v1/',  # All OpenAI API routes
        ]

    def __call__(self, request):
        # Add CORS headers to all responses
        if request.method == 'OPTIONS':
            response = JsonResponse({}, status=200)
            self.add_cors_headers(response)
            return response

        # Check if path is exempt (exact match or prefix match)
        if request.path in self.exempt_paths or any(request.path.startswith(prefix) for prefix in self.exempt_prefixes):
            print(f"[Auth Debug] Path exempt from authentication: {request.path}")
            response = self.get_response(request)
            self.add_cors_headers(response)
            return response

        # Get authorization header
        auth_header = request.headers.get('Authorization', '')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            response = JsonResponse({'error': 'Invalid authorization header'}, status=401)
            self.add_cors_headers(response)
            return response

        token = auth_header.replace('Bearer ', '')

        try:
            # Get session ID from token claims
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            print(f"[Auth Debug] Token claims: {unverified_claims}")
            session_id = unverified_claims.get('sid')

            if not session_id:
                response = JsonResponse({'error': 'Invalid session ID'}, status=401)
                self.add_cors_headers(response)
                return response

            # Get session using Clerk SDK
            session = sync_clerk_get_session(self.clerk, session_id)

            if not session:
                response = JsonResponse({'error': 'Invalid session'}, status=401)
                self.add_cors_headers(response)
                return response

            # Add user_id to request
            request.user_id = session.user_id
            print(f"[Auth Debug] Authentication successful for user: {session.user_id}")
            
            # Process the request
            response = self.get_response(request)
            self.add_cors_headers(response)
            return response

        except Exception as e:
            print(f"[Auth Debug] Token verification failed: {str(e)}")
            response = JsonResponse({'error': f'Authentication failed: {str(e)}'}, status=401)
            self.add_cors_headers(response)
            return response

    def add_cors_headers(self, response):
        """Add CORS headers to a response"""
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS, PUT, DELETE'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        return response

# ASGI version of subscription middleware
class SubscriptionMiddleware(BaseMiddleware):
    def __init__(self, inner):
        super().__init__(inner)

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            return await self.inner(scope, receive, send)

        # Get user_id from scope (set by AuthenticationMiddleware)
        user_id = scope.get('user_id')
        if not user_id:
            return await self.inner(scope, receive, send)

        # Add subscription check logic here if needed
        # For now, just pass through
        return await self.inner(scope, receive, send)

    async def check_subscription(self, user_id: str, service_type: str) -> tuple[bool, Optional[str]]:
        """Check user's subscription status"""
        try:
            # Get the latest usage record or create a new one
            usage = await database_sync_to_async(SubscriptionUsage.objects.filter(user_id=user_id).first)()
            
            if not usage:
                usage = await database_sync_to_async(SubscriptionUsage.objects.create)(
                    user_id=user_id,
                    search_count=0,
                    chat_count=0
                )

            subscription = await database_sync_to_async(UserSubscription.objects.filter(user_id=user_id).first)()

            # Check if user has active subscription
            if subscription and subscription.subscription_status == 'active':
                # Unlimited usage for paid subscriptions
                if service_type == 'search':
                    usage.search_count += 1
                else:
                    usage.chat_count += 1
                await database_sync_to_async(usage.save)()
                return True, None
            
            # Free trial limit check
            count = usage.search_count if service_type == 'search' else usage.chat_count
            if count >= 1000:
                return False, f'{service_type.capitalize()} limit reached'
            
            # Increment usage counter
            if service_type == 'search':
                usage.search_count += 1
            else:
                usage.chat_count += 1
            await database_sync_to_async(usage.save)()
            return True, None

        except Exception as e:
            print(f"Error in check_subscription: {str(e)}")
            return False, f"Error checking subscription: {str(e)}"

    async def send_error(self, send, message: str, status: int):
        """Helper method to send error responses"""
        response = JsonResponse({'error': message}, status=status)
        
        await send({
            'type': 'http.response.start',
            'status': status,
            'headers': [
                (b'content-type', b'application/json'),
                (b'Access-Control-Allow-Origin', b'*'),
                (b'Access-Control-Allow-Methods', b'GET, POST, OPTIONS'),
                (b'Access-Control-Allow-Headers', b'Content-Type, Authorization'),
            ]
        })
        
        await send({
            'type': 'http.response.body',
            'body': response.content
        })

# WSGI version of subscription middleware
class WsgiSubscriptionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Get user_id from request (set by WsgiAuthenticationMiddleware)
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return self.get_response(request)

        # Process the request
        response = self.get_response(request)
        # Add CORS headers
        self.add_cors_headers(response)
        return response

    def check_subscription(self, user_id: str, service_type: str) -> tuple[bool, Optional[str]]:
        """Check user's subscription status"""
        try:
            # Get the latest usage record or create a new one
            usage = SubscriptionUsage.objects.filter(user_id=user_id).first()
            
            if not usage:
                usage = SubscriptionUsage.objects.create(
                    user_id=user_id,
                    search_count=0,
                    chat_count=0
                )

            subscription = UserSubscription.objects.filter(user_id=user_id).first()

            # Check if user has active subscription
            if subscription and subscription.subscription_status == 'active':
                # Unlimited usage for paid subscriptions
                if service_type == 'search':
                    usage.search_count += 1
                else:
                    usage.chat_count += 1
                usage.save()
                return True, None
            
            # Free trial limit check
            count = usage.search_count if service_type == 'search' else usage.chat_count
            if count >= 1000:
                return False, f'{service_type.capitalize()} limit reached'
            
            # Increment usage counter
            if service_type == 'search':
                usage.search_count += 1
            else:
                usage.chat_count += 1
            usage.save()
            return True, None

        except Exception as e:
            print(f"Error in check_subscription: {str(e)}")
            return False, f"Error checking subscription: {str(e)}"
            
    def add_cors_headers(self, response):
        """Add CORS headers to a response"""
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS, PUT, DELETE'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        return response 