'use client'
import React from 'react'
import { HeroHeader } from '@/containers/main/hero5-header'
import { Card } from '@/components/ui/card'
import { MainThemeProvider, useMainTheme } from '@/components/theme/main-theme-provider'

const ContactContent = () => {
  const { theme } = useMainTheme()
  
  return (
    <div className={`min-h-screen flex flex-col ${theme === 'dark' ? 'bg-background dark' : 'bg-white'}`}>
      <HeroHeader />
      
      <section className="py-32">
        <div className="mx-auto max-w-3xl px-8 lg:px-0">
            <h1 className="text-center text-4xl font-semibold lg:text-5xl text-[#091225] dark:text-white">Contact Us</h1>
            <p className="mt-4 text-center text-neutral-500 dark:text-neutral-400">Get in touch with us at:</p>

            <Card className="mx-auto mt-12 max-w-lg p-8 shadow-md sm:p-16">
                <div className="text-center">
                    <div className="space-y-4">
                        <h2 className="text-2xl font-semibold text-[#091225] dark:text-white"><EMAIL></h2>
                        <p className="text-neutral-600 dark:text-neutral-400">
                          We are here to help! Feel free to reach out for any questions or support needs.
                        </p>
                    </div>
                </div>
            </Card>
        </div>
      </section>
    </div>
  )
}

export default function ContactPage() {
  return (
    <MainThemeProvider>
      <ContactContent />
    </MainThemeProvider>
  )
}
