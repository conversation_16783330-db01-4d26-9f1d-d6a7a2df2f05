"use client";

import { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';

export default function CatchAllDecodePage() {
  const router = useRouter();
  const params = useParams();
  
  useEffect(() => {
    // params.params will be an array of path segments
    const pathSegments = params.params as string[];
    
    if (!pathSegments || pathSegments.length === 0) {
      // If no params, redirect to dashboard
      router.push('/studio');
      return;
    }
    
    if (pathSegments.length === 1) {
      // If only one parameter (likely a project ID), redirect to a default medspace (0) with the project ID
      router.push(`/decode/0/${pathSegments[0]}`);
      return;
    }
    
    // For other cases, redirect to the proper format
    if (pathSegments.length >= 2) {
      const medspaceId = pathSegments[0];
      const projectId = pathSegments[1];
      router.push(`/decode/${medspaceId}/${projectId}`);
      return;
    }
  }, [params, router]);

  return (
    <div className="h-screen flex items-center justify-center">
      <p className="text-lg">Redirecting...</p>
    </div>
  );
} 