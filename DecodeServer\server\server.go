package server

import (
	"decodemed/api/routes"
	"decodemed/config"
	"decodemed/database"
	"decodemed/middleware"
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Server represents the HTTP server
type Server struct {
	router *gin.Engine
	db     *gorm.DB
	config *config.Config
}

// New creates a new server instance
func New() (*Server, error) {
	// Load configuration
	cfg, err := config.GetInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to load configuration: %v", err)
	}

	// Initialize database connection
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %v", err)
	}

	// Set Gin mode based on environment
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create a new router with middleware
	router := gin.New()

	// Set trusted proxies
	router.SetTrustedProxies([]string{"127.0.0.1", "::1"})

	// Add middleware
	router.Use(middleware.CORSMiddleware())
	router.Use(gin.Recovery())
	router.Use(middleware.ErrorHandler())
	router.Use(middleware.AuthErrorHandler())

	// Setup routes
	routes.SetupRoutes(router, db)

	return &Server{
		router: router,
		db:     db,
		config: cfg,
	}, nil
}

// Start starts the HTTP server
func (s *Server) Start() error {
	log.Printf("Starting server on port %d in %s mode", s.config.Port, s.config.Env)
	return s.router.Run(s.config.ServerAddress())
}

// GetRouter returns the router instance (useful for testing)
func (s *Server) GetRouter() *gin.Engine {
	return s.router
}

// GetDB returns the database instance
func (s *Server) GetDB() *gorm.DB {
	return s.db
}
