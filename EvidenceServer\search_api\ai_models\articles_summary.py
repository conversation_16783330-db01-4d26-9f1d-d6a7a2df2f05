import os
from openai import Async<PERSON>penAI
from dotenv import load_dotenv
import asyncio
load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

async def generate_summary(question, articles):
    combined_abstracts = "\n\n".join([f"[{i+1}]. {article['title']}: {article['abstract']}" for i, article in enumerate(articles)])

    response = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "user",
                "content": (
                    f"Respond to the question: '{question}' "
                    "with this research data:\n\n"
                    f"{combined_abstracts}\n\n"
                    
                   "Title: Do not include an overall title for the summary"
                    "Snapshot: This should give the rapid answer to the question, with only 20 words, use references"
                    "Body: Ensure it responds to the question with the information from the research articles. "
                    "Body: Use references for each bullet point. "
                    """
                    References: 
                    "Do not return a list of references, just return the references in the text. "
                    """
                    "The summary itself should be about 300 and 500 words. "
                    """ 
                    the summary should be in the following format:
                    Direct Answer to the question(bold) [1][2][3]
                    (subtitle 1)
                     - bullet point 1. [4][5] 
                     - bullet point 2. [6][7]
                    Subtitle 2(bold):
                     - bullet point 1. [8][9]
                     - bullet point 2. [10][11]
                     """
                    "Use markdown format and always return a well and consistently formatted text."
                )
            }
        ],
        stream=True
    )

    async for chunk in response:
        if chunk.choices[0].delta.content is not None:
            yield chunk.choices[0].delta.content


