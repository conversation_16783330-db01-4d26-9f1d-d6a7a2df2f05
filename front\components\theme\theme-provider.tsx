"use client"

import { createContext, useContext, useEffect, useState } from "react"

type Theme = "dark" | "light"

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
}

type ThemeProviderState = {
  theme: Theme
  toggleTheme: () => void
}

const ThemeProviderContext = createContext<ThemeProviderState | undefined>(undefined)

export function ThemeProvider({
  children,
  defaultTheme = "dark",
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme)

  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove("light", "dark")
    root.classList.add(theme)
    
    if (theme === 'dark') {
      const darkBg = "hsl(0 0% 7.0%)"
      root.style.setProperty('--background', darkBg)
      root.style.setProperty('--background-color', darkBg)
      root.style.setProperty('--background-color-200', darkBg)
      root.style.setProperty('--foreground', 'hsl(0 0% 95%)')
      
      // Dark mode scrollbar colors - with stronger colors for visibility
      root.style.setProperty('--scrollbar-track', 'hsl(0 0% 15%)')
      root.style.setProperty('--scrollbar-thumb', 'hsl(0 0% 35%)')
      root.style.setProperty('--scrollbar-thumb-hover', 'hsl(0 0% 45%)')
      root.style.setProperty('--pdf-bg', darkBg)
      
      // Add a global class for dark scrollbars
      document.body.classList.add('dark-scrollbars')
      document.body.classList.remove('light-scrollbars')
    } else {
      const lightBg = "#ffffff"
      root.style.setProperty('--background', lightBg)
      root.style.setProperty('--background-color', lightBg)
      root.style.setProperty('--background-color-200', '#f3f4f6')
      root.style.setProperty('--foreground', 'hsl(0 0% 3.9%)')
      
      // Light mode scrollbar colors
      root.style.setProperty('--scrollbar-track', '#f1f1f1')
      root.style.setProperty('--scrollbar-thumb', '#c1c1c1')
      root.style.setProperty('--scrollbar-thumb-hover', '#a8a8a8')
      root.style.setProperty('--pdf-bg', lightBg)
      
      // Add a global class for light scrollbars
      document.body.classList.add('light-scrollbars')
      document.body.classList.remove('dark-scrollbars')
    }

    // Add a global style for scrollbars
    let globalScrollbarStyle = document.getElementById('global-scrollbar-style');
    if (!globalScrollbarStyle) {
      globalScrollbarStyle = document.createElement('style');
      globalScrollbarStyle.id = 'global-scrollbar-style';
      document.head.appendChild(globalScrollbarStyle);
    }

    globalScrollbarStyle.textContent = `
      /* Global scrollbar styles */
      ::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }
      ::-webkit-scrollbar-track {
        background: var(--scrollbar-track);
      }
      ::-webkit-scrollbar-thumb {
        background: var(--scrollbar-thumb);
        border-radius: 6px;
        border: 3px solid var(--scrollbar-track);
      }
      ::-webkit-scrollbar-thumb:hover {
        background: var(--scrollbar-thumb-hover);
      }
      /* Firefox */
      * {
        scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
        scrollbar-width: thin;
      }
    `;
    
  }, [theme])

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light")
  }

  return (
    <ThemeProviderContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}