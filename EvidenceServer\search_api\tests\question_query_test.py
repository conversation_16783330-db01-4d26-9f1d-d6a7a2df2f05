import unittest
import asyncio
from unittest.mock import AsyncMock, patch
from ..ai_models.question_query import generate_search_query

class TestQuestionQuery(unittest.TestCase):
    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        self.loop.close()

    def test_generate_search_query_integration(self):
        # Test question and publication types
        question = "What are the latest treatments for type 1 diabetes?"
        publication_types = ""
        
        # Run the async function
        query = self.loop.run_until_complete(
            generate_search_query(question, publication_types)
        )
        
        # Print the generated query
        print(f"\nGenerated query: {query}")
        
        # Basic assertions
        self.assertIsInstance(query, str)
        self.assertGreater(len(query), 0)
        
        # Since publication_types is empty, we don't need to check for it
        # Removed the publication types assertion

if __name__ == '__main__':
    unittest.main()

