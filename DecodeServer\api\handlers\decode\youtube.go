package handlers

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	ytai "decodemed/ai/youtube"
	"decodemed/models"

	"github.com/gin-gonic/gin"
	"google.golang.org/api/option"
	youtube "google.golang.org/api/youtube/v3"
	"gorm.io/gorm"
)

// Regex pattern to extract YouTube video ID from various URL formats
var youtubeRegex = regexp.MustCompile(`(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})`)

// GetYouTubeClient returns a YouTube API client
func GetYouTubeClient() (*youtube.Service, error) {
	apiKey := os.Getenv("YOUTUBE_API_KEY")
	if apiKey == "" {
		return nil, errors.New("YOUTUBE_API_KEY environment variable not set")
	}

	ctx := context.Background()
	service, err := youtube.NewService(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create YouTube client: %v", err)
	}

	return service, nil
}

// ExtractYouTubeID extracts video ID from various YouTube URL formats
func ExtractYouTubeID(youtubeURL string) (string, error) {
	log.Printf("Attempting to extract YouTube ID from URL: %s", youtubeURL)

	// Handle common URL prefixes if missing
	if !strings.HasPrefix(youtubeURL, "http://") && !strings.HasPrefix(youtubeURL, "https://") {
		youtubeURL = "https://" + youtubeURL
		log.Printf("Added https:// prefix, URL is now: %s", youtubeURL)
	}

	matches := youtubeRegex.FindStringSubmatch(youtubeURL)
	if len(matches) < 2 {
		log.Printf("Failed to extract YouTube ID, regex didn't match. URL: %s", youtubeURL)
		return "", errors.New("invalid YouTube URL format")
	}

	videoID := matches[1]
	log.Printf("Successfully extracted YouTube ID: %s from URL: %s", videoID, youtubeURL)
	return videoID, nil
}

// YouTubeVideoInfo holds information about a YouTube video
type YouTubeVideoInfo struct {
	VideoID      string   `json:"video_id"`
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	ThumbnailURL string   `json:"thumbnail_url"`
	Languages    []string `json:"available_languages"`
}

// FetchYouTubeInfo gets information about a YouTube video
func FetchYouTubeInfo(c *gin.Context) {
	youtubeURL := c.Query("url")
	if youtubeURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "YouTube URL is required"})
		return
	}

	log.Printf("FetchYouTubeInfo: Processing URL: %s", youtubeURL)

	videoID, err := ExtractYouTubeID(youtubeURL)
	if err != nil {
		log.Printf("Error extracting YouTube ID: %v from URL: %s", err, youtubeURL)
		// Still return a valid response with just the URL
		c.JSON(http.StatusOK, gin.H{
			"video_id":            "",
			"title":               "Unknown Video",
			"description":         "",
			"thumbnail_url":       "",
			"available_languages": []string{"en"},
		})
		return
	}

	// Try to get YouTube client
	ytService, err := GetYouTubeClient()
	if err != nil {
		log.Printf("YouTube API error: %v", err)
		// Return a minimal response with just the video ID
		c.JSON(http.StatusOK, gin.H{
			"video_id":            videoID,
			"title":               "YouTube Video",
			"description":         "",
			"thumbnail_url":       fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID),
			"available_languages": []string{"en"},
		})
		return
	}

	// Get video details
	call := ytService.Videos.List([]string{"snippet"}).Id(videoID)
	response, err := call.Do()
	if err != nil || len(response.Items) == 0 {
		log.Printf("Failed to fetch video info: %v", err)
		// Return a minimal response with just the video ID
		c.JSON(http.StatusOK, gin.H{
			"video_id":            videoID,
			"title":               "YouTube Video",
			"description":         "",
			"thumbnail_url":       fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID),
			"available_languages": []string{"en"},
		})
		return
	}

	// Normal response processing
	video := response.Items[0]
	snippet := video.Snippet

	// Build response
	info := YouTubeVideoInfo{
		VideoID:     videoID,
		Title:       snippet.Title,
		Description: snippet.Description,
		Languages:   []string{"en"}, // Default to English
	}

	// Get the highest quality thumbnail available
	if snippet.Thumbnails != nil {
		if snippet.Thumbnails.Maxres != nil {
			info.ThumbnailURL = snippet.Thumbnails.Maxres.Url
		} else if snippet.Thumbnails.High != nil {
			info.ThumbnailURL = snippet.Thumbnails.High.Url
		} else if snippet.Thumbnails.Medium != nil {
			info.ThumbnailURL = snippet.Thumbnails.Medium.Url
		} else if snippet.Thumbnails.Default != nil {
			info.ThumbnailURL = snippet.Thumbnails.Default.Url
		} else {
			// Fallback to standard YouTube thumbnail URL
			info.ThumbnailURL = fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID)
		}
	} else {
		// Fallback to standard YouTube thumbnail URL
		info.ThumbnailURL = fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID)
	}

	c.JSON(http.StatusOK, info)
}

// CreateYouTubeProject creates a new project from a YouTube video
func CreateYouTubeProject(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			YouTubeURL string `json:"youtube_url" binding:"required"`
			MedSpaceID *uint  `json:"med_space_id"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		log.Printf("CreateYouTubeProject: Received request with URL: %s", req.YouTubeURL)

		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Extract video ID from URL
		videoID, err := ExtractYouTubeID(req.YouTubeURL)
		if err != nil {
			log.Printf("CreateYouTubeProject: Error extracting video ID: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid YouTube URL: " + err.Error()})
			return
		}

		log.Printf("CreateYouTubeProject: Successfully extracted video ID: %s", videoID)

		// Get YouTube client
		ytService, err := GetYouTubeClient()
		if err != nil {
			log.Printf("CreateYouTubeProject: YouTube API error: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize YouTube API: " + err.Error()})
			return
		}

		// Get video details
		call := ytService.Videos.List([]string{"snippet"}).Id(videoID)
		response, err := call.Do()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video info: " + err.Error()})
			return
		}

		if len(response.Items) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
			return
		}

		video := response.Items[0]
		snippet := video.Snippet

		// Create a new project
		// Handle different user ID types correctly
		var userIDUint uint
		switch v := userID.(type) {
		case uint:
			userIDUint = v
		case int:
			userIDUint = uint(v)
		case float64:
			userIDUint = uint(v)
		case string:
			id, err := strconv.ParseUint(v, 10, 32)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
				return
			}
			userIDUint = uint(id)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Unexpected user ID type: %T", userID)})
			return
		}

		// Get the thumbnail URL from the video snippet
		thumbnailURL := fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID)
		if snippet.Thumbnails != nil {
			if snippet.Thumbnails.Maxres != nil {
				thumbnailURL = snippet.Thumbnails.Maxres.Url
			} else if snippet.Thumbnails.High != nil {
				thumbnailURL = snippet.Thumbnails.High.Url
			} else if snippet.Thumbnails.Medium != nil {
				thumbnailURL = snippet.Thumbnails.Medium.Url
			} else if snippet.Thumbnails.Default != nil {
				thumbnailURL = snippet.Thumbnails.Default.Url
			}
		}

		project := models.Project{
			Name:                  snippet.Title,
			UserID:                userIDUint,
			MedSpaceID:            req.MedSpaceID,
			FileURL:               req.YouTubeURL,
			ThumbnailURL:          thumbnailURL,       // Store the thumbnail URL directly
			Type:                  models.TypeYouTube, // Use the new ContentType enum instead of IsYouTube
			YouTubeUnitsGenerated: false,              // Initialize YouTube units generated flag
		}

		// Start a transaction to create project and update user counts atomically
		err = db.Transaction(func(tx *gorm.DB) error {
			// Create the project
			if err := tx.Create(&project).Error; err != nil {
				return err
			}

			// Get user to check subscription status
			var user models.User
			if err := tx.First(&user, userIDUint).Error; err != nil {
				return err
			}

			// If user doesn't have an active subscription, increment their free projects count
			if status, _ := models.GetSubscriptionStatusForUser(tx, userIDUint); status != "active" && status != "trialing" {
				if err := tx.Model(&user).Update("free_projects_count", user.FreeProjectsCount+1).Error; err != nil {
					return err
				}
				log.Printf("Incremented free projects count for user %d to %d", user.ID, user.FreeProjectsCount+1)
			}

			return nil
		})

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project: " + err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"project": project})
	}
}

// GenerateYouTubeUnits creates units for a YouTube project using AI
func GenerateYouTubeUnits(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			ProjectID uint `json:"project_id" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Get the project
		var project models.Project
		if err := db.First(&project, req.ProjectID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch project: " + err.Error()})
			}
			return
		}

		userID, _ := c.Get("user_id")

		// Verify it's a YouTube project
		if project.Type != models.TypeYouTube {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Not a YouTube project"})
			return
		}

		// Check if YouTube units are already generated
		if project.YouTubeUnitsGenerated {
			// Get existing units
			var existingUnits []models.Unit
			if err := db.Where("project_id = ?", project.ID).Find(&existingUnits).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch existing units: " + err.Error()})
				return
			}

			// Return existing units
			c.JSON(http.StatusOK, gin.H{
				"message":           "YouTube units already generated",
				"units":             existingUnits,
				"already_generated": true,
			})
			return
		}

		// Even if the flag is not set, check if there are actually units for this project
		var existingUnits []models.Unit
		var unitCount int64
		if err := db.Model(&models.Unit{}).Where("project_id = ?", project.ID).Count(&unitCount).Error; err != nil {
			log.Printf("Error checking if units exist for project %d: %v", project.ID, err)
		} else if unitCount > 0 {
			// Units exist but flag wasn't set, update the flag
			if err := db.Exec("UPDATE projects SET youtube_units_generated = true WHERE id = ?", project.ID).Error; err != nil {
				log.Printf("Error updating youtube_units_generated flag: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project: " + err.Error()})
				return
			} else {
				log.Printf("Successfully updated youtube_units_generated flag for project %d", project.ID)
			}

			// Fetch the existing units
			if err := db.Where("project_id = ?", project.ID).Find(&existingUnits).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch existing units: " + err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message":           "YouTube units already exist",
				"units":             existingUnits,
				"already_generated": true,
			})
			return
		}

		// Create a YouTube summarizer
		summarizer, err := ytai.NewYouTubeSummarizer()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize YouTube summarizer: " + err.Error()})
			return
		}
		defer summarizer.Close()

		// Extract video ID and get video details
		videoID, err := ExtractYouTubeID(project.FileURL)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid YouTube URL: " + err.Error()})
			return
		}

		// Get YouTube client
		ytService, err := GetYouTubeClient()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize YouTube API: " + err.Error()})
			return
		}

		// Get video details
		call := ytService.Videos.List([]string{"snippet"}).Id(videoID)
		response, err := call.Do()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video info: " + err.Error()})
			return
		}

		if len(response.Items) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
			return
		}

		video := response.Items[0]
		snippet := video.Snippet

		// Generate structured units using AI
		ctx := context.Background()
		opts := ytai.DefaultSummaryOptions()
		opts.UnitCount = 5 // Generate 5 units

		aiUnits, fullContent, err := summarizer.GenerateStructuredUnits(ctx, project.FileURL, snippet.Title, opts)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate AI units: " + err.Error()})
			return
		}

		// Convert AI units to database units
		var units []models.Unit
		for _, aiUnit := range aiUnits {
			unit := models.Unit{
				Title:          aiUnit.Title,
				SourceText:     aiUnit.SourceText,
				Type:           models.TypeYouTube,
				ProjectID:      project.ID,
				YoutubeSummary: aiUnit.YoutubeSummary,
			}
			units = append(units, unit)
		}

		// Create the units in the database
		for i := range units {
			if err := db.Create(&units[i]).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create unit: " + err.Error()})
				return
			}
		}

		// Update project to mark YouTube units as generated
		if err := db.Model(&models.Project{}).Where("id = ?", project.ID).Update("youtube_units_generated", true).Error; err != nil {
			log.Printf("Error updating youtube_units_generated flag: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project: " + err.Error()})
			return
		} else {
			log.Printf("Successfully updated youtube_units_generated flag for project %d", project.ID)

			// Refresh the project to ensure we have the latest state
			if err := db.First(&project, project.ID).Error; err != nil {
				log.Printf("Warning: Failed to refresh project after update: %v", err)
			}
		}

		// Auto-trigger content generation (quizzes, mindmaps, flashcards) in parallel after YouTube units are generated
		youtubeService := GetYouTubeAutoGenService()
		if youtubeService != nil {
			log.Printf("Auto-triggering parallel content generation for YouTube project %d", project.ID)

			// Small delay to ensure database updates are propagated
			go func(projectID uint, userID interface{}) {
				// Wait a moment to make sure database transactions are committed
				time.Sleep(500 * time.Millisecond)
				youtubeService.TriggerYouTubeAutoContentGeneration(projectID, userID)
			}(project.ID, userID)

		} else {
			log.Printf("ERROR: Cannot auto-trigger content generation - YouTube auto generation service is nil")

			// Fallback to just quizzes if the full service is unavailable
			if autoGenService != nil {
				log.Printf("Falling back to just auto-triggering quiz generation for project %d", project.ID)
				go func(projectID uint, userID interface{}) {
					time.Sleep(500 * time.Millisecond)
					autoGenService.TriggerAutoQuizGeneration(projectID, userID)
				}(project.ID, userID)
			} else {
				log.Printf("ERROR: Cannot auto-trigger quiz generation - autoGenService is nil")
			}
		}

		c.JSON(http.StatusCreated, gin.H{
			"message":      "Created YouTube units with AI successfully",
			"units":        units,
			"full_content": fullContent,
		})
	}
}

// RegisterYouTubeHandlers registers all YouTube API handlers
// DEPRECATED: YouTube endpoints are now registered directly in routes.go
func RegisterYouTubeHandlers(router *gin.Engine, db *gorm.DB) {
	// This function is no longer used.
	// YouTube endpoints are now registered directly in routes.go in the protected routes group.
}
