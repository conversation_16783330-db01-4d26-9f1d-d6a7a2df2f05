import os
from openai import Async<PERSON>penAI
from dotenv import load_dotenv
import asyncio
from search_api.ai_models.articles_data import get_pubmed_results
from search_api.ai_models.question_query import generate_search_query

load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

async def extract_study_design(question, articles):
    study_designs = []
    for article in articles:
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": """You are an expert in evidence based medicine and research methodology. Identify the study design from the given abstract. 
                    Possible study designs include:

                     Clinical Practice Guidelines

                     Systematic Review 
                     Meta-analysis
                     Randomized Controlled Trial (RCT)
                 
                     Cohort Study
                     Case-Control Study
                     Cross-sectional Study

                     Case Series
                     Case Report

                     Narrative Review: Do not confuse with systematic review or meta-analysis.
                     Expert Opinion
                     Editorial opinion

                     In vivo study
                     In vitro study
                    
                   
                    
                    
                    
                    If the study design is not clear or doesn't fit these categories, respond with 'Unavailable'.
                   
                    Do not return other text or explanation, do not return numbers or references.
                   
                    """
                    
                },
                {
                    "role": "user",
                    "content": f"Identify the study design from the following abstract:\n\nAbstract: {article['abstract']}"
                }
            ],
            temperature=0.4
        )
        design = response.choices[0].message.content.strip()
        study_designs.append({
            'title': article['title'],
            'study_design': design
        })
    return study_designs





