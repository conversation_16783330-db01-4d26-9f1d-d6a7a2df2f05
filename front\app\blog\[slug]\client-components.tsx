'use client'

import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import Script from 'next/script';
import { PostData } from './posts-data';

interface ClientBlogPostProps {
  post: PostData;
}

export function ClientBlogPost({ post }: ClientBlogPostProps) {
  
  return (
    <main>
      {/* Breadcrumb JSON-LD Structured Data */}
      <Script id="structured-data" type="application/ld+json" strategy="afterInteractive">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Article",
          mainEntityOfPage: {
            "@type": "WebPage",
            "@id": `https://decodemed.com/blog/${post.slug}`,
          },
          headline: post.title,
          image: post.ogImage,
          datePublished: post.date,
          author: {
            "@type": "Person",
            name: "DecodeMed Team",
          },
          publisher: {
            "@type": "Organization",
            name: "DecodeMed",
            logo: {
              "@type": "ImageObject",
              url: "https://decodemed.com/images/logo.png",
            },
          },
          description: "Learn about the latest in medical education and study techniques.",
        })}
      </Script>

      <section className="py-16">
        <div className="mx-auto max-w-4xl px-6">
          <div className="mb-10">
            <Link href="/blog" className="text-primary flex items-center gap-1 mb-8 hover:underline">
              <ChevronLeft className="w-4 h-4" />
              <span>Back to Blog</span>
            </Link>
            
            <h1 className="text-3xl md:text-4xl font-bold mb-4">{post.title}</h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-8">
              <span>By DecodeMed Team</span>
              <span>•</span>
              <time dateTime={post.date}>
                {new Date(post.date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
            </div>
          </div>

          <div className="prose prose-lg max-w-none dark:prose-invert">
            {post.content}
          </div>

          <div className="mt-12 pt-8 border-t">
            <h3 className="text-xl font-bold mb-4">Continue Reading</h3>
            <div className="grid grid-cols-1 gap-4">
              {post.relatedPosts
                .slice(0, 2)
                .map(relatedPost => (
                  <Link 
                    key={relatedPost.slug}
                    href={`/blog/${relatedPost.slug}`}
                    className="group block p-4 border rounded-lg hover:border-primary transition-all duration-200"
                  >
                    <h4 className="font-semibold group-hover:text-primary transition-colors duration-200">
                      {relatedPost.title}
                    </h4>
                    <time className="text-sm text-muted-foreground">
                      {new Date(relatedPost.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </time>
                  </Link>
                ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 