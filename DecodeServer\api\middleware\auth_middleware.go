package middleware

import (
	"log"
	"net/http"
	"os"
	"strings"

	"decodemed/models"

	"github.com/clerk/clerk-sdk-go/v2"
	"github.com/clerk/clerk-sdk-go/v2/jwt"
	"github.com/clerk/clerk-sdk-go/v2/user"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// responseWriter wraps gin's ResponseWriter to implement http.ResponseWriter
type responseWriter struct {
	gin.ResponseWriter
}

func (w *responseWriter) Write(b []byte) (int, error) {
	return w.ResponseWriter.Write(b)
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.ResponseWriter.WriteHeader(statusCode)
}

// AuthMiddleware authenticates requests using Clerk SDK
func AuthMiddleware() gin.HandlerFunc {
	// Initialize Clerk client
	clerk.SetKey(os.Getenv("CLERK_SECRET_KEY"))

	return func(c *gin.Context) {
		// Skip auth for OPTIONS requests (CORS preflight)
		if c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// Log the incoming request
		log.Printf("Incoming request to: %s %s", c.Request.Method, c.Request.URL.Path)

		// Get the Authorization header
		authHeader := c.GetHeader("Authorization")

		if authHeader == "" {
			log.Printf("No Authorization header found")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "No authorization header"})
			c.Abort()
			return
		}

		// Validate Bearer token format
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			log.Printf("Invalid authorization header format")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		sessionToken := parts[1]

		// Verify the session token
		claims, err := jwt.Verify(c.Request.Context(), &jwt.VerifyParams{
			Token: sessionToken,
		})
		if err != nil {
			log.Printf("Token verification failed: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Get the database from the context
		db, exists := c.Get("db")
		if !exists {
			log.Printf("Database not available in context")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
			c.Abort()
			return
		}

		gormDB, ok := db.(*gorm.DB)
		if !ok {
			log.Printf("Invalid database instance type")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid database instance"})
			c.Abort()
			return
		}

		// Get or create user
		var dbUser models.User
		result := gormDB.Where("clerk_id = ?", claims.Subject).First(&dbUser)
		if result.Error != nil {
			log.Printf("User not found in database, attempting to create: %v", result.Error)

			// Get user details from Clerk
			clerkUser, err := user.Get(c.Request.Context(), claims.Subject)
			if err != nil {
				log.Printf("Failed to get user from Clerk: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user details"})
				c.Abort()
				return
			}

			// Create new user
			dbUser = models.User{
				ClerkID: claims.Subject,
				Email:   clerkUser.EmailAddresses[0].EmailAddress,
			}
			if err := gormDB.Create(&dbUser).Error; err != nil {
				log.Printf("Failed to create user: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
				c.Abort()
				return
			}
			log.Printf("Created new user: ID=%d, Email=%s", dbUser.ID, dbUser.Email)
		}

		// Set user information in context
		c.Set("user_id", dbUser.ID)
		c.Set("user_email", dbUser.Email)
		c.Set("clerk_id", dbUser.ClerkID)
		log.Printf("Successfully authenticated user: ID=%d, Email=%s, ClerkID=%s", dbUser.ID, dbUser.Email, dbUser.ClerkID)

		c.Next()
	}
}
