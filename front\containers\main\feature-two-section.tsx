'use client'

// No imports needed for video element
import { useLanguage } from '@/app/providers/language-provider'

export default function ContentSection() {
    const { t } = useLanguage()
    return (
        <section className="py-12 sm:py-16 md:py-24 lg:py-32 dark:bg-[hsl(240_10%_3.9%)]">
            <div className="mx-auto max-w-5xl space-y-6 sm:space-y-8 md:space-y-12 lg:space-y-16 px-4 sm:px-6">
                <h2 className="relative z-10 max-w-xl text-2xl sm:text-3xl md:text-4xl font-medium text-gray-900 dark:text-gray-100 lg:text-5xl">{t('featureTwo.title')}</h2>
                <div className="grid gap-6 md:gap-8 lg:gap-12 xl:gap-24 md:grid-cols-2">
                    <div className="relative mb-4 sm:mb-6 md:mb-0 order-2 md:order-1">
                        <div className="border-border/50 relative rounded-xl sm:rounded-2xl md:rounded-3xl aspect-video dark:from-zinc-700">
                            <video 
                                src="https://1kl8egk7md.ufs.sh/f/I3tdzkulYJ0GsXxRteZBK1jYgzfUuy9mpxFkvLA5HoRC4teT" 
                                className="rounded-xl sm:rounded-[15px] w-full h-auto" 
                                controls={false}
                                autoPlay
                                muted
                                loop
                            />
                        </div>
                    </div>

                    <div className="relative space-y-3 sm:space-y-4 order-1 md:order-2">
                        <p 
                            className="text-sm sm:text-base md:text-lg text-muted-foreground dark:text-gray-300"
                            dangerouslySetInnerHTML={{ __html: t('featureTwo.description1') }}
                        >
                        </p>
                        <p className="text-sm sm:text-base md:text-lg text-muted-foreground dark:text-gray-300">
                            {t('featureTwo.description2')}
                        </p>
                    </div>
                </div>
            </div>
        </section>
    )
}
