from django.shortcuts import render
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import stripe
import os
from .models import UserSubscription, SubscriptionUsage
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from datetime import datetime
from .middleware import AuthenticationMiddleware
import jwt
stripe.api_key = os.getenv('STRIPE_SECRET_KEY')

# Create your views here.

# CREATE CHECKOUT SESSION
class CreateCheckoutSession(APIView):
    def post(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            # Extract and verify token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = middleware.verify_token_sync(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')
       
            if not user_id:
                return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

            subscription_type = request.data.get('subscriptionType')  # 'monthly' or 'yearly'
        
          
            # Get or create subscription record
            subscription, created = UserSubscription.objects.get_or_create(
                user_id=user_id
            )
   
            # Verify if customer exists in Stripe
            if subscription.stripe_customer_id:
                try:
                    # Try to retrieve the customer
                    stripe.Customer.retrieve(subscription.stripe_customer_id)
                except stripe.error.InvalidRequestError:
                    # Customer doesn't exist in Stripe, create new one
                    print(f"Customer {subscription.stripe_customer_id} not found in Stripe, creating new customer")
                    subscription.stripe_customer_id = None

            # Create new customer if needed
            if not subscription.stripe_customer_id:
                customer = stripe.Customer.create(
                    metadata={'user_id': user_id}
                )
                subscription.stripe_customer_id = customer.id
                subscription.save()

            if subscription_type == 'monthly':
                price_id = os.getenv('STRIPE_MONTHLY_PRICE_ID')
                subscription_type_name = 'Monthly Subscription'
            else:
                price_id = os.getenv('STRIPE_YEARLY_PRICE_ID')
                subscription_type_name = 'Yearly Subscription'

            try:
                checkout_session = stripe.checkout.Session.create(
                    customer=subscription.stripe_customer_id,
                    success_url=f"{request.data.get('successUrl')}?session_id={{CHECKOUT_SESSION_ID}}",
                    cancel_url=request.data.get('cancelUrl'),
                    payment_method_types=['card'],
                    mode='subscription',
                    line_items=[{
                        'price': price_id,
                        'quantity': 1,
                    }],
                    metadata={
                        'user_id': user_id,
                        'subscription_type': subscription_type
                    },
                    subscription_data={
                        'metadata': {
                            'user_id': user_id,
                            'subscription_type': subscription_type
                        }
                    }
                )
               
                return Response({'sessionId': checkout_session.id})
            except Exception as e:
                return Response({'error': str(e)}, status=400)
        except Exception as e:
            return Response({'error': str(e)}, status=400)


# -------- CHECK SUBSCRIPTION STATUS -------- FREE TRIAL LIMITS
class CheckSubscriptionStatus(APIView):
    def get(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            # Extract and verify token
            auth_token = auth_header.replace('Bearer ', '')
            print("Token received:", auth_token[:10] + "...")  # Debug token (first 10 chars)
            middleware = AuthenticationMiddleware(None)
            claims = middleware.verify_token_sync(auth_token)
     
            user_id = claims.get('sub') or claims.get('user.id')
            print("User ID status:", user_id)  # Debug user_id
            if not user_id:
                return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

            subscription = UserSubscription.objects.get(user_id=user_id)
            print("Subscription found:", subscription)  # Debug subscription
            usage, _ = SubscriptionUsage.objects.get_or_create(user_id=user_id)
            print("Usage found:", usage)  # Debug usage
            
            # Define limits based on subscription type
            if subscription.subscription_status == 'active':
                search_limit = -1  # Use -1 to indicate unlimited
                chat_limit = -1
            else:
                search_limit = 10  # Free trial limits
                chat_limit = 10
            
            return Response({
                'subscription_type': subscription.subscription_type,
                'status': subscription.subscription_status,
                'current_period_end': subscription.subscription_end_date,
                'search': {
                    'usage': usage.search_count,
                    'limit': search_limit
                },
                'chat': {
                    'usage': usage.chat_count,
                    'limit': chat_limit
                }
            })
        except UserSubscription.DoesNotExist:
            return Response({
                'subscription_type': 'none',
                'status': 'inactive',
                'current_period_end': None,
                'search': {'usage': 0, 'limit': 10},
                'chat': {'usage': 0, 'limit': 10}
            })
        except Exception as e:
            return Response({'error': str(e)}, status=400)

#-------------------CANCEL SUBSCRIPTION -------------------
class CancelSubscription(APIView):
    def post(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, status=status.HTTP_401_UNAUTHORIZED)
        print('cancel subscription:', auth_header)
        try:
            # Extract and verify token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = middleware.verify_token_sync(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')
           
            if not user_id:
                return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

            subscription = UserSubscription.objects.get(user_id=user_id)
        
            if not subscription.subscription_id:
                return Response({'error': 'No active subscription found'}, status=400)
            
            # Cancel the subscription at period end in Stripe
            stripe_subscription = stripe.Subscription.modify(
                subscription.subscription_id,
                cancel_at_period_end=True
            )
            
            # Update local subscription record
            subscription.will_cancel = True
            subscription.subscription_end_date = datetime.fromtimestamp(stripe_subscription.current_period_end)
            subscription.save()
            
            return Response({
                'message': 'Subscription will be canceled at the end of the billing period',
                'end_date': subscription.subscription_end_date
            })
            
        except UserSubscription.DoesNotExist:
            return Response({'error': 'Subscription not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

#-------------------REACTIVATE SUBSCRIPTION -------------------
class ReactivateSubscription(APIView):
    def post(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            # Extract and verify token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = middleware.verify_token_sync(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')

            if not user_id:
                return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

            subscription = UserSubscription.objects.get(user_id=user_id)
            
            if not subscription.subscription_id:
                return Response({'error': 'No subscription found to reactivate'}, status=400)
            
            # Reactivate the subscription in Stripe
            stripe_subscription = stripe.Subscription.modify(
                subscription.subscription_id,
                cancel_at_period_end=False
            )
            
            # Update local subscription record
            subscription.will_cancel = False
            subscription.subscription_status = stripe_subscription.status
            subscription.subscription_end_date = datetime.fromtimestamp(stripe_subscription.current_period_end)
            subscription.save()
            
            return Response({
                'message': 'Subscription successfully reactivated',
                'status': subscription.subscription_status,
                'current_period_end': subscription.subscription_end_date
            })
            
        except UserSubscription.DoesNotExist:
            return Response({'error': 'Subscription not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

# ------------ WEBHOOKS ------------ CONSTANTLY UPDATING SUBSCRIPTION STATUS 
@csrf_exempt
def stripe_webhook(request):
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    webhook_secret = os.getenv('STRIPE_WEBHOOK_SECRET')


    if not webhook_secret:
        print("Error: STRIPE_WEBHOOK_SECRET not configured")
        return JsonResponse({'error': 'Webhook secret not configured'}, status=500)
    
    if not sig_header:
        print("Error: No Stripe signature header found")
        return JsonResponse({'error': 'No Stripe signature found'}, status=400)

    try:
        # Convert payload to string if it's bytes
        if isinstance(payload, bytes):
            payload = payload.decode('utf-8')
            
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
        
      

        # Handle different event types
        if event['type'] == 'checkout.session.completed':
            session = event.data.object
            print(f"Processing checkout session: {session.id}")
            if session.subscription:
                subscription = stripe.Subscription.retrieve(session.subscription)
                handle_subscription_created(subscription)
                
        elif event['type'] == 'customer.subscription.updated':
            subscription = event.data.object
            print(f"Processing subscription update: {subscription.id}")
            handle_subscription_updated(subscription)
            
        elif event['type'] == 'customer.subscription.deleted':
            subscription = event.data.object
            print(f"Processing subscription deletion: {subscription.id}")
            handle_subscription_deleted(subscription)
            
        elif event['type'] == 'invoice.payment_failed':
            invoice = event.data.object
            print(f"Processing failed payment for invoice: {invoice.id}")
            handle_payment_failed(invoice)
            
        elif event['type'] == 'invoice.payment_succeeded':
            invoice = event.data.object
            print(f"Processing successful payment for invoice: {invoice.id}")
            handle_payment_succeeded(invoice)
        
        return JsonResponse({'status': 'success'})
        
    except ValueError as e:
        print(f"Invalid payload: {str(e)}")
        return JsonResponse({'error': 'Invalid payload'}, status=400)
    except stripe.error.SignatureVerificationError as e:
        print(f"Invalid signature: {str(e)}")
        return JsonResponse({'error': 'Invalid signature'}, status=400)
    except Exception as e:
        print(f"Webhook error: {str(e)}")
        return JsonResponse({'error': str(e)}, status=400)


def handle_subscription_created(subscription):
    try:
        # First verify we have the required metadata
        user_id = subscription.metadata.get('user_id')
        subscription_type = subscription.metadata.get('subscription_type')
        
        if not user_id:
            print(f"No user_id found in metadata for subscription {subscription.id}")
            # Try to get metadata from customer's subscriptions
            customer_subscriptions = stripe.Subscription.list(
                customer=subscription.customer,
                status='all'
            )
            
            target_subscription = next(
                (sub for sub in customer_subscriptions.data if sub.id == subscription.id),
                None
            )
            
            if target_subscription and target_subscription.metadata.get('user_id'):
                user_id = target_subscription.metadata.get('user_id')
                subscription_type = target_subscription.metadata.get('subscription_type')
            else:
                print(f"Could not find valid user_id for subscription {subscription.id}")
                return
        
        if not user_id:
            print(f"Unable to process subscription {subscription.id} - no user_id found")
            return
            
        print(f"Processing subscription for user_id: {user_id}, type: {subscription_type}")
        
        # Get or create the user subscription
        user_subscription, created = UserSubscription.objects.get_or_create(
            user_id=user_id,
            defaults={
                'stripe_customer_id': subscription.customer,
                'subscription_status': 'inactive'
            }
        )
        
        # Update subscription details
        user_subscription.subscription_id = subscription.id
        user_subscription.subscription_type = subscription_type  # 'monthly' or 'yearly'
        user_subscription.subscription_status = 'active'
        user_subscription.save()
        
        print(f"Successfully created {subscription_type} subscription for user {user_id}")
        
    except Exception as e:
        print(f"Error in handle_subscription_created: {str(e)}")
        print(f"Subscription data: {subscription}")
        print(f"Metadata: {subscription.metadata}")

def handle_subscription_updated(subscription):
    user_id = subscription.metadata.get('user_id')
    subscription_type = subscription.metadata.get('subscription_type')
    
    try:
        user_subscription = UserSubscription.objects.get(user_id=user_id)
        user_subscription.subscription_status = subscription.status
        user_subscription.subscription_type = subscription_type
        user_subscription.will_cancel = subscription.cancel_at_period_end
        if subscription.cancel_at_period_end:
            user_subscription.subscription_end_date = datetime.fromtimestamp(subscription.current_period_end)
        user_subscription.save()
        
        print(f"Successfully updated subscription status to {subscription.status} for user {user_id}")
        if subscription.cancel_at_period_end:
            print(f"Subscription is set to cancel at {user_subscription.subscription_end_date}")
    except UserSubscription.DoesNotExist:
        print(f"No subscription found for user {user_id}")
    except Exception as e:
        print(f"Error in handle_subscription_updated: {str(e)}")

def handle_subscription_deleted(subscription):
    user_id = subscription.metadata.get('user_id')
    
    try:
        user_subscription = UserSubscription.objects.get(user_id=user_id)
        user_subscription.subscription_status = 'inactive'
        user_subscription.subscription_id = None
        user_subscription.subscription_type = 'none'
        user_subscription.save()
        
        print(f"Successfully deleted subscription for user {user_id}")
    except UserSubscription.DoesNotExist:
        print(f"No subscription found for user {user_id}")
    except Exception as e:
        print(f"Error in handle_subscription_deleted: {str(e)}")

def handle_payment_failed(invoice):
    subscription_id = invoice.subscription
    if subscription_id:
        try:
            subscription = stripe.Subscription.retrieve(subscription_id)
            user_id = subscription.metadata.get('user_id')
            
            if not user_id:
                print(f"No valid user_id found in metadata for subscription {subscription_id}")
                return
            
            user_subscription = UserSubscription.objects.get(user_id=user_id)
            user_subscription.subscription_status = 'past_due'
            user_subscription.save()
            
            print(f"Successfully updated payment failed status for user {user_id}")
        except Exception as e:
            print(f"Error in handle_payment_failed: {str(e)}")

def handle_payment_succeeded(invoice):
    subscription_id = invoice.subscription
    if subscription_id:
        try:
            subscription = stripe.Subscription.retrieve(subscription_id)
            user_id = subscription.metadata.get('user_id')
            
            if not user_id:
                print(f"No valid user_id found in metadata for subscription {subscription_id}")
                return
            
            user_subscription = UserSubscription.objects.get(user_id=user_id)
            user_subscription.subscription_status = 'active'
            user_subscription.save()
            
            print(f"Successfully updated payment succeeded status for user {user_id}")
        except Exception as e:
            print(f"Error in handle_payment_succeeded: {str(e)}")








