# Generated by Django 5.1.3 on 2024-11-11 15:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0002_remove_usersubscription_chat_count_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='usersubscription',
            old_name='chat_subscription_id',
            new_name='subscription_id',
        ),
        migrations.RenameField(
            model_name='usersubscription',
            old_name='chat_subscription_status',
            new_name='subscription_status',
        ),
        migrations.RemoveField(
            model_name='usersubscription',
            name='search_subscription_id',
        ),
        migrations.RemoveField(
            model_name='usersubscription',
            name='search_subscription_status',
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='subscription_type',
            field=models.CharField(choices=[('none', 'No Subscription'), ('monthly', 'Monthly Subscription'), ('yearly', 'Yearly Subscription')], default='none', max_length=50),
        ),
    ]
