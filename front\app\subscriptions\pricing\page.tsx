'use client';

import { useState } from 'react';
import { <PERSON>rk<PERSON>, AlertCircle, Check } from 'lucide-react';
import NavBar from '@/components/header/nav-bar';
import LeftSidebar from '@/components/left_tab/LeftTab';
import { useTheme } from '@/components/theme/theme-provider';
import CheckoutForm from '@/app/subscriptions/components/CheckoutForm';
import { SubscriptionType } from '@/app/utils/stripe';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/app/providers/language-provider';


export default function PricingPage() {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const [error] = useState('');
  const billingCycle: SubscriptionType = 'monthly';
  const [isLeftTabVisible, setIsLeftTabVisible] = useState(true);

  return (
    <>
      <div className="flex flex-col min-h-screen">
        {isLeftTabVisible && (
          <LeftSidebar 
            onHide={() => setIsLeftTabVisible(false)}
          />
        )}
        <div className="fixed top-0 left-0 right-0 z-10">
          <NavBar 
            isLeftTabVisible={isLeftTabVisible} 
            onShowLeftTab={() => setIsLeftTabVisible(true)} 
          />
        </div>
        
        <div className={`flex flex-1 pt-12`}>
          <main className={`flex-1 transition-all duration-300 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
            <div className={`min-h-screen ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
              <section className="py-16 md:py-32">
                <div className="mx-auto max-w-6xl px-6">
                 

                  {error && (
                    <div className="mt-4 max-w-md mx-auto p-3 bg-red-50 text-red-600 rounded-md flex items-center">
                      <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                      <span>{error}</span>
                    </div>
                  )}
                  

                  <div className="mt-8 grid gap-6 [--color-card:var(--color-muted)] *:border-none *:shadow-none md:mt-20 md:grid-cols-2 dark:[--color-muted:var(--color-zinc-900)]">
                    <Card className="flex flex-col">
                      <CardHeader>
                        <CardTitle className="font-medium">Free</CardTitle>
                        <span className="my-3 block text-2xl font-semibold">$0 / mo</span>
                        <CardDescription className="text-sm"></CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-4">
                        <hr className="border-dashed" />

                        <ul className="list-outside space-y-3 text-sm">
                          {[t('pricing.freeFeature1'), t('pricing.freeFeature3')].map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <Check className="size-3" />
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>

                     
                    </Card>

                    <Card className="relative">
                      <span className="absolute inset-x-0 -top-3 mx-auto flex h-6 w-fit items-center rounded-full from-neutral-400 to-neutral-300 bg-gradient-to-br px-3 py-1 text-xs font-medium text-neutral-950 ring-1 ring-inset ring-white/20 ring-offset-1 ring-offset-neutral-950/5">Popular</span>

                      <div className="flex flex-col">
                        <CardHeader>
                          <CardTitle className="font-medium">Pro</CardTitle>
                          <span className="my-3 block text-2xl font-semibold">$15 / mo</span>
                          <CardDescription className="text-sm">{t('pricing.proPlanDescMonthly')}</CardDescription>
                        </CardHeader>

                        <CardContent className="space-y-4">
                          <hr className="border-dashed" />
                          <ul className="list-outside space-y-3 text-sm">
                            {[
                            t('pricing.proFeature1'),
                            t('pricing.proFeature2'),
                            t('pricing.proFeature3'),
                            t('pricing.proFeature4'),
                            t('pricing.proFeature5')
                          ].map((item, index) => (
                              <li key={index} className="flex items-center gap-2">
                                <Check className="size-3" />
                                {item}
                              </li>
                            ))}
                          </ul>
                        </CardContent>

                        <CardFooter>
                          <CheckoutForm
                            subscriptionType={billingCycle}
                            buttonText={
                              <div className="flex items-center space-x-2">
                                <Sparkles className="w-5 h-5" />
                                <span>{t('pricing.subscribeMonthly')}</span>
                              </div>
                            }
                            className="w-full bg-neutral-900 hover:bg-neutral-800 text-white dark:bg-neutral-800 dark:hover:bg-neutral-700"
                          />
                        </CardFooter>
                      </div>
                    </Card>
                  </div>
                </div>
              </section>
            </div>
          </main>
        </div>
      </div>
    </>
  );
}
