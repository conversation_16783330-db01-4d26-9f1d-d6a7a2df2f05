'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/components/theme/theme-provider';

import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { API_URL } from '@/app/utils/decode_api';

// Import the PDF viewer components from pdf_render.tsx
import { PdfViewer } from './document_render/pdf_render';

import { YouTubePlayer } from './youtube_components/youtube_player';

// Remove PDF.js imports that are now in pdf_render.tsx
// Remove PDF styles that are now in pdf_render.tsx

interface Project {
  id: number;
  ID?: number;           // Add uppercase ID for Go field
  name: string;
  Name?: string;         // Add uppercase Name
  description: string;
  Description?: string;  // Add uppercase Description
  file_url: string;
  FileURL?: string;      // Add uppercase FileURL
  created_at?: string;
  updated_at?: string;
  CreatedAt?: string;    // Add uppercase CreatedAt
  UpdatedAt?: string;    // Add uppercase UpdatedAt
  is_youtube?: boolean;
  IsYouTube?: boolean;   // Add uppercase IsYouTube for Go field
  med_space_id?: number;
  MedSpaceID?: number;
}

interface MedSpace {
  id: number;
  name: string;
  description: string;
}

interface FileViewerProps {
  projectId?: string;
  medspaceId?: string;
}

function FileViewerContent({ projectId, medspaceId }: FileViewerProps) {
  const { theme } = useTheme();
  const { isLoaded, isSignedIn, getToken } = useAuth();
  const router = useRouter();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [, setProjects] = useState<Project[]>([]);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoadingPdf, setIsLoadingPdf] = useState(false);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthLoading, setIsAuthLoading] = useState(true);
  const [youtubeUrl, setYoutubeUrl] = useState<string | null>(null);
  const [isLoadingVideo, setIsLoadingVideo] = useState(false);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [activeMedSpace, setActiveMedSpace] = useState<MedSpace | null>(null);
  const [isShowingAllProjects, setIsShowingAllProjects] = useState(true);
  const [, setVideoId] = useState<string | null>(null);
  const [availableLanguages, setAvailableLanguages] = useState<string[]>(['en']);
  const [selectedLanguage, setSelectedLanguage] = useState('en');

  // File type state
  const [fileType, setFileType] = useState<'pdf' | 'youtube' | 'unknown'>('unknown');

  // New authenticatedFetch function based on the pattern in mindmap.tsx
  const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
    try {
      // Get a fresh token for each request
      const token = await getToken();
      
      if (!token) {
        throw new Error('No authentication token available');
      }
      
      // Create headers with authentication
      const headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`
      };
      
      // Make the request
      const response = await fetch(url, {
        ...options,
        headers
      });
      
      return response;
    } catch (error) {
      console.error('Authentication error in fetch:', error);
      throw error;
    }
  };

  useEffect(() => {
    if (selectedProject && (selectedProject.id !== undefined || selectedProject.ID !== undefined)) {
      // Only fetch details if we have a selected project ID that's just an ID,
      // not if we already have complete project details
      const projectId = selectedProject.id || selectedProject.ID;
      const projectIdStr = typeof projectId === 'number' ? projectId.toString() : projectId;
      
      // Only fetch details if we don't already have the file_url
      // This prevents the infinite loop
      if (!selectedProject.file_url && !selectedProject.FileURL) {
        if (projectIdStr !== undefined && projectIdStr !== null) {
          fetchProjectDetails(projectIdStr);
        }
      } else {
        // If we already have a file URL, check if it's a YouTube URL
        const fileUrl = selectedProject.file_url || selectedProject.FileURL;
        if (fileUrl) {
          const youtubeRegex = /(?:youtube\.com|youtu\.be)/i;
          const isYoutube = youtubeRegex.test(fileUrl) || selectedProject.is_youtube || selectedProject.IsYouTube;
          
          if (isYoutube) {
            // It's a YouTube URL, set state accordingly
            setFileType('youtube');
            setYoutubeUrl(fileUrl);
            setPdfUrl(null);
            
            // Try to extract video ID
            const videoId = extractVideoId(fileUrl);
            if (videoId) {
              setVideoId(videoId);
              setAvailableLanguages(['en']);
              setSelectedLanguage('en');
            }
          }
        }
      }
    }
  // Only depend on project ID and file URL to prevent unnecessary fetches
  }, [selectedProject?.id, selectedProject?.ID, selectedProject?.file_url, selectedProject?.FileURL]);

  useEffect(() => {
    // Helper function to clean up blob URLs to prevent memory leaks
    const cleanupBlobUrl = (url: string | null) => {
      if (url && url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
    };
    
    // Check if there's a file URL using either lowercase or uppercase property
    const fileUrl = selectedProject?.file_url || selectedProject?.FileURL;
    
    if (selectedProject && fileUrl) {
      setIsLoadingPdf(true);
      setPdfError(null);
      
      // Always get a proxy URL through our backend
      const projectIdToUse = selectedProject.id || selectedProject.ID;
      if (!projectIdToUse) {
        console.error("No project ID found");
        setPdfError("Project ID not found. Please try again or contact support.");
        setIsLoadingPdf(false);
        return;
      }
      
      console.log(`Loading content for project ID: ${projectIdToUse}`);
      getSignedURL(projectIdToUse)
        .then(signedUrl => {
          if (signedUrl) {
            console.log("Using proxied URL:", signedUrl);
            // Clean up any previous blob URL
            cleanupBlobUrl(pdfUrl);
            setPdfUrl(signedUrl);
          } else {
            setPdfError("Unable to get access to the file. Please try again or contact support.");
          }
        })
        .catch(error => {
          console.error("Error getting signed URL:", error);
          setPdfError(`Error loading file: ${error.message}`);
        })
        .finally(() => {
          setIsLoadingPdf(false);
        });
    } else {
      // Clean up any previous blob URL
      cleanupBlobUrl(pdfUrl);
      setPdfUrl(null);
      setIsLoadingPdf(false);
    }
    
    // Cleanup function to revoke any blob URLs when component unmounts or dependencies change
    return () => {
      cleanupBlobUrl(pdfUrl);
    };
  }, [selectedProject]); // Depend on the entire selectedProject object to catch both id and ID changes

  useEffect(() => {
   
    if (projectId) {
      // Fetch the specific project when projectId is provided
      fetchProjectDetails(projectId);
      
      // If medspaceId is provided, set it as the active medspace
      if (medspaceId && medspaceId !== '0') {
        // We'll update activeMedSpace when fetching medspaces
        setIsShowingAllProjects(false);
      } else {
        setIsShowingAllProjects(true);
      }
    } else {
      // Otherwise fetch all projects and select the first one
      fetchProjects();
    }
  }, [projectId, medspaceId]);
  
  useEffect(() => {
    if (isLoaded) {
      setIsAuthLoading(false);
      if (!isSignedIn) {
        router.push('/sign-in');
        return;
      }
      
      // First fetch medspaces to ensure we have the correct activeMedSpace
      fetchMedSpaces().then(() => {
        // Then fetch projects (which may depend on activeMedSpace)
        fetchProjects().then(() => {
          // If projectId is provided, fetch its details directly
          if (projectId) {
            console.log("Directly fetching project details for ID:", projectId);
            fetchProjectDetails(projectId);
          }
        });
      });
    }
  }, [isLoaded, isSignedIn, router, projectId, medspaceId]);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      
      
      
      // Build the URL with the appropriate query parameters
      let url = `${API_URL}/api/decode/projects`;
      
      // If a MedSpace is selected, add the med_space_id parameter
      if (activeMedSpace && !isShowingAllProjects) {
        url += `?med_space_id=${activeMedSpace.id}`;
      } else if (!isShowingAllProjects) {
        // If showing projects not in any MedSpace
        url += `?show_all=false`;
      } else {
        // If showing all projects
        url += `?show_all=true`;
      }
      
      const response = await authenticatedFetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }
      
      const data = await response.json();
   
      
    
      // Make sure we're correctly accessing the projects array
      if (!data.projects) {
        console.error("No projects key in API response:", data);
        // Check if the data itself is an array instead
        if (Array.isArray(data)) {
          setProjects(data);
          if (data.length > 0) {
            setSelectedProject(data[0]);
          }
        } else {
          setProjects([]);
        }
        return;
      }
      
      // Convert the projects to use lowercase field names consistently
      const normalizedProjects = data.projects.map((project: Project) => ({
        id: project.ID || project.id,
        name: project.Name || project.name,
        description: project.Description || project.description,
        file_url: project.FileURL || project.file_url,
        created_at: project.CreatedAt || project.created_at,
        updated_at: project.UpdatedAt || project.updated_at,
        is_youtube: project.IsYouTube || project.is_youtube || false,
        med_space_id: project.MedSpaceID || project.med_space_id
      }));
      
      
      
      // Now filter the normalized projects
      const validProjects = normalizedProjects.filter((project: Project) => project && project.id !== undefined);
      
     
      
      setProjects(validProjects);
      
      // Only set selected project if we have valid projects and it has an ID
      if (validProjects.length > 0 && validProjects[0].id !== undefined) {
        console.log("Setting selected project:", validProjects[0]);
        setSelectedProject(validProjects[0]);
      } else {
        console.warn("No valid projects found to select");
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      setProjects([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProjectDetails = async (projectId: string | number | null) => {
    if (!projectId) {
      console.log("No project ID provided to fetchProjectDetails");
      setSelectedProject(null);
      setPdfUrl(null);
      setYoutubeUrl(null);
      // Remove wordUrl setting
      setFileType('unknown');
      return;
    }
    
    console.log("Fetching details for project ID:", projectId);
    setIsLoadingPdf(true);
    setPdfError(null);
    setIsLoadingVideo(true);
    setVideoError(null);
    // Remove isLoadingWord and wordError setting
    
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('Failed to get authentication token.');
      }
      
      const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch project details: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("Project details response:", data);
      
      if (data.project) {
        const projectData = data.project;
        
        // Create normalized project object
        const normalizedProject = {
          id: projectData.id || projectData.ID,
          name: projectData.name || projectData.Name,
          description: projectData.description || projectData.Description,
          file_url: projectData.file_url || projectData.FileURL,
          created_at: projectData.created_at || projectData.CreatedAt,
          updated_at: projectData.updated_at || projectData.UpdatedAt,
          is_youtube: projectData.is_youtube || projectData.IsYouTube
        };
        
        setSelectedProject(normalizedProject);
        
        // Handle file type detection
        const fileUrl = normalizedProject.file_url;
        if (fileUrl) {
          const youtubeRegex = /(?:youtube\.com|youtu\.be)/i;
          const isYoutube = youtubeRegex.test(fileUrl) || normalizedProject.is_youtube;
          
          // Remove Word document detection
          
          // Check if it's a PDF
          const pdfRegex = /\.pdf$/i;
          const isPdf = pdfRegex.test(fileUrl);
          
          // Don't set a default file type here, determine it based on conditions below
          // Remove isWordDocument setting
        
          if (isYoutube) {
            setFileType('youtube');
            setYoutubeUrl(fileUrl);
            setPdfUrl(null);
            // Remove wordUrl setting
            
            // Try to extract video ID
            const videoId = extractVideoId(fileUrl);
            if (videoId) {
              setVideoId(videoId);
              setAvailableLanguages(['en']);
              setSelectedLanguage('en');
            }
          } else if (isPdf) {
            // Explicitly handle PDF files
            setFileType('pdf');
            setYoutubeUrl(null);
            // Remove wordUrl setting
          } else {
            // Default to unknown for other file types
            setFileType('unknown');
            setYoutubeUrl(null);
            setPdfUrl(null);
            // Remove wordUrl setting
          }
        } else {
          console.log("No file URL in project data");
          setYoutubeUrl(null);
          setPdfUrl(null);
          // Remove wordUrl setting
          setFileType('unknown');
        }
      } else {
        console.error("No project data in response:", data);
        setSelectedProject(null);
      }
    } catch (error) {
      console.error("Error fetching project details:", error);
      setSelectedProject(null);
      setPdfError(error instanceof Error ? error.message : "Failed to fetch project details");
    } finally {
      setIsLoadingPdf(false);
      setIsLoadingVideo(false);
      // Remove isLoadingWord setting
    }
  };

  // Update the getSignedURL function to use the new pattern
  const getSignedURL = async (projectId: string | number) => {
    // If fileUrl is empty or null, return null
    if (!projectId) {
      console.error("Project ID is empty");
      return null;
    }
    
    try {
      // Get a fresh token for each request using getToken()
      const token = await getToken();
      
      if (!token) {
        console.error("No authentication token available");
        setPdfError("Authentication failed. Please sign in again.");
        return null;
      }
      
      // IMPORTANT: Never try to directly access Google Cloud Storage URLs from the browser
      // Always proxy through our backend to avoid CORS issues
      
      // Get the file URL from the backend
      console.log(`Fetching file view URL for project ID: ${projectId}`);
      const response = await authenticatedFetch(`${API_URL}/api/decode/fileview/file/${projectId}`);
      
      if (!response.ok) {
        console.error(`Failed to get file URL: ${response.status}`);
        setPdfError(`Failed to get file URL: ${response.status}. Please try again or contact support.`);
        return null;
      }
      
      const data = await response.json();
      
      // Check if it's a YouTube video first (handled differently)
      if (data.is_youtube) {
        console.log("YouTube video detected, extracting video ID from:", data.file_url);
        // Extract the video ID instead of returning the URL directly
        const videoId = extractVideoId(data.file_url);
        if (videoId) {
          // Return the embed URL which doesn't cause CORS issues
          return `https://www.youtube.com/embed/${videoId}`;
        } else {
          console.error("Failed to extract YouTube video ID from:", data.file_url);
          return data.file_url;
        }
      }
      
      // For PDF/other files, extract the object name from the signed URL or use object_name if provided
      let objectName;
      
      // First, check if we have an object name directly in the response
      if (data.file_name) {
        objectName = data.file_name;
        console.log("Using file_name from response:", objectName);
      } else if (data.signed_url) {
        // Try to extract from the signed URL if available
        // Updated regex to handle various URL formats
        const objectNameMatch = data.signed_url.match(/storage\.googleapis\.com\/[^\/]+\/([^?]+)(\?|$)/);
        if (objectNameMatch && objectNameMatch[1]) {
          objectName = decodeURIComponent(objectNameMatch[1]); // Decode URL-encoded characters
       
        } else {
          console.error("Could not extract object name from URL:", data.signed_url);
          objectName = data.signed_url; // Fallback to using the entire URL
        }
      } else {
        console.error("No signed URL or file_name in response:", data);
        setPdfError("No file URL returned from server. Please try again or contact support.");
        return null;
      }
      
      // Create the proxy URL with the sanitized object name
      const proxyUrl = `${API_URL}/api/decode/fileview/proxy?object=${encodeURIComponent(objectName)}`;
      console.log("Created proxy URL:", proxyUrl);
      
      try {
        // Fetch the content through the proxy with authentication
        const proxyResponse = await fetch(proxyUrl, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!proxyResponse.ok) {
          const errorText = await proxyResponse.text();
          console.error(`Proxy error: ${proxyResponse.status}`, errorText);
          setPdfError(`File access error: ${proxyResponse.status}. Please try again or contact support.`);
          return null;
        }
        
        // Create a blob URL from the response
        const blob = await proxyResponse.blob();
        const blobUrl = URL.createObjectURL(blob);
        
        return blobUrl;
      } catch (proxyError) {
        console.error("Error fetching from proxy:", proxyError);
        setPdfError("Failed to access file through proxy. Please try again or contact support.");
        return null;
      }
    } catch (error) {
      console.error("Error setting up file access:", error);
      setPdfError("Failed to access file. Please try again or contact support.");
      return null;
    }
  };

  // Add a function to extract video ID from YouTube URL
  const extractVideoId = (url: string): string | null => {
    if (!url) return null;
    
    console.log("Extracting YouTube video ID from URL:", url);
    
    try {
      // First check if it's already an embed URL
      if (url.includes('/embed/')) {
        const embedMatch = url.match(/\/embed\/([^?&#]+)/);
        if (embedMatch && embedMatch[1] && embedMatch[1].length === 11) {
          console.log("Successfully extracted YouTube video ID from embed URL:", embedMatch[1]);
          return embedMatch[1];
        }
      }
      
      // Standard format: https://www.youtube.com/watch?v=VIDEO_ID
      // Short format: https://youtu.be/VIDEO_ID
      // Embed format: https://www.youtube.com/embed/VIDEO_ID
      // With timestamp: https://www.youtube.com/watch?v=VIDEO_ID&t=1s
      
      // Standard YouTube URL regex
      const regExp = /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
      const match = url.match(regExp);
      
      if (match && match[1] && match[1].length === 11) {
        console.log("Successfully extracted YouTube video ID:", match[1]);
        return match[1];
      }
      
      // If we couldn't match, check if it's a simple ID
      if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {
        console.log("URL appears to be a direct YouTube video ID");
        return url;
      }
      
      console.warn("Could not extract YouTube video ID from URL:", url);
      return null;
    } catch (error) {
      console.error("Error extracting YouTube video ID:", error);
      return null;
    }
  };

  // Handle language change for YouTube videos
  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
  };

  // Add fetchMedSpaces function
  const fetchMedSpaces = async () => {
    try {
      const response = await authenticatedFetch(`${API_URL}/api/decode/medspaces`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch medspaces');
      }
      
      const data = await response.json();
      
      if (data.medspaces) {
        // If medspaceId is provided, find and set the corresponding medspace
        if (medspaceId && medspaceId !== '0') {
          const selectedMedSpace = data.medspaces.find((ms: MedSpace) => ms && ms.id && ms.id.toString() === medspaceId);
          if (selectedMedSpace) {
            setActiveMedSpace(selectedMedSpace);
            setIsShowingAllProjects(false);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching medspaces:", error);
    }
  };

  // Function to try loading with a signed URL if direct access fails
  async function handlePdfAccessDenied() {
    if (!selectedProject || (!selectedProject.file_url && !selectedProject.FileURL)) {
      setPdfError("No file URL available for this project.");
      return;
    }
    
    setIsLoadingPdf(true);
    setPdfError(null);
    
    try {
      console.log("Handling PDF access denied, retrying with proxy...");
      
      // Get a fresh token
      const token = await getToken();
      if (!token) {
        setPdfError("Authentication failed. Please sign in again.");
        return;
      }
      
      // Force using the project ID approach to get a proxied URL
      const projectId = selectedProject.id || selectedProject.ID;
      if (!projectId) {
        setPdfError("Project ID not found. Please try again.");
        return;
      }
      
      console.log(`Retrying file access for project ID: ${projectId}`);
      
      // Try up to 3 times with increasing delays
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          // Call our backend proxy directly
          const response = await authenticatedFetch(`${API_URL}/api/decode/fileview/file/${projectId}`);
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error(`Server error response (attempt ${attempt}):`, errorText);
            
            if (attempt < 3) {
              // Wait before retrying (exponential backoff)
              const delay = attempt * 1000; // 1s, 2s, 3s
              console.log(`Retrying after ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
              continue;
            }
            
            setPdfError(`Server error: ${response.status}. Please try again later or contact support.`);
            return;
          }
          
          const data = await response.json();
          console.log("File view retry response:", data);
          
          if (!data.signed_url && !data.file_name) {
            setPdfError("No file URL returned from server. Please try again or contact support.");
            return;
          }
          
          // Extract the object name from the response
          let objectName;
          
          if (data.file_name) {
            objectName = data.file_name;
          } else if (data.signed_url) {
            // Try to extract from the signed URL if available
            const objectNameMatch = data.signed_url.match(/storage\.googleapis\.com\/[^\/]+\/([^?]+)(\?|$)/);
            if (objectNameMatch && objectNameMatch[1]) {
              objectName = decodeURIComponent(objectNameMatch[1]);
            } else {
              setPdfError("Could not process file URL. Please contact support.");
              return;
            }
          } else {
            setPdfError("Invalid file data returned. Please contact support.");
            return;
          }
          
          const proxyUrl = `${API_URL}/api/decode/fileview/proxy?object=${encodeURIComponent(objectName)}`;
          console.log("Using proxy URL for retry:", proxyUrl);
          
          // Call the proxy with the token
          const proxyResponse = await fetch(proxyUrl, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (!proxyResponse.ok) {
            const errorText = await proxyResponse.text();
            console.error(`Proxy error response:`, errorText);
            setPdfError(`File access error: ${proxyResponse.status}. Please try again or contact support.`);
            return;
          }
          
          // Create a blob URL from the proxy response
          const blob = await proxyResponse.blob();
          
          // Check if the blob is valid (not empty and has the right type)
          if (blob.size === 0) {
            setPdfError("The file appears to be empty. Please try again or contact support.");
            return;
          }
          
          const blobUrl = URL.createObjectURL(blob);
          setPdfUrl(blobUrl);
          
          // Successfully retrieved and processed the file
          return;
        } catch (attemptError) {
          console.error(`Error in attempt ${attempt}:`, attemptError);
          
          if (attempt < 3) {
            // Wait before retrying (exponential backoff)
            const delay = attempt * 1000; // 1s, 2s, 3s
            console.log(`Retrying after ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          
          throw attemptError; // Rethrow to be caught by the outer catch
        }
      }
    } catch (error) {
      console.error("Error in handlePdfAccessDenied:", error);
      setPdfError(error instanceof Error ? error.message : "Failed to get access to this file. Please try again or contact support.");
    } finally {
      setIsLoadingPdf(false);
    }
  }

  if (isAuthLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p className="text-lg">Loading authentication...</p>
      </div>
    );
  }

  if (!isSignedIn) {
    return null;
  }

  return (
    <div className={`h-full w-full max-w-[100%] mx-auto flex flex-col ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-900'}`}>
      
     
      
      {/* Main content */}
      <div className="flex-1 overflow-hidden">
        {isLoading || isAuthLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
          </div>
        ) : fileType === 'youtube' ? (
          // YouTube Video Rendering
          <div className="h-full w-full flex items-center p-4">
            <YouTubePlayer
              youtubeUrl={youtubeUrl}
              isLoadingVideo={isLoadingVideo}
              videoError={videoError}
              availableLanguages={availableLanguages}
              selectedLanguage={selectedLanguage}
              onLanguageChange={handleLanguageChange}
            />
          </div>
        ) : (
          // PDF Rendering (default)
          <div className="h-full w-full">
            <PdfViewer 
              pdfUrl={pdfUrl}
              isLoadingPdf={isLoadingPdf}
              pdfError={pdfError}
              handlePdfAccessDenied={handlePdfAccessDenied}
              handleFileView={(mode) => {
                if (mode === 'external' && pdfUrl) {
                  window.open(pdfUrl, '_blank');
                }
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default function FileViewer({ projectId, medspaceId }: FileViewerProps) {
  return (
    <FileViewerContent projectId={projectId} medspaceId={medspaceId} />
  );
}