'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircle } from 'lucide-react';
import NavBar from '@/components/header/nav-bar';

export default function SuccessPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to subscription management page after a brief delay
    const timeout = setTimeout(() => {
      router.push('/subscriptions/subscription-management');
    }, 5000);

    return () => clearTimeout(timeout);
  }, [router]);

  return (
    <>
      <NavBar />
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-lg text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Subscription Successful!
            </h1>
            
            <p className="text-gray-600 mb-6">
              Thank you for your subscription. Your account has been upgraded, and you now have access to all premium features.
            </p>
            
            <p className="text-sm text-gray-500 mb-6">
              You&apos;ll be redirected to your subscription management page in a few seconds.
            </p>
            
            <button
              onClick={() => router.push('/subscriptions/subscription-management')}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Go to Subscription Management
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
