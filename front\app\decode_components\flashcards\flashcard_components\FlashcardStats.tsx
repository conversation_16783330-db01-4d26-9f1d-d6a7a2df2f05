"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { FlashcardStats, fetchWithTokenRetry } from '../flashcards';
import { API_URL } from '@/app/utils/decode_api';


interface FlashcardStatsProps {
  projectId: string;
}

export default function FlashcardStatsComponent({ projectId }: FlashcardStatsProps) {
  const { getToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<FlashcardStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  // API methods specific to FlashcardStats
  const getFlashcardStats = async (): Promise<FlashcardStats> => {
    try {
      console.log('Fetching flashcard stats for project:', projectId);
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/flashcards/stats?project_id=${projectId}`,
        { method: 'GET' },
        getToken
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error fetching flashcard stats: ${response.status} ${response.statusText}`, errorText);
        
        // Check for specific error types
        if (response.status === 500) {
          console.log('Backend database error detected. There might be an issue with the database schema.');
        }
        
        throw new Error(`Failed to fetch flashcard stats: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Flashcard stats response:', data);
      
      // If stats are directly in the response
      if (typeof data.total_flashcards !== 'undefined') {
        // Match the backend response structure
        return {
          total_cards: data.total_flashcards,
          cards_due: data.due_flashcards || 0,
          cards_learned: data.total_flashcards - (data.due_flashcards || 0),
          average_difficulty: data.mastery_level || 0,
          review_accuracy: data.review_accuracy ? data.review_accuracy / 100 : 0,
          streak: data.current_streak || 0
        };
      }
      
      // If no stats data is available, return default values
      return {
        total_cards: 0,
        cards_due: 0,
        cards_learned: 0,
        average_difficulty: 0,
        review_accuracy: 0,
        streak: 0
      };
    } catch (error) {
      console.error('Error in getFlashcardStats:', error);
      throw error;
    }
  };

  // Load flashcard stats
  useEffect(() => {
    const loadStats = async () => {
      if (!projectId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const statsData = await getFlashcardStats();
        setStats(statsData);
      } catch (err) {
        console.error('Error loading stats:', err);
        setError('Could not load flashcard statistics. The API endpoint may not be available yet.');
      } finally {
        setLoading(false);
      }
    };
    
    loadStats();
  }, [projectId]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8 w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
        <p className="text-muted-foreground">Loading statistics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-transparent bg-card rounded-2xl w-full">
        <CardHeader>
          <h3 className="text-lg font-semibold text-destructive">Error Loading Statistics</h3>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">{error}</p>
          
          {error.includes('500') && (
            <div className="bg-destructive/10 p-4 rounded-md mt-2">
              <p className="text-sm font-medium">Database Error Detected</p>
              <p className="text-xs text-muted-foreground mt-1">
                The backend is encountering a database error with the flashcards table. 
                This might be due to missing tables or schema issues.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <div className="flex justify-center items-center h-64 w-full">
        <p className="text-foreground">No statistics available.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full">
      <Card className="w-full border-transparent bg-card rounded-2xl">
        <CardHeader>
          
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <StatCard title="Total Cards" value={stats.total_cards} />
            <StatCard title="Cards Due" value={stats.cards_due} />
            <StatCard title="Cards Learned" value={stats.cards_learned} />
            <StatCard title="Average Difficulty" value={stats.average_difficulty.toFixed(1)} />
            <StatCard title="Review Accuracy" value={`${(stats.review_accuracy * 100).toFixed(0)}%`} />
            <StatCard title="Current Streak" value={stats.streak} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string | number;
}

function StatCard({ title, value }: StatCardProps) {
  return (
    <div className="p-4 rounded-lg border bg-card">
      <h4 className="text-sm font-medium mb-1 text-muted-foreground">{title}</h4>
      <p className="text-2xl font-bold text-foreground">{value}</p>
    </div>
  );
}