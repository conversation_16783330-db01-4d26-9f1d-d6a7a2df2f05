package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"decodemed/ai/flashcards"
	"decodemed/ai/mindmap"
	"decodemed/api/handlers/async"
	"decodemed/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AutoGenService handles automation of content generation
type AutoGenService struct {
	DB *gorm.DB
}

// NewAutoGenService creates a new instance of the auto-generation service
func NewAutoGenService(db *gorm.DB) *AutoGenService {
	return &AutoGenService{
		DB: db,
	}
}

// TriggerAutoContentGeneration automatically generates all content types for a project
// This triggers generation of quizzes, mindmaps, and flashcards in parallel
func (s *AutoGenService) TriggerAutoContentGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering all content generation for project %d", projectID)

	// Verify project belongs to user and units have been generated
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for auto content generation: %v", err)
		return
	}

	log.Printf("Found project %d for auto-generation. Units: %t, YouTube: %t",
		projectID, project.UnitsGenerated, project.YouTubeUnitsGenerated)

	// Check if units are generated
	if !project.UnitsGenerated && !project.YouTubeUnitsGenerated {
		log.Printf("No units have been generated for project %d, cannot auto-generate content", projectID)
		return
	}

	// Start parallel generation of different content types
	var wg sync.WaitGroup

	// Start quiz generation
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.TriggerAutoQuizGeneration(projectID, userID)
	}()

	// Start mindmap generation
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.TriggerAutoMindmapGeneration(projectID, userID)
	}()

	// Start flashcard generation
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.TriggerAutoFlashcardGeneration(projectID, userID)
	}()

	// Optional: Wait for all to complete (if we want to do something after)
	go func() {
		wg.Wait()
		log.Printf("All content generation completed for project %d", projectID)
	}()
}

// TriggerAutoQuizGeneration automatically generates quizzes for a project
// This can be called after unit generation completes
func (s *AutoGenService) TriggerAutoQuizGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering quiz generation for project %d", projectID)

	// Verify project belongs to user and units have been generated
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for auto quiz generation: %v", err)
		return
	}

	log.Printf("Found project %d for auto-generation. Units: %t, YouTube: %t, Quizzes: %t",
		projectID, project.UnitsGenerated, project.YouTubeUnitsGenerated, project.QuizzesGenerated)

	// Check if units are generated
	if !project.UnitsGenerated && !project.YouTubeUnitsGenerated {
		log.Printf("No units have been generated for project %d, cannot auto-generate quizzes", projectID)
		return
	}

	// Skip if quizzes are already generated
	if project.QuizzesGenerated {
		log.Printf("Quizzes already generated for project %d, skipping auto-generation", projectID)
		return
	}

	// Create job ID and initial status
	jobID := uuid.New().String()
	jobStatus := &async.JobStatus{
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
	}

	async.JobMutex.Lock()
	async.JobStatuses[jobID] = jobStatus
	async.JobMutex.Unlock()

	// Broadcast initial status via WebSocket
	async.BroadcastQuizUpdate(projectID, "pending", 0)
	log.Printf("Created job %s for auto-generating quizzes for project %d", jobID, projectID)

	// Start async processing
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in auto quiz generation: %v", r)
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Internal server error"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastQuizUpdate(projectID, "failed", 0)
			}
		}()

		// Update job status
		async.JobMutex.Lock()
		jobStatus.Status = "processing"
		jobStatus.Progress = 5
		async.JobMutex.Unlock()

		// Broadcast progress via WebSocket
		async.BroadcastQuizUpdate(projectID, "processing", 5)

		// Query to get all units for the project
		var units []models.Unit
		if err := s.DB.Where("project_id = ?", projectID).Find(&units).Error; err != nil {
			log.Printf("Error fetching units for project %d: %v", projectID, err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to fetch units"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastQuizUpdate(projectID, "failed", 0)
			return
		}

		// Update progress based on number of units
		totalUnits := len(units)
		if totalUnits == 0 {
			log.Printf("No units found for project %d", projectID)
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Error = "No units found"
			async.JobMutex.Unlock()

			// Broadcast completion via WebSocket
			async.BroadcastQuizUpdate(projectID, "completed", 100)
			return
		}

		log.Printf("Auto-generating quizzes for %d units in project %d", totalUnits, projectID)

		// Generate quizzes for each unit
		for i, unit := range units {
			log.Printf("Auto-generating quiz for unit %d (%d/%d)", unit.Model.ID, i+1, totalUnits)

			// Delete existing quiz if it exists
			if err := s.DB.Where("unit_id = ?", unit.Model.ID).Delete(&models.Quiz{}).Error; err != nil {
				log.Printf("Warning: Failed to delete existing quiz for unit %d: %v", unit.Model.ID, err)
				// Continue with generation even if delete fails
			}

			// Create new quiz
			questions := GenerateQuizContent(unit)
			quiz := models.Quiz{
				UnitID:       unit.Model.ID,
				Questions:    questions,
				MaxScore:     100,
				PassingScore: 70,
			}

			log.Printf("Generated quiz questions for unit %d (JSON length: %d)",
				unit.Model.ID, len(quiz.Questions))

			// Save quiz to database
			if err := s.DB.Create(&quiz).Error; err != nil {
				log.Printf("Failed to save quiz for unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Update progress
			progress := int((float64(i+1) / float64(totalUnits)) * 100)
			async.JobMutex.Lock()
			jobStatus.Progress = progress
			jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket every 10% or for the last unit
			if (i+1)%maxInt(1, totalUnits/10) == 0 || i == totalUnits-1 {
				async.BroadcastQuizUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
			}
		}

		// Update job status to complete
		async.JobMutex.Lock()
		jobStatus.Status = "completed"
		jobStatus.Progress = 100
		async.JobMutex.Unlock()

		// Update project to mark quizzes as generated
		if err := s.DB.Model(&models.Project{}).Where("id = ?", projectID).Update("quizzes_generated", true).Error; err != nil {
			log.Printf("Failed to update project quizzes_generated status: %v", err)
		} else {
			log.Printf("Successfully marked project %d as having quizzes generated", projectID)
		}

		// Broadcast completion via WebSocket
		async.BroadcastQuizUpdate(projectID, "completed", 100)
		log.Printf("Auto-generation of quizzes for project %d completed successfully", projectID)
	}()
}

// TriggerAutoMindmapGeneration automatically generates mindmaps for a project
func (s *AutoGenService) TriggerAutoMindmapGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering mindmap generation for project %d", projectID)

	// Verify project belongs to user and units have been generated
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for auto mindmap generation: %v", err)
		return
	}

	// Check if mindmaps are already generated using direct database query
	var mindmapsCount int64
	if err := s.DB.Model(&models.Mindmap{}).
		Joins("JOIN units ON mindmaps.unit_id = units.id").
		Where("units.project_id = ?", projectID).
		Count(&mindmapsCount).Error; err != nil {
		log.Printf("Error checking existing mindmaps: %v", err)
	} else if mindmapsCount > 0 {
		log.Printf("Mindmaps already exist for project %d, skipping auto-generation", projectID)
		return
	}

	// Create job ID and initial status
	jobID := uuid.New().String()
	jobStatus := &async.JobStatus{
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
	}

	async.JobMutex.Lock()
	async.JobStatuses[jobID] = jobStatus
	async.JobMutex.Unlock()

	// Broadcast initial status via WebSocket
	async.BroadcastMindmapUpdate(projectID, "pending", 0)
	log.Printf("Created job %s for auto-generating mindmaps for project %d", jobID, projectID)

	// Start async processing
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in auto mindmap generation: %v", r)
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Internal server error"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastMindmapUpdate(projectID, "failed", 0)
			}
		}()

		// Update job status
		async.JobMutex.Lock()
		jobStatus.Status = "processing"
		jobStatus.Progress = 5
		async.JobMutex.Unlock()

		// Broadcast progress via WebSocket
		async.BroadcastMindmapUpdate(projectID, "processing", 5)

		// Query to get all units for the project
		var units []models.Unit
		if err := s.DB.Where("project_id = ?", projectID).Find(&units).Error; err != nil {
			log.Printf("Error fetching units for project %d: %v", projectID, err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to fetch units"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastMindmapUpdate(projectID, "failed", 0)
			return
		}

		// Update progress based on number of units
		totalUnits := len(units)
		if totalUnits == 0 {
			log.Printf("No units found for project %d", projectID)
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Error = "No units found"
			async.JobMutex.Unlock()

			// Broadcast completion via WebSocket
			async.BroadcastMindmapUpdate(projectID, "completed", 100)
			return
		}

		log.Printf("Auto-generating mindmaps for %d units in project %d", totalUnits, projectID)

		// Initialize mindmap generator
		generator, err := mindmap.NewGenerator()
		if err != nil {
			log.Printf("Failed to create mindmap generator: %v", err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to initialize mindmap generator"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastMindmapUpdate(projectID, "failed", 0)
			return
		}

		// Generate mindmaps for each unit
		for i, unit := range units {
			log.Printf("Auto-generating mindmap for unit %d (%d/%d)", unit.Model.ID, i+1, totalUnits)

			// Delete existing mindmap if it exists
			if err := s.DB.Where("unit_id = ?", unit.Model.ID).Delete(&models.Mindmap{}).Error; err != nil {
				log.Printf("Warning: Failed to delete existing mindmap for unit %d: %v", unit.Model.ID, err)
				// Continue with generation even if delete fails
			}

			// Generate mindmap content
			ctx := context.Background()
			mindmapData, err := generator.GenerateFromContent(ctx, unit.Content, "")
			if err != nil {
				log.Printf("Failed to generate mindmap for unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Create new mindmap
			mindmapModel := models.Mindmap{
				UnitID: unit.Model.ID,
				Data:   mindmapData,
			}

			// Save mindmap to database
			if err := s.DB.Create(&mindmapModel).Error; err != nil {
				log.Printf("Failed to save mindmap for unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Update progress
			progress := int((float64(i+1) / float64(totalUnits)) * 100)
			async.JobMutex.Lock()
			jobStatus.Progress = progress
			jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket every 10% or for the last unit
			if (i+1)%maxInt(1, totalUnits/10) == 0 || i == totalUnits-1 {
				async.BroadcastMindmapUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
			}
		}

		// Update job status to complete
		async.JobMutex.Lock()
		jobStatus.Status = "completed"
		jobStatus.Progress = 100
		async.JobMutex.Unlock()

		// Broadcast completion via WebSocket
		async.BroadcastMindmapUpdate(projectID, "completed", 100)

		// Log completion instead of trying to update project metadata
		log.Printf("Auto-generation of mindmaps for project %d completed successfully", projectID)
	}()
}

// TriggerAutoFlashcardGeneration automatically generates flashcards for a project
func (s *AutoGenService) TriggerAutoFlashcardGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering flashcard generation for project %d", projectID)

	// Verify project belongs to user and units have been generated
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for auto flashcard generation: %v", err)
		return
	}

	// Check if flashcards are already generated using direct database query
	var flashcardsCount int64
	if err := s.DB.Model(&models.Flashcard{}).
		Joins("JOIN units ON flashcards.unit_id = units.id").
		Where("units.project_id = ?", projectID).
		Count(&flashcardsCount).Error; err != nil {
		log.Printf("Error checking existing flashcards: %v", err)
	} else if flashcardsCount > 0 {
		log.Printf("Flashcards already exist for project %d, skipping auto-generation", projectID)
		return
	}

	// Create job ID and initial status
	jobID := uuid.New().String()
	jobStatus := &async.JobStatus{
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
	}

	async.JobMutex.Lock()
	async.JobStatuses[jobID] = jobStatus
	async.JobMutex.Unlock()

	// Broadcast initial status via WebSocket
	async.BroadcastFlashcardUpdate(projectID, "pending", 0)
	log.Printf("Created job %s for auto-generating flashcards for project %d", jobID, projectID)

	// Start async processing
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in auto flashcard generation: %v", r)
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Internal server error"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastFlashcardUpdate(projectID, "failed", 0)
			}
		}()

		// Update job status
		async.JobMutex.Lock()
		jobStatus.Status = "processing"
		jobStatus.Progress = 5
		async.JobMutex.Unlock()

		// Broadcast progress via WebSocket
		async.BroadcastFlashcardUpdate(projectID, "processing", 5)

		// Query to get all units for the project
		var units []models.Unit
		if err := s.DB.Where("project_id = ?", projectID).Find(&units).Error; err != nil {
			log.Printf("Error fetching units for project %d: %v", projectID, err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to fetch units"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastFlashcardUpdate(projectID, "failed", 0)
			return
		}

		// Update progress based on number of units
		totalUnits := len(units)
		if totalUnits == 0 {
			log.Printf("No units found for project %d", projectID)
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Error = "No units found"
			async.JobMutex.Unlock()

			// Broadcast completion via WebSocket
			async.BroadcastFlashcardUpdate(projectID, "completed", 100)
			return
		}

		log.Printf("Auto-generating flashcards for %d units in project %d", totalUnits, projectID)

		// Initialize flashcard generator
		generator, err := flashcards.NewFlashcardGenerator()
		if err != nil {
			log.Printf("Failed to create flashcard generator: %v", err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to initialize flashcard generator"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastFlashcardUpdate(projectID, "failed", 0)
			return
		}

		// Generate flashcards for each unit
		for i, unit := range units {
			log.Printf("Auto-generating flashcards for unit %d (%d/%d)", unit.Model.ID, i+1, totalUnits)

			// Delete existing flashcards if they exist
			if err := s.DB.Where("unit_id = ?", unit.Model.ID).Delete(&models.Flashcard{}).Error; err != nil {
				log.Printf("Warning: Failed to delete existing flashcards for unit %d: %v", unit.Model.ID, err)
				// Continue with generation even if delete fails
			}

			// Generate flashcard content
			ctx := context.Background()
			flashcardItems, err := generator.GenerateFlashcardsFromContent(ctx, unit.Title, unit.Content, "medical")
			if err != nil {
				log.Printf("Failed to generate flashcards for unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Save each flashcard to database
			for _, item := range flashcardItems {
				flashcard := models.Flashcard{
					UnitID:         unit.Model.ID,
					Question:       item.Question,
					Answer:         item.Answer,
					DifficultyRank: item.DifficultyRank,
				}

				if err := s.DB.Create(&flashcard).Error; err != nil {
					log.Printf("Failed to save flashcard for unit %d: %v", unit.Model.ID, err)
					continue
				}
			}

			// Update progress
			progress := int((float64(i+1) / float64(totalUnits)) * 100)
			async.JobMutex.Lock()
			jobStatus.Progress = progress
			jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket every 10% or for the last unit
			if (i+1)%maxInt(1, totalUnits/10) == 0 || i == totalUnits-1 {
				async.BroadcastFlashcardUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
			}
		}

		// Update job status to complete
		async.JobMutex.Lock()
		jobStatus.Status = "completed"
		jobStatus.Progress = 100
		async.JobMutex.Unlock()

		// Broadcast completion via WebSocket
		async.BroadcastFlashcardUpdate(projectID, "completed", 100)

		// Log completion instead of trying to update project metadata
		log.Printf("Auto-generation of flashcards for project %d completed successfully", projectID)
	}()
}

// EnableAutoGeneration route handler to enable or disable auto-generation for a project
func EnableAutoGeneration(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			ProjectID            uint `json:"project_id" binding:"required"`
			EnableAutoQuizzes    bool `json:"enable_auto_quizzes"`
			EnableAutoMindmaps   bool `json:"enable_auto_mindmaps"`
			EnableAutoFlashcards bool `json:"enable_auto_flashcards"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		userID, _ := c.Get("user_id")

		// Verify project belongs to user
		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", req.ProjectID, userID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Update project settings
		// Note: In a full implementation, you might want to add auto_quizzes, auto_mindmaps, and auto_flashcards
		// boolean fields to the Project model
		// For now, we'll just return the current settings

		c.JSON(http.StatusOK, gin.H{
			"project_id":             project.ID,
			"enable_auto_quizzes":    req.EnableAutoQuizzes,
			"enable_auto_mindmaps":   req.EnableAutoMindmaps,
			"enable_auto_flashcards": req.EnableAutoFlashcards,
			"message":                "Auto-generation settings updated",
		})
	}
}

// Helper function for calculating progress intervals (copied from quizzes.go)
func maxInt(a, b int) int {
	if a > b {
		return a
	}
	return b
}
