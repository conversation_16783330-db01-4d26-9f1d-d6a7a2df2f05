import unittest
import asyncio
from unittest.mock import patch, AsyncMock
from ..chat_ai.chat_query import chat_query

def async_test(func):
    def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(func(*args, **kwargs))
    return wrapper

class TestChatQuery(unittest.TestCase):
    def setUp(self):
        self.chat_context = [{
            'user': 'what are the latest practice guiduilenes to treat necrotizing fasciitis?',
            'assistant': '- **Chronic Inflammatory Disease**: Acne is primarily a chronic inflammatory disease...'
        }]
        
        # Mock response data
        self.mock_response = AsyncMock()
        self.mock_response.choices = [AsyncMock()]
        self.mock_response.choices[0].message.content = "Mocked response content"

    @async_test
    @patch('openai.AsyncClient')
    async def test_basic_query(self, mock_client):
        # Configure mock
        mock_instance = mock_client.return_value
        mock_instance.chat.completions.create.return_value = self.mock_response
        
        question = """
        A 24-year-old woman presents with a fever and myalgias. She experienced brief, self-limited diarrhea 24 hours after attending a barbecue two weeks earlier. She remained asymptomatic until the day prior to presentation when she developed a fever of 39.4 C (103 F), conjunctivitis, and severe muscle pain. On physical examination she appears acutely ill and has a fever of 39.4 C. There is a diffuse maculopapular rash and generalized muscular tenderness. Several hemorrhages are noted beneath the fingernails. Admission hemogram reveals a white blood cell count of 15,000/mm3 with 25 percent eosinophils. The infectious form of the most likely causative agent is a(n)
(A) cyst
(B) cysticerci
(C) encysted larvae
(D) ovum
(E) rhabditiform larvae 
        """
        result = await chat_query(question, self.chat_context)
        
        self.assertIsInstance(result, str)
        print(f"Test Basic Query Result: {result}")
        return None  # Explicitly return None

    @async_test
    @patch('openai.AsyncClient')
    async def test_follow_up_query(self, mock_client):
        mock_instance = mock_client.return_value
        mock_instance.chat.completions.create.return_value = self.mock_response
        
        question1 = "What are the most effective treatments for bacterial sinusitis in adults?"
        result1 = await chat_query(question1, self.chat_context)
        
        self.chat_context.append({"user": question1, "assistant": result1})
        question2 = "Are there any new emerging treatments for acne?"
        result2 = await chat_query(question2, self.chat_context)
        
        self.assertIsInstance(result2, str)
        print(f"Test Follow-up Query Result: {result2}")
        return None  # Explicitly return None

if __name__ == '__main__':
    unittest.main(verbosity=2)