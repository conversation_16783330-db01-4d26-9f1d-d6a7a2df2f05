'use client';

import { useState } from 'react';
import { Sparkles } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import axios from 'axios';
import { API_URL } from '../../../utils/decode_api';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

interface UpgradeSubscriptionProps {
  token?: string;
}

export default function UpgradeSubscription({ token }: UpgradeSubscriptionProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubscribe = async (subscriptionType: 'monthly' | 'yearly') => {
    if (!token) {
      setError('Please login to subscribe');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const { data } = await axios.post(
        `${API_URL}/api/subscription-api/create-checkout-session`,
        {
          subscriptionType,
          successUrl: `${window.location.origin}/subscriptions/success`,
          cancelUrl: `${window.location.origin}/subscriptions/subscription-management`,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          withCredentials: true,
        }
      );
  
      const { sessionId } = data;
      const stripe = await stripePromise;
      
      if (!stripe) {
        throw new Error('Stripe failed to initialize');
      }
  
      const { error } = await stripe.redirectToCheckout({ sessionId });
      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error:', error);
      setError(
        axios.isAxiosError(error) 
          ? error.response?.data?.error || error.message 
          : 'An error occurred'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-20 flex flex-row space-x-8">
      {error && (
        <p className="text-red-600">{error}</p>
      )}
      <button
        onClick={() => handleSubscribe('yearly')}
        disabled={loading}
        className="inline-flex items-center space-x-2 bg-[#243153]  text-white py-3 px-6 rounded-md hover:bg-[#384973]"
      >
        {loading ? (
          <>
            <span>⚪</span>
            <span>Processing...</span>
          </>
        ) : (
          <>
            <Sparkles className="w-5 h-5" />
            <span>Upgrade</span>
          </>
        )}
      </button>
      <a href="./pricing" className="inline-flex items-center space-x-2 bg-[#243153]  text-white py-3 px-6 rounded-md hover:bg-[#384973]">
          <Sparkles className="w-5 h-5" />
          <span>Pricing</span>
      </a>
    </div>
  );
}
