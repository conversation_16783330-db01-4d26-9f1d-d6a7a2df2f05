import React from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { useTheme } from '@/components/theme/theme-provider';

const CustomNode: React.FC<NodeProps> = ({ data, isConnectable }) => {
  const { theme } = useTheme();
  
  return (
    <div
      style={{
        padding: '10px 20px',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '400',
        border: `1px solid ${theme === 'dark' ? '#555' : '#ddd'}`,
        background: data.isRoot 
          ? (theme === 'dark' ? '#69718a' : '#60a5fa') // Blue background for Root node (dark/light)
          : (theme === 'dark' ? '#3a3a3a' : '#fafafa'), // Neutral background for Child nodes (dark/light)
        color: theme === 'dark' ? '#fff' : '#000',
        width: 'auto',
        minWidth: '150px',
        textAlign: 'center',
        boxShadow: theme === 'dark' 
          ? '0 4px 6px rgba(0, 0, 0, 0.3)' 
          : '0 4px 6px rgba(0, 0, 0, 0.1)',
      }}
    >
      <Handle
        type="target"
        position={Position.Top}
        id="t"
        isConnectable={isConnectable}
        style={{ visibility: 'hidden' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="l"
        isConnectable={isConnectable}
        style={{ visibility: 'hidden' }}
      />
      {data.label}
      <Handle
        type="source"
        position={Position.Right}
        id="r"
        isConnectable={isConnectable}
        style={{ visibility: 'hidden' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="b"
        isConnectable={isConnectable}
        style={{ visibility: 'hidden' }}
      />
    </div>
  );
};

export default CustomNode; 