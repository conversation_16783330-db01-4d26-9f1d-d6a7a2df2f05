'use client';

import React, { useState, useEffect } from 'react';
import { Youtube } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';

// Interface for YouTube player props
interface YouTubePlayerProps {
  youtubeUrl: string | null;
  isLoadingVideo: boolean;
  videoError: string | null;
  availableLanguages?: string[];
  selectedLanguage?: string;
  onLanguageChange?: (language: string) => void;
}

// Function to extract video ID from YouTube URL
const extractVideoId = (url: string): string => {
  if (!url) return '';
  
  try {
    // Multiple format regex
    const regExp = /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
    const match = url.match(regExp);
    
    if (match && match[1] && match[1].length === 11) {
      return match[1];
    }
    
    // If we couldn't match, check if it's a simple ID
    if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {
      return url;
    }
    
    return '';
  } catch (error) {
    console.error("Error extracting YouTube video ID:", error);
    return '';
  }
};

export function YouTubePlayer({ 
  youtubeUrl, 
  isLoadingVideo, 
  videoError,
}: YouTubePlayerProps) {
  const [videoId, setVideoId] = useState<string | null>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    if (youtubeUrl) {
      // If it's already an embed URL, extract the ID directly
      if (youtubeUrl.includes('/embed/')) {
        const embedMatch = youtubeUrl.match(/\/embed\/([^?&#]+)/);
        if (embedMatch && embedMatch[1]) {
          console.log("YouTubePlayer - Extracted ID from embed URL:", embedMatch[1]);
          setVideoId(embedMatch[1]);
          return;
        }
      }
      
      // Otherwise use the normal extraction method
      const id = extractVideoId(youtubeUrl);
      console.log("YouTubePlayer - Extracted ID:", id, "from URL:", youtubeUrl);
      setVideoId(id);
    } else {
      setVideoId(null);
    }
  }, [youtubeUrl]);

  if (isLoadingVideo) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className={`${theme === 'dark' ? 'bg-gray-800 text-gray-200' : 'bg-white text-gray-800'} p-4 rounded shadow-md text-center`}>
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4">Loading video...</p>
        </div>
      </div>
    );
  }

  if (videoError) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className={`${theme === 'dark' ? 'bg-gray-800 text-gray-200' : 'bg-white text-gray-800'} p-4 rounded shadow-md text-center max-w-md`}>
          <div className="text-red-500 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <p>{videoError}</p>
        </div>
      </div>
    );
  }

  if (!videoId) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className={`${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-800'} p-4 rounded shadow-md text-center`}>
          <Youtube className="h-10 w-10 mx-auto text-red-500" />
          <p className="mt-4">Select a YouTube video to watch</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full w-full flex flex-col justify-center ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-900'}`}>
      <div className="relative pb-[56.25%] h-0 overflow-hidden max-w-full">
        <iframe 
          className="absolute top-0 left-0 w-full h-full"
          src={`https://www.youtube.com/embed/${videoId}?autoplay=0`}
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
      </div>
    </div>
  );
}