// This file implements a static, SEO friendly blog post using Next.js App Router.
'use client'

import Link from 'next/link';
import <PERSON><PERSON><PERSON> from 'next/script';
import { <PERSON>Header } from '@/containers/main/hero5-header';
import { Mail } from 'lucide-react';
import { MainThemeProvider } from '@/components/theme/main-theme-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

// A static posts array; you can replace this with real data or import from a JSON/Markdown file later.
const posts = [
  {
    slug: "top-ai-tools-for-medical-students",
    title: "Top AI Tools for Medical Students in 2025: Revolutionize Your Learning",
    subtitle: "Comprehensive Guide to AI Flashcards, Quizzes, Search & More",
    category: "AI in MedEd",
    date: "2025-09-05",
    excerpt: "Explore the best AI tools enhancing medical education, from flashcard generators to AI tutors. Discover why integrated platforms like DecodeMed offer the ultimate advantage.",
    readTime: "15 min read",
  },
  {
    slug: "using-ai-medical-search-effectively",
    title: "Using AI Medical Search Effectively: Strategies for Students & Professionals",
    subtitle: "Practical Tips for Medical Literature Review and Concept Understanding",
    category: "AI in Medicine",
    date: "2025-08-30",
    excerpt: "Learn practical strategies to maximize AI medical search tools for efficient literature reviews, understanding complex topics, and enhancing clinical reasoning. Features DecodeMed examples.",
    readTime: "14 min read",
  },
  {
    slug: "best-ai-medical-search-tools",
    title: "Best AI Medical Search Tools in 2025: A Comparative Guide",
    subtitle: "Choosing the Right Platform for Medical Information Retrieval",
    category: "AI Tools Comparison",
    date: "2025-08-25",
    excerpt: "Compare different categories of AI medical search tools, from general engines to specialized databases and integrated platforms like DecodeMed, to find the best fit for your needs.",
    readTime: "13 min read",
  },
  {
    slug: "ai-medical-search-explained",
    title: "AI Medical Search Explained: The Future of Medical Information Retrieval",
    subtitle: "Understanding How AI is Transforming Access to Medical Knowledge",
    category: "AI Fundamentals",
    date: "2025-08-20",
    excerpt: "Explore what AI medical search is, how it leverages NLP and ML to understand context and semantics, and the key benefits for medical students and professionals. Features DecodeMed integration.",
    readTime: "12 min read",
  },
  {
    slug: "proven-med-student-study-techniques",
    title: "Proven Med Student Study Techniques: Evidence-Based Methods for 2025",
    subtitle: "Master Effective Learning Strategies for Medical School",
    category: "Study Methods",
    date: "2025-08-15",
    excerpt: "Discover seven evidence-based med student study techniques backed by cognitive science research. Learn how AI-powered tools like DecodeMed can enhance these methods for maximum retention and efficiency.",
    readTime: "16 min read",
  },
  {
    slug: "essential-med-school-study-tools",
    title: "Essential Med School Study Tools: A Comprehensive Guide for 2025",
    subtitle: "Leveraging AI for Medical Education Success",
    category: "Medical Education",
    date: "2025-08-01",
    excerpt: "Discover the most powerful study tools for medical students in 2025, with a detailed analysis of how AI-powered solutions like DecodeMed are revolutionizing the way students master complex medical concepts.",
    readTime: "15 min read",
  },
  {
    slug: "beneficios-flashcards-ia-medicina-estudiantes",
    title: "10 Beneficios de las Flashcards IA para Estudiantes de Medicina en 2025",
    subtitle: "Análisis Detallado para Futuros Médicos",
    category: "Educación Médica",
    date: "2025-07-15",
    excerpt: "Descubre los beneficios concretos y medibles que las flashcards con IA ofrecen a estudiantes de medicina, desde la optimización del tiempo hasta la mejora en resultados académicos.",
    readTime: "12 min de lectura",
  },
  {
    slug: "las-mejores-flashcards-con-ia-para-estudiantes-medicina",
    title: "Las Mejores Flashcards con IA Para Estudiantes de Medicina en 2025",
    subtitle: "Colecciones Óptimas para Cada Especialidad",
    category: "Recursos de Estudio",
    date: "2025-07-10",
    excerpt: "Análisis de las mejores colecciones de flashcards creadas con IA específicamente diseñadas para estudiantes de medicina hispanohablantes.",
    readTime: "10 min de lectura",
  },
  {
    slug: "tecnicas-estudio-flashcards-ia-medicina",
    title: "Técnicas de Estudio con Flashcards IA: Estrategias Avanzadas para Estudiantes de Medicina",
    subtitle: "Métodos Basados en Neurociencia para Maximizar Resultados",
    category: "Metodología de Estudio",
    date: "2025-07-05",
    excerpt: "Descubre estrategias avanzadas basadas en neurociencia para aprovechar al máximo las flashcards con IA en tu preparación médica.",
    readTime: "14 min de lectura",
  },
  {
    slug: "comparativa-flashcards-ia-vs-tradicionales-medicina",
    title: "Comparativa: Flashcards IA vs Tradicionales para Estudiantes de Medicina 2025",
    subtitle: "Análisis Científico y Resultados Medibles",
    category: "Educación Médica",
    date: "2025-06-30",
    excerpt: "Un estudio comparativo exhaustivo entre las flashcards generadas por IA y las tradicionales, con datos concretos sobre eficiencia, retención y resultados académicos.",
    readTime: "15 min de lectura",
  },
  {
    slug: "mejores-generadores-flashcards-ia",
    title: "Los Mejores Generadores de Flashcards con IA para Estudiantes de Medicina en 2025",
    subtitle: "Guía Completa para Estudiantes Hispanohablantes",
    category: "Herramientas de Estudio",
    date: "2025-06-25",
    excerpt: "Análisis detallado de las mejores plataformas que utilizan inteligencia artificial para crear flashcards para estudiantes de medicina, con pros y contras de cada opción.",
    readTime: "11 min de lectura",
  },
  {
    slug: 'best-flashcards-for-medical-students',
    title: 'Best Flashcards for Medical Students: The Ultimate Guide for 2025',
    subtitle: 'Evidence-Based Recommendations for Effective Learning',
    category: 'Study Resources',
    date: '2025-06-10',
    excerpt:
      'Discover the most effective flashcard systems for medical education, from traditional physical cards to cutting-edge AI solutions. Learn which options best suit different subjects and learning styles.',
    readTime: '12 min read',
  },
  {
    slug: 'top-ai-quiz-generators',
    title: 'The Top AI Quiz Generators for Effective Medical Learning in 2025',
    subtitle: 'Advanced Tools for Medical Exam Preparation',
    category: 'Learning Tools',
    date: '2025-05-15',
    excerpt:
      'Discover the leading AI-powered quiz generators for medical students, with DecodeMed offering the most comprehensive solution. Create high-quality practice questions and enhance your exam preparation.',
    readTime: '9 min read',
  },
  {
    slug: 'best-ai-flashcard-generators',
    title: 'The Top AI Flashcard Generators to Elevate Your Medical Learning in 2025',
    subtitle: 'Comprehensive Guide for Medical Students',
    category: 'Learning Tools',
    date: '2025-04-20',
    excerpt:
      'Discover the top AI flashcard generators for medical students, with DecodeMed leading the pack. Save time and enhance learning with these cutting-edge tools optimized for medical education.',
    readTime: '10 min read',
  },
  {
    slug: 'decodemed-medical-flashcard-maker',
    title: 'Decodemed: The Ultimate Medical Flashcard Maker for Medical Students',
    subtitle: 'Advanced AI-Powered Learning Tools',
    category: 'Resources',
    date: '2025-01-15',
    excerpt:
      'Discover how Decodemed revolutionizes medical studying with its best-in-class flashcard maker—outperforming all competitors online and featuring a sleek dark mode interface.',
    readTime: '5 min read',
  },
  // New Portuguese posts
  {
    slug: 'melhores-aplicativos-flashcards-ia-medicina',
    title: 'Os 7 Melhores Aplicativos de Flashcards com IA para Estudantes de Medicina em 2025',
    subtitle: 'Análise Completa para Estudantes Brasileiros',
    category: 'Ferramentas de Estudo',
    date: '2025-07-20',
    excerpt:
      'Descubra os aplicativos de flashcards com IA mais eficientes para estudantes de medicina, com o DecodeMed liderando o ranking. Otimize seu tempo de estudo e aumente sua retenção de conteúdo com estas ferramentas inovadoras.',
    readTime: '8 min de leitura',
  },
  {
    slug: 'como-criar-flashcards-eficientes-com-ia',
    title: 'Como Criar Flashcards Eficientes com IA: Guia Completo para Estudantes de Medicina',
    subtitle: 'Técnicas Avançadas de Elaboração e Estudo',
    category: 'Metodologia de Estudo',
    date: '2025-07-18',
    excerpt:
      'Aprenda a criar flashcards personalizados e eficientes usando inteligência artificial para maximizar sua memorização. O DecodeMed oferece as ferramentas perfeitas para transformar seu material de estudo em cartões inteligentes que se adaptam ao seu ritmo de aprendizado.',
    readTime: '10 min de leitura',
  },
  {
    slug: 'plataformas-flashcards-inteligencia-artificial-medicina',
    title: 'As 5 Melhores Plataformas de Flashcards com Inteligência Artificial para Estudantes de Medicina',
    subtitle: 'Comparativo Detalhado com Prós e Contras',
    category: 'Recursos Educacionais',
    date: '2025-07-12',
    excerpt:
      'Análise comparativa das principais plataformas de flashcards com IA disponíveis para estudantes de medicina, destacando como o DecodeMed se diferencia com recursos avançados de personalização e adaptação ao aprendizado individual.',
    readTime: '12 min de leitura',
  },
  {
    slug: 'geradores-flashcards-ia-estudo-medicina',
    title: 'Geradores de Flashcards com IA: Revolucionando o Estudo da Medicina em 2025',
    subtitle: 'Como a Tecnologia Está Transformando a Educação Médica',
    category: 'Tecnologia Educacional',
    date: '2025-07-05',
    excerpt:
      'Conheça como os geradores de flashcards com IA estão transformando a forma de estudar medicina, economizando tempo e melhorando resultados acadêmicos. O DecodeMed lidera essa revolução com seu algoritmo exclusivo que adapta o conteúdo ao seu estilo de aprendizado.',
    readTime: '9 min de leitura',
  },
  {
    slug: 'flashcards-adaptativo-aprendizado-medicina',
    title: 'Flashcards de Aprendizado Adaptativo: O Futuro da Educação Médica Está Aqui',
    subtitle: 'Como a IA Personaliza seu Estudo para Máxima Retenção',
    category: 'Inovação Educacional',
    date: '2025-06-28',
    excerpt:
      'Descubra como os flashcards de aprendizado adaptativo do DecodeMed utilizam inteligência artificial para identificar seus pontos fracos e ajustar automaticamente a frequência de revisão, criando um sistema de estudo personalizado que aumenta significativamente a retenção de conteúdo.',
    readTime: '11 min de leitura',
  },
  // You can add additional posts here.
];

const BlogContent = () => {
  return (
    <main>
      {/* Breadcrumb JSON-LD Structured Data */}
      <Script id="breadcrumb" type="application/ld+json" strategy="afterInteractive">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "https://decodemed.com"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Blog",
              "item": "https://decodemed.com/blog"
            }
          ]
        })}
      </Script>

      {/* Hero Section with the same styling as pricing page */}
      <section id="blog" className="py-16 md:py-32">
        <div className="mx-auto max-w-6xl px-6">
          <div className="mx-auto max-w-2xl space-y-6 text-center">
            <h1 className="text-center text-4xl font-semibold lg:text-5xl">Our Blog</h1>
            <p className="text-muted-foreground">Stay updated with the latest medical education techniques, study tips, and platform features
            to enhance your learning journey.</p>
          </div>
        </div>
      </section>

      {/* Main Content Grid */}
      <div className="mx-auto max-w-6xl px-6 pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Blog Posts Section */}
          <div className="lg:col-span-2">
            <div className="grid gap-8">
              {posts.map((post) => (
                <Card key={post.slug} className="border overflow-hidden transition-all duration-300 hover:shadow-md">
                  <Link href={`/blog/${post.slug}`} className="group">
                    <CardContent className="p-8">
                      <div className="flex items-center gap-4 mb-6">
                        <span className="px-4 py-1.5 bg-primary/10 rounded-full text-sm font-medium text-primary">
                          {post.category}
                        </span>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <time dateTime={post.date}>
                            {new Date(post.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </time>
                          <span>•</span>
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <h2 className="text-3xl font-bold group-hover:text-primary transition-colors duration-200">
                          {post.title}
                        </h2>
                        <h3 className="text-xl">
                          {post.subtitle}
                        </h3>
                        <p className="text-muted-foreground text-lg leading-relaxed">
                          {post.excerpt}
                        </p>
                      </div>
                      <div className="mt-6 flex items-center text-primary">
                        <span className="font-medium">Read article</span>
                        <svg className="w-4 h-4 ml-2 group-hover:translate-x-2 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
          </div>

          {/* Newsletter Section */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <Card className="border">
                <CardHeader>
                  <div className="flex justify-center mb-2">
                    <div className="bg-primary/10 p-4 rounded-full">
                      <Mail className="w-8 h-8 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-2xl font-bold text-center">
                    Subscribe to our newsletter
                  </CardTitle>
                  <CardDescription className="text-center text-lg">
                    Get the latest updates on medical education and study techniques
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form className="w-full space-y-4">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                    />
                    <Button className="w-full py-6" type="submit">
                      Subscribe
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default function BlogIndexPage() {
  return (
    <MainThemeProvider>
      <HeroHeader />
      <BlogContent />
    </MainThemeProvider>
  );
}
