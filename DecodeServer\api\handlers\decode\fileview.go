package handlers

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"decodemed/models"
	"decodemed/storage"
)

// FileViewerRequest is the request body for getting a file viewer URL
type FileViewerRequest struct {
	ObjectName string `json:"object_name" binding:"required"`
	ProjectID  uint   `json:"project_id,omitempty"` // Optional: provide project ID to fetch from database
	ExpiresIn  int    `json:"expires_in,omitempty"` // Optional: expiration time in seconds
}

// ThumbnailRequest is the request body for getting a thumbnail URL
type ThumbnailRequest struct {
	ObjectName string `json:"object_name" binding:"required"`
	ProjectID  uint   `json:"project_id,omitempty"` // Optional: provide project ID to fetch from database
}

// BatchThumbnailRequest is the request body for getting multiple thumbnail URLs at once
type BatchThumbnailRequest struct {
	ProjectIDs []uint `json:"project_ids" binding:"required"`
}

// GetFileViewerURL handles requests for generating file viewer URLs for file access
func GetFileViewerURL(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req FileViewerRequest

		// Check if this is a GET request with project ID in the URL
		if c.Request.Method == http.MethodGet {
			projectIDStr := c.Param("projectId")

			if projectIDStr == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Project ID is required",
					"success": false,
				})
				return
			}

			var projectID uint
			if _, err := fmt.Sscanf(projectIDStr, "%d", &projectID); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Invalid project ID format: " + err.Error(),
					"success": false,
				})
				return
			}

			// Get project from database
			var project models.Project
			if err := db.First(&project, projectID).Error; err != nil {
				c.JSON(http.StatusNotFound, gin.H{
					"error":      "Project not found: " + err.Error(),
					"project_id": projectID,
					"success":    false,
				})
				return
			}

			if project.FileURL == "" {
				c.JSON(http.StatusNotFound, gin.H{
					"error":   "Project has no associated file",
					"success": false,
				})
				return
			}

			// Check if this is a YouTube video
			if project.Type == models.TypeYouTube {
				log.Printf("Project %d is a YouTube video, returning direct URL: %s", projectID, project.FileURL)
				c.JSON(http.StatusOK, gin.H{
					"file_url":   project.FileURL,
					"project_id": projectID,
					"success":    true,
					"is_youtube": true,
					"type":       project.Type,
				})
				return
			}

			// Check if this is a YouTube URL before trying to extract object name
			if (strings.Contains(project.FileURL, "youtube.com") || strings.Contains(project.FileURL, "youtu.be")) && project.Type == models.TypeYouTube {
				log.Printf("YouTube URL detected, skipping GCS signed URL generation: %s", project.FileURL)
				c.JSON(http.StatusOK, gin.H{
					"file_url":   project.FileURL,
					"project_id": projectID,
					"success":    true,
					"is_youtube": true,
					"type":       project.Type,
				})
				return
			}

			// Extract object name from project file URL
			objectName := project.FileURL
			if objectNameMatch := storage.ExtractObjectNameFromURL(project.FileURL); objectNameMatch != "" {
				objectName = objectNameMatch

			}

			// Get the storage client
			log.Printf("Initializing storage client...")
			storageClient, err := storage.GetStorage(context.Background())
			if err != nil {
				log.Printf("Failed to initialize storage client: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":      "Failed to initialize storage client: " + err.Error(),
					"bucket":     os.Getenv("GCS_BUCKET_NAME"),
					"project_id": os.Getenv("GCS_PROJECT_ID"),
				})
				return
			}

			// Add this line - use service account email from credentials file
			serviceAccountEmail := os.Getenv("GCS_SERVICE_ACCOUNT_EMAIL")
			if serviceAccountEmail == "" {
				log.Printf("Warning: GCS_SERVICE_ACCOUNT_EMAIL not set, falling back to direct file URL")
				c.JSON(http.StatusOK, gin.H{
					"signed_url": project.FileURL, // Return direct URL as fallback
					"success":    true,
				})
				return
			}

			// Default expiration time (1 hour)
			expiration := 1 * time.Hour

			// Generate the signed URL

			signedURL, err := storageClient.GetSignedURL(context.Background(), objectName, expiration)
			if err != nil {
				log.Printf("Failed to generate signed URL: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":       "Failed to generate file viewer URL: " + err.Error(),
					"object_name": objectName,
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"signed_url": signedURL,
				"expires_in": int(expiration.Seconds()),
				"file_name":  objectName,
				"project":    project,
				"success":    true,
			})
			return
		}

		// POST request handling follows - original implementation
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid request: " + err.Error(),
			})
			return
		}

		// If ProjectID is provided, get the fileURL from the Project model
		objectName := req.ObjectName
		if req.ProjectID > 0 {
			var project models.Project
			if err := db.First(&project, req.ProjectID).Error; err != nil {
				c.JSON(http.StatusNotFound, gin.H{
					"error": "Project not found: " + err.Error(),
				})
				return
			}

			if project.FileURL == "" {
				c.JSON(http.StatusNotFound, gin.H{
					"error": "Project has no associated file",
				})
				return
			}

			// If the FileURL is already a full URL, extract the object name
			if project.FileURL != "" {
				// Extract the object name from the URL if it's a full URL
				// Otherwise use it directly as object name
				if objectNameMatch := storage.ExtractObjectNameFromURL(project.FileURL); objectNameMatch != "" {
					objectName = objectNameMatch
				} else {
					objectName = project.FileURL
				}
			}
		}

		// Get the storage client
		storageClient, err := storage.GetStorage(context.Background())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to initialize storage client: " + err.Error(),
			})
			return
		}

		// Set default expiration time if not provided (1 hour)
		expiration := time.Duration(req.ExpiresIn) * time.Second
		if expiration == 0 {
			expiration = 1 * time.Hour
		}

		// Generate the signed URL
		signedURL, err := storageClient.GetSignedURL(context.Background(), objectName, expiration)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate file viewer URL: " + err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"signed_url": signedURL,
			"expires_in": int(expiration.Seconds()),
			"file_name":  objectName,
		})
	}
}

// ProxyFileContent retrieves and serves file content directly through the backend to avoid CORS issues
func ProxyFileContent(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Log the request
		log.Printf("Incoming request to: %s %s", c.Request.Method, c.Request.URL.Path)

		// Get the object name from the query parameter
		objectName := c.Query("object")
		if objectName == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Object name is required",
			})
			return
		}

		log.Printf("Proxying file content for object: %s", objectName)

		// Get the storage client
		storageClient, err := storage.GetStorage(context.Background())
		if err != nil {
			log.Printf("Failed to initialize storage client: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to initialize storage client: " + err.Error(),
			})
			return
		}

		// Get a signed URL to redirect to
		ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
		defer cancel()

		// Create a temporary signed URL with short expiration (30 minutes)
		signedURL, err := storageClient.GetSignedURL(ctx, objectName, 30*time.Minute)
		if err != nil {
			log.Printf("Failed to generate signed URL for object %s: %v", objectName, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to access file: " + err.Error(),
			})
			return
		}

		// Create a client to fetch the content from the signed URL
		httpClient := &http.Client{
			Timeout: 30 * time.Second,
		}

		// Request the file from GCS using the signed URL
		req, err := http.NewRequestWithContext(ctx, http.MethodGet, signedURL, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to create request: " + err.Error(),
			})
			return
		}

		resp, err := httpClient.Do(req)
		if err != nil {
			log.Printf("Error fetching from GCS: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to fetch file: " + err.Error(),
			})
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Printf("GCS returned status code: %d", resp.StatusCode)
			c.JSON(resp.StatusCode, gin.H{
				"error": fmt.Sprintf("Storage server returned status code %d", resp.StatusCode),
			})
			return
		}

		// Determine content type
		contentType := resp.Header.Get("Content-Type")
		if contentType == "" {
			// Default to application/octet-stream if content type is not available
			contentType = "application/octet-stream"
		}

		// Set appropriate content type based on file extension
		ext := strings.ToLower(filepath.Ext(objectName))
		switch ext {
		case ".pdf":
			contentType = "application/pdf"
		case ".docx":
			contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
		case ".doc":
			contentType = "application/msword"
		case ".txt", ".text":
			contentType = "text/plain"
		case ".md", ".markdown":
			contentType = "text/markdown"
		case ".csv":
			contentType = "text/csv"
		}

		// Set appropriate headers for CORS and caching
		c.Header("Content-Type", contentType)
		c.Header("Content-Disposition", fmt.Sprintf("inline; filename=%s", path.Base(objectName)))
		c.Header("Access-Control-Allow-Origin", "*") // Allow access from any origin
		c.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")
		c.Header("Cache-Control", "public, max-age=3600") // Cache for 1 hour

		// Stream the content to the client
		log.Printf("Streaming content to client, content type: %s, length: %d", contentType, resp.ContentLength)
		c.DataFromReader(http.StatusOK, resp.ContentLength, contentType, resp.Body, nil)
	}
}

// GetThumbnailURL handles requests for generating thumbnail URLs for files
func GetThumbnailURL(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req ThumbnailRequest

		// Check if this is a GET request with project ID in the URL
		if c.Request.Method == http.MethodGet {
			projectIDStr := c.Param("projectId")

			if projectIDStr == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Project ID is required",
					"success": false,
				})
				return
			}

			var projectID uint
			if _, err := fmt.Sscanf(projectIDStr, "%d", &projectID); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Invalid project ID format: " + err.Error(),
					"success": false,
				})
				return
			}

			// Get project from database
			var project models.Project
			if err := db.First(&project, projectID).Error; err != nil {
				c.JSON(http.StatusNotFound, gin.H{
					"error":      "Project not found: " + err.Error(),
					"project_id": projectID,
					"success":    false,
				})
				return
			}

			if project.FileURL == "" {
				c.JSON(http.StatusNotFound, gin.H{
					"error":   "Project has no associated file",
					"success": false,
				})
				return
			}

			// Check if project already has a thumbnail URL stored
			if project.ThumbnailURL != "" {
				c.JSON(http.StatusOK, gin.H{
					"thumbnail_url": project.ThumbnailURL,
					"object_name":   storage.ExtractObjectNameFromURL(project.FileURL),
					"project_id":    projectID,
					"success":       true,
					"from_db":       true,
					"is_youtube":    project.Type == models.TypeYouTube,
					"type":          project.Type,
				})
				return
			}

			// Check if project is a YouTube video
			if project.Type == models.TypeYouTube {
				// Extract YouTube video ID from URL
				videoID, err := ExtractYoutubeID(project.FileURL)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "Failed to extract YouTube ID: " + err.Error(),
						"success": false,
					})
					return
				}

				// Generate YouTube thumbnail URL
				// YouTube provides various thumbnail sizes, we'll use the highest quality
				thumbnailURL := fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID)

				// Return success with YouTube thumbnail URL
				c.JSON(http.StatusOK, gin.H{
					"thumbnail_url": thumbnailURL,
					"object_name":   videoID,
					"project_id":    projectID,
					"success":       true,
					"is_youtube":    true,
					"type":          project.Type,
				})
				return
			}

			// Extract object name from project file URL
			objectName := project.FileURL
			if objectNameMatch := storage.ExtractObjectNameFromURL(project.FileURL); objectNameMatch != "" {
				objectName = objectNameMatch
			}

			// Get the storage client
			storageClient, err := storage.GetStorage(context.Background())
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":   "Failed to initialize storage client: " + err.Error(),
					"success": false,
				})
				return
			}

			// Get thumbnail URL - this is now asynchronous in the backend
			thumbnailURL, err := storageClient.GetThumbnailURL(context.Background(), objectName)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":   "Failed to generate thumbnail: " + err.Error(),
					"success": false,
				})
				return
			}

			// Store the thumbnail URL in the database for future use
			if !strings.Contains(thumbnailURL, "processing") {
				if err := db.Model(&models.Project{}).Where("id = ?", projectID).Update("thumbnail_url", thumbnailURL).Error; err != nil {
					log.Printf("Failed to update project %d with thumbnail URL: %v", projectID, err)
				}
			}

			// Return success with thumbnail URL
			c.JSON(http.StatusOK, gin.H{
				"thumbnail_url": thumbnailURL,
				"object_name":   objectName,
				"project_id":    projectID,
				"success":       true,
				"is_processing": !strings.Contains(thumbnailURL, objectName), // Check if we got a processing placeholder
			})
			return
		}

		// Handle POST request
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid request: " + err.Error(),
				"success": false,
			})
			return
		}

		// If project ID provided, get file URL from database
		if req.ProjectID > 0 {
			var project models.Project
			if err := db.First(&project, req.ProjectID).Error; err != nil {
				c.JSON(http.StatusNotFound, gin.H{
					"error":      "Project not found: " + err.Error(),
					"project_id": req.ProjectID,
					"success":    false,
				})
				return
			}

			if project.FileURL == "" {
				c.JSON(http.StatusNotFound, gin.H{
					"error":   "Project has no associated file",
					"success": false,
				})
				return
			}

			// Check if project already has a thumbnail URL stored
			if project.ThumbnailURL != "" {
				c.JSON(http.StatusOK, gin.H{
					"thumbnail_url": project.ThumbnailURL,
					"object_name":   storage.ExtractObjectNameFromURL(project.FileURL),
					"project_id":    req.ProjectID,
					"success":       true,
					"from_db":       true,
					"is_youtube":    project.Type == models.TypeYouTube,
					"type":          project.Type,
				})
				return
			}

			// Check if project is a YouTube video
			if project.Type == models.TypeYouTube {
				// Extract YouTube video ID from URL
				videoID, err := ExtractYoutubeID(project.FileURL)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "Failed to extract YouTube ID: " + err.Error(),
						"success": false,
					})
					return
				}

				// Generate YouTube thumbnail URL
				thumbnailURL := fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID)

				// Return success with YouTube thumbnail URL
				c.JSON(http.StatusOK, gin.H{
					"thumbnail_url": thumbnailURL,
					"object_name":   videoID,
					"project_id":    req.ProjectID,
					"success":       true,
					"is_youtube":    true,
					"type":          project.Type,
				})
				return
			}

			// Extract object name from project file URL
			if objectNameMatch := storage.ExtractObjectNameFromURL(project.FileURL); objectNameMatch != "" {
				req.ObjectName = objectNameMatch
			} else {
				req.ObjectName = project.FileURL
			}
		}

		// Get the storage client
		storageClient, err := storage.GetStorage(context.Background())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to initialize storage client: " + err.Error(),
				"success": false,
			})
			return
		}

		// Get thumbnail URL - this is now asynchronous in the backend
		thumbnailURL, err := storageClient.GetThumbnailURL(context.Background(), req.ObjectName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to generate thumbnail: " + err.Error(),
				"success": false,
			})
			return
		}

		// Store the thumbnail URL in the database if this is for a project
		if req.ProjectID > 0 && !strings.Contains(thumbnailURL, "processing") {
			if err := db.Model(&models.Project{}).Where("id = ?", req.ProjectID).Update("thumbnail_url", thumbnailURL).Error; err != nil {
				log.Printf("Failed to update project %d with thumbnail URL: %v", req.ProjectID, err)
			}
		}

		// Return success with thumbnail URL
		c.JSON(http.StatusOK, gin.H{
			"thumbnail_url": thumbnailURL,
			"object_name":   req.ObjectName,
			"project_id":    req.ProjectID,
			"success":       true,
			"is_processing": !strings.Contains(thumbnailURL, req.ObjectName), // Check if we got a processing placeholder
		})
	}
}

// GetBatchThumbnailURLs handles requests for generating multiple thumbnail URLs at once
func GetBatchThumbnailURLs(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {

		// Log the request body
		body, _ := io.ReadAll(c.Request.Body)
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body)) // Reset the body

		var req BatchThumbnailRequest

		if err := c.ShouldBindJSON(&req); err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid request: " + err.Error(),
				"success": false,
			})
			return
		}

		if len(req.ProjectIDs) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "No project IDs provided",
				"success": false,
			})
			return
		}

		// Limit the number of projects to process at once (to prevent abuse)
		maxProjects := 50
		if len(req.ProjectIDs) > maxProjects {
			req.ProjectIDs = req.ProjectIDs[:maxProjects]
		}

		// Get the storage client
		storageClient, err := storage.GetStorage(context.Background())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to initialize storage client: " + err.Error(),
				"success": false,
			})
			return
		}

		// Process thumbnails in parallel with a worker pool
		var wg sync.WaitGroup
		resultsChan := make(chan map[string]interface{}, len(req.ProjectIDs))
		// Limit concurrency to avoid overwhelming the system
		semaphore := make(chan struct{}, 10) // Process up to 10 thumbnails concurrently

		// First, check if any projects already have thumbnail URLs stored in the database
		var projects []models.Project
		if err := db.Where("id IN ?", req.ProjectIDs).Find(&projects).Error; err != nil {
			log.Printf("Error fetching projects: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to fetch projects: " + err.Error(),
				"success": false,
			})
			return
		}

		// Create a map for quick lookup of projects by ID
		projectMap := make(map[uint]models.Project)
		for _, p := range projects {
			projectMap[p.ID] = p
		}

		// Process each project ID
		for _, projectID := range req.ProjectIDs {
			wg.Add(1)

			// Acquire semaphore (this will block if we're already processing the maximum number of thumbnails)
			semaphore <- struct{}{}

			// Process this project in a goroutine
			go func(pid uint) {
				defer wg.Done()
				defer func() { <-semaphore }() // Release semaphore when done

				thumbnailInfo := map[string]interface{}{
					"project_id": pid,
					"success":    true,
				}

				// Check if we found the project in the database
				project, exists := projectMap[pid]
				if !exists {
					thumbnailInfo["error"] = "Project not found"
					thumbnailInfo["success"] = false
					resultsChan <- thumbnailInfo
					return
				}

				// If project has no file URL, skip it
				if project.FileURL == "" {
					thumbnailInfo["error"] = "Project has no file URL"
					thumbnailInfo["success"] = false
					resultsChan <- thumbnailInfo
					return
				}

				// Check if project already has a thumbnail URL stored
				if project.ThumbnailURL != "" {
					thumbnailInfo["thumbnail_url"] = project.ThumbnailURL
					thumbnailInfo["object_name"] = storage.ExtractObjectNameFromURL(project.FileURL)
					thumbnailInfo["from_db"] = true

					// For YouTube videos, add additional info
					if project.Type == models.TypeYouTube {
						thumbnailInfo["is_youtube"] = true
						thumbnailInfo["type"] = project.Type
					}

					resultsChan <- thumbnailInfo
					return
				}

				// Handle YouTube videos
				if project.Type == models.TypeYouTube {
					// Extract YouTube video ID from URL
					videoID, err := ExtractYoutubeID(project.FileURL)
					if err != nil {
						thumbnailInfo["error"] = "Failed to extract YouTube ID: " + err.Error()
						thumbnailInfo["success"] = false
						resultsChan <- thumbnailInfo
						return
					}

					// Generate YouTube thumbnail URL
					thumbnailURL := fmt.Sprintf("https://img.youtube.com/vi/%s/maxresdefault.jpg", videoID)

					// Store the thumbnail URL in the database for future use
					if err := db.Model(&models.Project{}).Where("id = ?", pid).Update("thumbnail_url", thumbnailURL).Error; err != nil {
						log.Printf("Failed to update project %d with YouTube thumbnail URL: %v", pid, err)
					}

					thumbnailInfo["thumbnail_url"] = thumbnailURL
					thumbnailInfo["object_name"] = videoID
					thumbnailInfo["is_youtube"] = true
					thumbnailInfo["type"] = project.Type
					resultsChan <- thumbnailInfo
					return
				}

				// Extract object name from project file URL
				objectName := project.FileURL
				if objectNameMatch := storage.ExtractObjectNameFromURL(project.FileURL); objectNameMatch != "" {
					objectName = objectNameMatch
				}

				// Get thumbnail URL
				thumbnailURL, err := storageClient.GetThumbnailURL(context.Background(), objectName)
				if err != nil {
					thumbnailInfo["error"] = "Failed to generate thumbnail: " + err.Error()
					thumbnailInfo["success"] = false
					resultsChan <- thumbnailInfo
					return
				}

				// Store the thumbnail URL in the database for future use
				if !strings.Contains(thumbnailURL, "processing") {
					if err := db.Model(&models.Project{}).Where("id = ?", pid).Update("thumbnail_url", thumbnailURL).Error; err != nil {
						log.Printf("Failed to update project %d with thumbnail URL: %v", pid, err)
					}
				}

				thumbnailInfo["thumbnail_url"] = thumbnailURL
				thumbnailInfo["object_name"] = objectName
				thumbnailInfo["is_processing"] = !strings.Contains(thumbnailURL, objectName) // Check if we got a processing placeholder
				resultsChan <- thumbnailInfo
			}(projectID)
		}

		// Wait for all goroutines to complete in a separate goroutine
		go func() {
			wg.Wait()
			close(resultsChan)
		}()

		// Collect results with a timeout to ensure the API doesn't hang
		thumbnails := make([]map[string]interface{}, 0, len(req.ProjectIDs))
		timeout := time.After(10 * time.Second) // Set a reasonable timeout

		for {
			select {
			case result, ok := <-resultsChan:
				if !ok {
					// Channel closed, all results collected
					// Return all thumbnails
					c.JSON(http.StatusOK, gin.H{
						"thumbnails": thumbnails,
						"success":    true,
						"count":      len(thumbnails),
					})
					return
				}
				thumbnails = append(thumbnails, result)

			case <-timeout:
				// Timeout reached, return what we have so far
				c.JSON(http.StatusOK, gin.H{
					"thumbnails": thumbnails,
					"success":    true,
					"count":      len(thumbnails),
					"timeout":    true,
				})
				return
			}
		}
	}
}

// ExtractYoutubeID extracts the video ID from a YouTube URL
func ExtractYoutubeID(youtubeURL string) (string, error) {
	// Common YouTube URL patterns:
	// - https://www.youtube.com/watch?v=VIDEO_ID
	// - https://youtu.be/VIDEO_ID
	// - https://www.youtube.com/embed/VIDEO_ID

	regExp := regexp.MustCompile(`(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})`)
	matches := regExp.FindStringSubmatch(youtubeURL)

	if len(matches) < 2 {
		return "", fmt.Errorf("invalid YouTube URL format: %s", youtubeURL)
	}

	return matches[1], nil
}

// Helper function to check if a file exists
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// ProxyYouTubeEmbed proxies requests to YouTube embed URLs to avoid CORS issues
func ProxyYouTubeEmbed() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the YouTube video ID from the query parameter
		videoID := c.Query("id")
		if videoID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "YouTube video ID is required",
			})
			return
		}

		log.Printf("Proxying YouTube embed for video ID: %s", videoID)

		// Create the YouTube embed URL
		youtubeEmbedURL := fmt.Sprintf("https://www.youtube.com/embed/%s", videoID)

		// Create a client to fetch the content
		httpClient := &http.Client{
			Timeout: 10 * time.Second,
		}

		// Create a request to the YouTube embed URL
		req, err := http.NewRequest(http.MethodGet, youtubeEmbedURL, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to create request: " + err.Error(),
			})
			return
		}

		// Add standard browser headers to avoid being blocked
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
		req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
		req.Header.Set("Accept-Language", "en-US,en;q=0.5")

		// Make the request to YouTube
		resp, err := httpClient.Do(req)
		if err != nil {
			log.Printf("Error fetching from YouTube: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to fetch YouTube content: " + err.Error(),
			})
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Printf("YouTube returned status code: %d", resp.StatusCode)
			c.JSON(resp.StatusCode, gin.H{
				"error": fmt.Sprintf("YouTube returned status code %d", resp.StatusCode),
			})
			return
		}

		// Read the response body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to read response body: " + err.Error(),
			})
			return
		}

		// Set appropriate headers for CORS
		c.Header("Content-Type", resp.Header.Get("Content-Type"))
		c.Header("Access-Control-Allow-Origin", "*") // Allow access from any origin
		c.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")
		c.Header("Cache-Control", "public, max-age=3600") // Cache for 1 hour

		// Write the response body to the client
		c.Data(http.StatusOK, resp.Header.Get("Content-Type"), body)
	}
}
