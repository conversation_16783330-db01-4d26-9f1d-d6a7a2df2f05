
import { useTheme } from '@/components/theme/theme-provider';

interface NewChatButtonProps {
  handleNewChat: () => void;
}

export const NewChatButton = ({ handleNewChat }: NewChatButtonProps) => {
  const { theme } = useTheme();
  
  return (
    <button
      onClick={handleNewChat}
      className={`w-full flex items-center justify-center p-2 mt-2 rounded-xl ${
        theme === 'dark' 
          ? 'text-gray-300 hover:bg-[#1a2234] hover:text-white' 
          : 'text-black hover:bg-gray-200'
      }`}
      title="New Chat"
    >
      <span className="text-sm">New Chat</span>
    </button>
  );
};