{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/motion-primitives/text-effect.tsx"], "sourcesContent": ["'use client';\nimport { cn } from '@/lib/utils';\nimport {\n  AnimatePresence,\n  motion,\n  TargetAndTransition,\n  Transition,\n  Variant,\n  Variants,\n} from 'motion/react';\nimport React from 'react';\n\nexport type PresetType = 'blur' | 'fade-in-blur' | 'scale' | 'fade' | 'slide';\n\nexport type PerType = 'word' | 'char' | 'line';\n\nexport type TextEffectProps = {\n  children: string;\n  per?: PerType;\n  as?: keyof React.JSX.IntrinsicElements;\n  variants?: {\n    container?: Variants;\n    item?: Variants;\n  };\n  className?: string;\n  preset?: PresetType;\n  delay?: number;\n  speedReveal?: number;\n  speedSegment?: number;\n  trigger?: boolean;\n  onAnimationComplete?: () => void;\n  onAnimationStart?: () => void;\n  segmentWrapperClassName?: string;\n  containerTransition?: Transition;\n  segmentTransition?: Transition;\n  style?: React.CSSProperties;\n};\n\nconst defaultStaggerTimes: Record<PerType, number> = {\n  char: 0.03,\n  word: 0.05,\n  line: 0.1,\n};\n\nconst defaultContainerVariants: Variants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.05,\n    },\n  },\n  exit: {\n    transition: { staggerChildren: 0.05, staggerDirection: -1 },\n  },\n};\n\nconst defaultItemVariants: Variants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n  },\n  exit: { opacity: 0 },\n};\n\nconst presetVariants: Record<\n  PresetType,\n  { container: Variants; item: Variants }\n> = {\n  blur: {\n    container: defaultContainerVariants,\n    item: {\n      hidden: { opacity: 0, filter: 'blur(12px)' },\n      visible: { opacity: 1, filter: 'blur(0px)' },\n      exit: { opacity: 0, filter: 'blur(12px)' },\n    },\n  },\n  'fade-in-blur': {\n    container: defaultContainerVariants,\n    item: {\n      hidden: { opacity: 0, y: 20, filter: 'blur(12px)' },\n      visible: { opacity: 1, y: 0, filter: 'blur(0px)' },\n      exit: { opacity: 0, y: 20, filter: 'blur(12px)' },\n    },\n  },\n  scale: {\n    container: defaultContainerVariants,\n    item: {\n      hidden: { opacity: 0, scale: 0 },\n      visible: { opacity: 1, scale: 1 },\n      exit: { opacity: 0, scale: 0 },\n    },\n  },\n  fade: {\n    container: defaultContainerVariants,\n    item: {\n      hidden: { opacity: 0 },\n      visible: { opacity: 1 },\n      exit: { opacity: 0 },\n    },\n  },\n  slide: {\n    container: defaultContainerVariants,\n    item: {\n      hidden: { opacity: 0, y: 20 },\n      visible: { opacity: 1, y: 0 },\n      exit: { opacity: 0, y: 20 },\n    },\n  },\n};\n\nconst AnimationComponent: React.FC<{\n  segment: string;\n  variants: Variants;\n  per: 'line' | 'word' | 'char';\n  segmentWrapperClassName?: string;\n}> = React.memo(({ segment, variants, per, segmentWrapperClassName }) => {\n  const content =\n    per === 'line' ? (\n      <motion.span variants={variants} className='block'>\n        {segment}\n      </motion.span>\n    ) : per === 'word' ? (\n      <motion.span\n        aria-hidden='true'\n        variants={variants}\n        className='inline-block whitespace-pre'\n      >\n        {segment}\n      </motion.span>\n    ) : (\n      <motion.span className='inline-block whitespace-pre'>\n        {segment.split('').map((char, charIndex) => (\n          <motion.span\n            key={`char-${charIndex}`}\n            aria-hidden='true'\n            variants={variants}\n            className='inline-block whitespace-pre'\n          >\n            {char}\n          </motion.span>\n        ))}\n      </motion.span>\n    );\n\n  if (!segmentWrapperClassName) {\n    return content;\n  }\n\n  const defaultWrapperClassName = per === 'line' ? 'block' : 'inline-block';\n\n  return (\n    <span className={cn(defaultWrapperClassName, segmentWrapperClassName)}>\n      {content}\n    </span>\n  );\n});\n\nAnimationComponent.displayName = 'AnimationComponent';\n\nconst splitText = (text: string, per: 'line' | 'word' | 'char') => {\n  if (per === 'line') return text.split('\\n');\n  return text.split(/(\\s+)/);\n};\n\nconst hasTransition = (\n  variant: Variant\n): variant is TargetAndTransition & { transition?: Transition } => {\n  return (\n    typeof variant === 'object' && variant !== null && 'transition' in variant\n  );\n};\n\nconst createVariantsWithTransition = (\n  baseVariants: Variants,\n  transition?: Transition & { exit?: Transition }\n): Variants => {\n  if (!transition) return baseVariants;\n\n  const {...mainTransition } = transition;\n\n  return {\n    ...baseVariants,\n    visible: {\n      ...baseVariants.visible,\n      transition: {\n        ...(hasTransition(baseVariants.visible)\n          ? baseVariants.visible.transition\n          : {}),\n        ...mainTransition,\n      },\n    },\n    exit: {\n      ...baseVariants.exit,\n      transition: {\n        ...(hasTransition(baseVariants.exit)\n          ? baseVariants.exit.transition\n          : {}),\n        ...mainTransition,\n        staggerDirection: -1,\n      },\n    },\n  };\n};\n\nexport function TextEffect({\n  children,\n  per = 'word',\n  as = 'p',\n  variants,\n  className,\n  preset = 'fade',\n  delay = 0,\n  speedReveal = 1,\n  speedSegment = 1,\n  trigger = true,\n  onAnimationComplete,\n  onAnimationStart,\n  segmentWrapperClassName,\n  containerTransition,\n  segmentTransition,\n  style,\n}: TextEffectProps) {\n  const segments = splitText(children, per);\n  const MotionTag = motion[as as keyof typeof motion] as typeof motion.div;\n\n  const baseVariants = preset\n    ? presetVariants[preset]\n    : { container: defaultContainerVariants, item: defaultItemVariants };\n\n  const stagger = defaultStaggerTimes[per] / speedReveal;\n\n  const baseDuration = 0.3 / speedSegment;\n\n  const customStagger = hasTransition(variants?.container?.visible ?? {})\n    ? (variants?.container?.visible as TargetAndTransition).transition\n        ?.staggerChildren\n    : undefined;\n\n  const customDelay = hasTransition(variants?.container?.visible ?? {})\n    ? (variants?.container?.visible as TargetAndTransition).transition\n        ?.delayChildren\n    : undefined;\n\n  const computedVariants = {\n    container: createVariantsWithTransition(\n      variants?.container || baseVariants.container,\n      {\n        staggerChildren: customStagger ?? stagger,\n        delayChildren: customDelay ?? delay,\n        ...containerTransition,\n        exit: {\n          staggerChildren: customStagger ?? stagger,\n          staggerDirection: -1,\n        },\n      }\n    ),\n    item: createVariantsWithTransition(variants?.item || baseVariants.item, {\n      duration: baseDuration,\n      ...segmentTransition,\n    }),\n  };\n\n  return (\n    <AnimatePresence mode='popLayout'>\n      {trigger && (\n        <MotionTag\n          initial='hidden'\n          animate='visible'\n          exit='exit'\n          variants={computedVariants.container}\n          className={className}\n          onAnimationComplete={onAnimationComplete}\n          onAnimationStart={onAnimationStart}\n          style={style}\n        >\n          {per !== 'line' ? <span className='sr-only'>{children}</span> : null}\n          {segments.map((segment, index) => (\n            <AnimationComponent\n              key={`${per}-${index}-${segment}`}\n              segment={segment}\n              variants={computedVariants.item}\n              per={per}\n              segmentWrapperClassName={segmentWrapperClassName}\n            />\n          ))}\n        </MotionTag>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAQA;AAVA;;;;;AAsCA,MAAM,sBAA+C;IACnD,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEA,MAAM,2BAAqC;IACzC,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;IACA,MAAM;QACJ,YAAY;YAAE,iBAAiB;YAAM,kBAAkB,CAAC;QAAE;IAC5D;AACF;AAEA,MAAM,sBAAgC;IACpC,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;IACX;IACA,MAAM;QAAE,SAAS;IAAE;AACrB;AAEA,MAAM,iBAGF;IACF,MAAM;QACJ,WAAW;QACX,MAAM;YACJ,QAAQ;gBAAE,SAAS;gBAAG,QAAQ;YAAa;YAC3C,SAAS;gBAAE,SAAS;gBAAG,QAAQ;YAAY;YAC3C,MAAM;gBAAE,SAAS;gBAAG,QAAQ;YAAa;QAC3C;IACF;IACA,gBAAgB;QACd,WAAW;QACX,MAAM;YACJ,QAAQ;gBAAE,SAAS;gBAAG,GAAG;gBAAI,QAAQ;YAAa;YAClD,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAG,QAAQ;YAAY;YACjD,MAAM;gBAAE,SAAS;gBAAG,GAAG;gBAAI,QAAQ;YAAa;QAClD;IACF;IACA,OAAO;QACL,WAAW;QACX,MAAM;YACJ,QAAQ;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAC/B,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAE;QAC/B;IACF;IACA,MAAM;QACJ,WAAW;QACX,MAAM;YACJ,QAAQ;gBAAE,SAAS;YAAE;YACrB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;QACrB;IACF;IACA,OAAO;QACL,WAAW;QACX,MAAM;YACJ,QAAQ;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC5B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;QAC5B;IACF;AACF;AAEA,MAAM,mCAKD,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,uBAAuB,EAAE;IAClE,MAAM,UACJ,QAAQ,uBACN,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;QAAC,UAAU;QAAU,WAAU;kBACxC;;;;;eAED,QAAQ,uBACV,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;QACV,eAAY;QACZ,UAAU;QACV,WAAU;kBAET;;;;;6BAGH,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;QAAC,WAAU;kBACpB,QAAQ,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,0BAC5B,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,eAAY;gBACZ,UAAU;gBACV,WAAU;0BAET;eALI,CAAC,KAAK,EAAE,WAAW;;;;;;;;;;IAWlC,IAAI,CAAC,yBAAyB;QAC5B,OAAO;IACT;IAEA,MAAM,0BAA0B,QAAQ,SAAS,UAAU;IAE3D,qBACE,6LAAC;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBAC1C;;;;;;AAGP;KA7CM;AA+CN,mBAAmB,WAAW,GAAG;AAEjC,MAAM,YAAY,CAAC,MAAc;IAC/B,IAAI,QAAQ,QAAQ,OAAO,KAAK,KAAK,CAAC;IACtC,OAAO,KAAK,KAAK,CAAC;AACpB;AAEA,MAAM,gBAAgB,CACpB;IAEA,OACE,OAAO,YAAY,YAAY,YAAY,QAAQ,gBAAgB;AAEvE;AAEA,MAAM,+BAA+B,CACnC,cACA;IAEA,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,EAAC,GAAG,gBAAgB,GAAG;IAE7B,OAAO;QACL,GAAG,YAAY;QACf,SAAS;YACP,GAAG,aAAa,OAAO;YACvB,YAAY;gBACV,GAAI,cAAc,aAAa,OAAO,IAClC,aAAa,OAAO,CAAC,UAAU,GAC/B,CAAC,CAAC;gBACN,GAAG,cAAc;YACnB;QACF;QACA,MAAM;YACJ,GAAG,aAAa,IAAI;YACpB,YAAY;gBACV,GAAI,cAAc,aAAa,IAAI,IAC/B,aAAa,IAAI,CAAC,UAAU,GAC5B,CAAC,CAAC;gBACN,GAAG,cAAc;gBACjB,kBAAkB,CAAC;YACrB;QACF;IACF;AACF;AAEO,SAAS,WAAW,EACzB,QAAQ,EACR,MAAM,MAAM,EACZ,KAAK,GAAG,EACR,QAAQ,EACR,SAAS,EACT,SAAS,MAAM,EACf,QAAQ,CAAC,EACT,cAAc,CAAC,EACf,eAAe,CAAC,EAChB,UAAU,IAAI,EACd,mBAAmB,EACnB,gBAAgB,EAChB,uBAAuB,EACvB,mBAAmB,EACnB,iBAAiB,EACjB,KAAK,EACW;IAChB,MAAM,WAAW,UAAU,UAAU;IACrC,MAAM,YAAY,qNAAA,CAAA,SAAM,CAAC,GAA0B;IAEnD,MAAM,eAAe,SACjB,cAAc,CAAC,OAAO,GACtB;QAAE,WAAW;QAA0B,MAAM;IAAoB;IAErE,MAAM,UAAU,mBAAmB,CAAC,IAAI,GAAG;IAE3C,MAAM,eAAe,MAAM;IAE3B,MAAM,gBAAgB,cAAc,UAAU,WAAW,WAAW,CAAC,KACjE,AAAC,UAAU,WAAW,QAAgC,UAAU,EAC5D,kBACJ;IAEJ,MAAM,cAAc,cAAc,UAAU,WAAW,WAAW,CAAC,KAC/D,AAAC,UAAU,WAAW,QAAgC,UAAU,EAC5D,gBACJ;IAEJ,MAAM,mBAAmB;QACvB,WAAW,6BACT,UAAU,aAAa,aAAa,SAAS,EAC7C;YACE,iBAAiB,iBAAiB;YAClC,eAAe,eAAe;YAC9B,GAAG,mBAAmB;YACtB,MAAM;gBACJ,iBAAiB,iBAAiB;gBAClC,kBAAkB,CAAC;YACrB;QACF;QAEF,MAAM,6BAA6B,UAAU,QAAQ,aAAa,IAAI,EAAE;YACtE,UAAU;YACV,GAAG,iBAAiB;QACtB;IACF;IAEA,qBACE,6LAAC,oNAAA,CAAA,kBAAe;QAAC,MAAK;kBACnB,yBACC,6LAAC;YACC,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU,iBAAiB,SAAS;YACpC,WAAW;YACX,qBAAqB;YACrB,kBAAkB;YAClB,OAAO;;gBAEN,QAAQ,uBAAS,6LAAC;oBAAK,WAAU;8BAAW;;;;;2BAAmB;gBAC/D,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wBAEC,SAAS;wBACT,UAAU,iBAAiB,IAAI;wBAC/B,KAAK;wBACL,yBAAyB;uBAJpB,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;;;;;;;;;;;;;;;;AAW/C;MArFgB", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/motion-primitives/animated-group.tsx"], "sourcesContent": ["'use client';\nimport { ReactNode } from 'react';\nimport { motion, Variants } from 'motion/react';\nimport React from 'react';\n\nexport type PresetType =\n  | 'fade'\n  | 'slide'\n  | 'scale'\n  | 'blur'\n  | 'blur-slide'\n  | 'zoom'\n  | 'flip'\n  | 'bounce'\n  | 'rotate'\n  | 'swing';\n\nexport type AnimatedGroupProps = {\n  children: ReactNode;\n  className?: string;\n  variants?: {\n    container?: Variants;\n    item?: Variants;\n  };\n  preset?: PresetType;\n  as?: React.ElementType;\n  asChild?: React.ElementType;\n};\n\nconst defaultContainerVariants: Variants = {\n  visible: {\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nconst defaultItemVariants: Variants = {\n  hidden: { opacity: 0 },\n  visible: { opacity: 1 },\n};\n\nconst presetVariants: Record<\n  PresetType,\n  Variants\n> = {\n  fade: {\n  },\n  slide: {\n    hidden: { y: 20 },\n    visible: { y: 0 },\n  },\n  scale: {\n    hidden: { scale: 0.8 },\n    visible: { scale: 1 },\n  },\n  blur: {\n    hidden: { filter: 'blur(4px)' },\n    visible: { filter: 'blur(0px)' },\n  },\n  'blur-slide': {\n    hidden: { filter: 'blur(4px)', y: 20 },\n    visible: { filter: 'blur(0px)', y: 0 },\n  },\n  zoom: {\n    hidden: { scale: 0.5 },\n    visible: {\n      scale: 1,\n      transition: { type: 'spring', stiffness: 300, damping: 20 },\n    },\n  },\n  flip: {\n    hidden: { rotateX: -90 },\n    visible: {\n      rotateX: 0,\n      transition: { type: 'spring', stiffness: 300, damping: 20 },\n    },\n  },\n  bounce: {\n    hidden: { y: -50 },\n    visible: {\n      y: 0,\n      transition: { type: 'spring', stiffness: 400, damping: 10 },\n    },\n  },\n  rotate: {\n    hidden: { rotate: -180 },\n    visible: {\n      rotate: 0,\n      transition: { type: 'spring', stiffness: 200, damping: 15 },\n    },\n  },\n  swing: {\n    hidden: { rotate: -10 },\n    visible: {\n      rotate: 0,\n      transition: { type: 'spring', stiffness: 300, damping: 8 },\n    },\n  },\n};\n\nconst addDefaultVariants = (variants: Variants) => ({\n    hidden: { ...defaultItemVariants.hidden, ...variants.hidden },\n    visible: { ...defaultItemVariants.visible, ...variants.visible },\n});\n\nfunction AnimatedGroup({\n  children,\n  className,\n  variants,\n  preset,\n  as = 'div',\n  asChild = 'div',\n}: AnimatedGroupProps) {\n  const selectedVariants = {\n    item: addDefaultVariants(preset ? presetVariants[preset] : {}),\n    container: addDefaultVariants(defaultContainerVariants)\n  };\n  const containerVariants = variants?.container || selectedVariants.container;\n  const itemVariants = variants?.item || selectedVariants.item;\n\n  // Use motion.custom for dynamic components\n  const Container = as === 'div' ? motion.div : motion(as as React.ElementType);\n  const Item = asChild === 'div' ? motion.div : motion(asChild as React.ElementType);\n\n  return (\n    <Container\n      initial='hidden'\n      animate='visible'\n      variants={containerVariants}\n      className={className}\n    >\n      {React.Children.map(children, (child, index) => (\n        <Item key={index} variants={itemVariants}>\n          {child}\n        </Item>\n      ))}\n    </Container>\n  );\n}\n\nexport { AnimatedGroup };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA6BA,MAAM,2BAAqC;IACzC,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,sBAAgC;IACpC,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QAAE,SAAS;IAAE;AACxB;AAEA,MAAM,iBAGF;IACF,MAAM,CACN;IACA,OAAO;QACL,QAAQ;YAAE,GAAG;QAAG;QAChB,SAAS;YAAE,GAAG;QAAE;IAClB;IACA,OAAO;QACL,QAAQ;YAAE,OAAO;QAAI;QACrB,SAAS;YAAE,OAAO;QAAE;IACtB;IACA,MAAM;QACJ,QAAQ;YAAE,QAAQ;QAAY;QAC9B,SAAS;YAAE,QAAQ;QAAY;IACjC;IACA,cAAc;QACZ,QAAQ;YAAE,QAAQ;YAAa,GAAG;QAAG;QACrC,SAAS;YAAE,QAAQ;YAAa,GAAG;QAAE;IACvC;IACA,MAAM;QACJ,QAAQ;YAAE,OAAO;QAAI;QACrB,SAAS;YACP,OAAO;YACP,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D;IACF;IACA,MAAM;QACJ,QAAQ;YAAE,SAAS,CAAC;QAAG;QACvB,SAAS;YACP,SAAS;YACT,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D;IACF;IACA,QAAQ;QACN,QAAQ;YAAE,GAAG,CAAC;QAAG;QACjB,SAAS;YACP,GAAG;YACH,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D;IACF;IACA,QAAQ;QACN,QAAQ;YAAE,QAAQ,CAAC;QAAI;QACvB,SAAS;YACP,QAAQ;YACR,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D;IACF;IACA,OAAO;QACL,QAAQ;YAAE,QAAQ,CAAC;QAAG;QACtB,SAAS;YACP,QAAQ;YACR,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAE;QAC3D;IACF;AACF;AAEA,MAAM,qBAAqB,CAAC,WAAuB,CAAC;QAChD,QAAQ;YAAE,GAAG,oBAAoB,MAAM;YAAE,GAAG,SAAS,MAAM;QAAC;QAC5D,SAAS;YAAE,GAAG,oBAAoB,OAAO;YAAE,GAAG,SAAS,OAAO;QAAC;IACnE,CAAC;AAED,SAAS,cAAc,EACrB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,MAAM,EACN,KAAK,KAAK,EACV,UAAU,KAAK,EACI;IACnB,MAAM,mBAAmB;QACvB,MAAM,mBAAmB,SAAS,cAAc,CAAC,OAAO,GAAG,CAAC;QAC5D,WAAW,mBAAmB;IAChC;IACA,MAAM,oBAAoB,UAAU,aAAa,iBAAiB,SAAS;IAC3E,MAAM,eAAe,UAAU,QAAQ,iBAAiB,IAAI;IAE5D,2CAA2C;IAC3C,MAAM,YAAY,OAAO,QAAQ,qNAAA,CAAA,SAAM,CAAC,GAAG,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAM,AAAD,EAAE;IACrD,MAAM,OAAO,YAAY,QAAQ,qNAAA,CAAA,SAAM,CAAC,GAAG,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAM,AAAD,EAAE;IAErD,qBACE,6LAAC;QACC,SAAQ;QACR,SAAQ;QACR,UAAU;QACV,WAAW;kBAEV,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,sBACpC,6LAAC;gBAAiB,UAAU;0BACzB;eADQ;;;;;;;;;;AAMnB;KAjCS", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/theme/main-theme-provider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { createContext, useContext, useEffect, useState } from \"react\"\r\n\r\ntype Theme = \"dark\" | \"light\"\r\n\r\ntype MainThemeProviderProps = {\r\n  children: React.ReactNode\r\n}\r\n\r\ntype MainThemeProviderState = {\r\n  theme: Theme\r\n  toggleTheme: () => void\r\n}\r\n\r\nconst MainThemeProviderContext = createContext<MainThemeProviderState | undefined>(undefined)\r\n\r\nexport function MainThemeProvider({\r\n  children,\r\n}: MainThemeProviderProps) {\r\n  const [theme, setTheme] = useState<Theme>(\"light\")\r\n  const [mounted, setMounted] = useState(false)\r\n\r\n  // Auto-detect user's preferred color scheme on initial load\r\n  useEffect(() => {\r\n    setMounted(true)\r\n    // Check if user prefers dark mode\r\n    const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches\r\n    setTheme(prefersDark ? \"dark\" : \"light\")\r\n    \r\n    // Listen for changes to color scheme preference\r\n    const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\")\r\n    const handleChange = (e: MediaQueryListEvent) => {\r\n      setTheme(e.matches ? \"dark\" : \"light\")\r\n    }\r\n    \r\n    mediaQuery.addEventListener(\"change\", handleChange)\r\n    return () => mediaQuery.removeEventListener(\"change\", handleChange)\r\n  }, [])\r\n\r\n  // Apply the theme to the document when it changes\r\n  useEffect(() => {\r\n    if (!mounted) return\r\n    \r\n    const root = window.document.documentElement\r\n    root.classList.remove(\"light\", \"dark\")\r\n    root.classList.add(theme)\r\n    \r\n    if (theme === 'dark') {\r\n      // Apply our specific dark background for main pages\r\n      const darkBg = \"hsl(240 10% 3.9%)\"\r\n      root.style.setProperty('--main-background', darkBg)\r\n      root.style.setProperty('--main-text', 'hsl(0 0% 95%)')\r\n    } else {\r\n      // Light mode background\r\n      const lightBg = \"#ffffff\"\r\n      root.style.setProperty('--main-background', lightBg)\r\n      root.style.setProperty('--main-text', 'hsl(0 0% 10%)')\r\n    }\r\n  }, [theme, mounted])\r\n\r\n  const toggleTheme = () => {\r\n    setTheme(theme === \"light\" ? \"dark\" : \"light\")\r\n  }\r\n\r\n  // Prevent flash of incorrect theme\r\n  if (!mounted) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <MainThemeProviderContext.Provider value={{ theme, toggleTheme }}>\r\n      {children}\r\n    </MainThemeProviderContext.Provider>\r\n  )\r\n}\r\n\r\nexport const useMainTheme = () => {\r\n  const context = useContext(MainThemeProviderContext)\r\n  if (context === undefined) {\r\n    throw new Error(\"useMainTheme must be used within a MainThemeProvider\")\r\n  }\r\n  return context\r\n} "], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAeA,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAsC;AAE5E,SAAS,kBAAkB,EAChC,QAAQ,EACe;;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,WAAW;YACX,kCAAkC;YAClC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAC7E,SAAS,cAAc,SAAS;YAEhC,gDAAgD;YAChD,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;4DAAe,CAAC;oBACpB,SAAS,EAAE,OAAO,GAAG,SAAS;gBAChC;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;+CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;sCAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;YAC5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;YAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;YAEnB,IAAI,UAAU,QAAQ;gBACpB,oDAAoD;gBACpD,MAAM,SAAS;gBACf,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB;gBAC5C,KAAK,KAAK,CAAC,WAAW,CAAC,eAAe;YACxC,OAAO;gBACL,wBAAwB;gBACxB,MAAM,UAAU;gBAChB,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB;gBAC5C,KAAK,KAAK,CAAC,WAAW,CAAC,eAAe;YACxC;QACF;sCAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,mCAAmC;IACnC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,yBAAyB,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAC5D;;;;;;AAGP;GA1DgB;KAAA;AA4DT,MAAM,eAAe;;IAC1B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/theme/main-theme-toggle.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Moon, Sun } from \"lucide-react\"\r\nimport { useMainTheme } from \"./main-theme-provider\"\r\n\r\nexport function MainThemeToggle() {\r\n  const { theme, toggleTheme } = useMainTheme()\r\n\r\n  return (\r\n    <button\r\n      onClick={toggleTheme}\r\n      className=\"p-2 rounded-md transition-colors flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-800\"\r\n      aria-label=\"Toggle theme\"\r\n    >\r\n      {theme === 'light' ? (\r\n        <Moon className=\"h-5 w-5\" />\r\n      ) : (\r\n        <Sun className=\"h-5 w-5\" />\r\n      )}\r\n    </button>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD;IAE1C,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;iCAEhB,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;;;;;;AAIvB;GAhBgB;;QACiB,oJAAA,CAAA,eAAY;;;KAD7B", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/providers/language-switcher.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useLanguage } from \"@/app/providers/language-provider\"\n\nimport { useState } from 'react'\nimport { ChevronDown } from 'lucide-react'\nimport type { Locale } from '@/app/utils/types/language'\n\nexport function LanguageSwitcher() {\n  const { language, setLanguage } = useLanguage()\n  const [isOpen, setIsOpen] = useState(false)\n\n  const languages = {\n    en: { name: 'English', flag: '🇺🇸' },\n    es: { name: 'Español', flag: '🇪🇸' },\n    pt: { name: 'Português', flag: '🇧🇷' }\n  }\n\n  const currentLanguage = languages[language as keyof typeof languages]\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center gap-2 text-base font-medium text-gray-300 hover:text-white hover:bg-[#111827] px-3 py-2 rounded-xl transition-all duration-300\"\n        aria-label=\"Change language\"\n      >\n        {currentLanguage.flag} {currentLanguage.name}\n        <ChevronDown className=\"w-4 h-4\" />\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-[#111827] rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50\">\n          {Object.entries(languages).map(([code, { name, flag }]) => (\n            <button\n              key={code}\n              onClick={() => {\n                setLanguage(code as Locale)\n                setIsOpen(false)\n              }}\n              className={`w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 ${\n                language === code ? 'bg-gray-100 dark:bg-gray-700' : ''\n              }`}\n            >\n              {flag} {name}\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAQO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY;QAChB,IAAI;YAAE,MAAM;YAAW,MAAM;QAAO;QACpC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAO;QACpC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAO;IACxC;IAEA,MAAM,kBAAkB,SAAS,CAAC,SAAmC;IAErE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;;oBAEV,gBAAgB,IAAI;oBAAC;oBAAE,gBAAgB,IAAI;kCAC5C,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAGxB,wBACC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,iBACpD,6LAAC;wBAEC,SAAS;4BACP,YAAY;4BACZ,UAAU;wBACZ;wBACA,WAAW,CAAC,qGAAqG,EAC/G,aAAa,OAAO,iCAAiC,IACrD;;4BAED;4BAAK;4BAAE;;uBATH;;;;;;;;;;;;;;;;AAgBnB;GA3CgB;;QACoB,4IAAA,CAAA,cAAW;;;KAD/B", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/containers/main/hero5-header.tsx"], "sourcesContent": ["'use client'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { Menu, X } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport React, { useEffect } from 'react'\nimport { cn } from '@/lib/utils'\nimport { SignInButton, SignUpButton } from \"@clerk/nextjs\"\nimport { useMainTheme } from '@/components/theme/main-theme-provider'\nimport { MainThemeToggle } from '@/components/theme/main-theme-toggle'\nimport { LanguageSwitcher } from '@/app/providers/language-switcher'\n\nconst menuItems = [\n    { name: 'Pricing', href: '/home/<USER>' },\n    { name: 'Blog', href: '/blog' },\n]\n\nexport const HeroHeader = () => {\n    const [menuState, setMenuState] = React.useState(false)\n    const [isScrolled, setIsScrolled] = React.useState(false)\n    const { theme } = useMainTheme()\n    const isDarkMode = theme === 'dark'\n\n    // Handle scroll detection\n    useEffect(() => {\n        const handleScroll = () => {\n            setIsScrolled(window.scrollY > 50)\n        }\n        window.addEventListener('scroll', handleScroll)\n        return () => window.removeEventListener('scroll', handleScroll)\n    }, [])\n\n    return (\n        <header className=\"dark:bg-[hsl(240_10%_3.9%)]\">\n            <nav\n                data-state={menuState && 'active'}\n                className=\"fixed z-20 w-full px-2\">\n                <div className={cn(\n                    'mx-auto mt-2 max-w-6xl px-4 sm:px-6 transition-all duration-300 lg:px-8 xl:px-12', \n                    isScrolled && 'bg-white/90 dark:bg-[hsl(240_10%_3.9%)]/90 max-w-[90%] sm:max-w-4xl rounded-xl sm:rounded-2xl border backdrop-blur-lg lg:px-5'\n                )}>\n                    <div className=\"relative flex flex-wrap items-center justify-between gap-4 py-2 sm:py-3 lg:gap-0 lg:py-4\">\n                        <div className=\"flex w-full justify-between lg:w-auto\">\n                            <Link\n                                href=\"/\"\n                                aria-label=\"home\"\n                                className=\"flex items-center space-x-2\">\n                                <Image\n                                    src={isDarkMode ? \"/decodemed_dark.svg\" : \"/decodemed.svg\"}\n                                    alt=\"DecodeMed Logo\"\n                                    width={32}\n                                    height={32}\n                                    priority={true}\n                                    className=\"mt-1\"\n                                />\n                                <Image \n                                    src={isDarkMode ? \"/decodemed_text_dark.svg\" : \"/decodemed_text.svg\"}\n                                    alt=\"DecodeMed Text\"\n                                    width={130}\n                                    height={45}\n                                    priority={true}\n                                    className=\"mt-2\"\n                                />\n                            </Link>\n\n                            <button\n                                onClick={() => setMenuState(!menuState)}\n                                aria-label={menuState == true ? 'Close Menu' : 'Open Menu'}\n                                className=\"relative z-20 -m-2.5 -mr-4 block cursor-pointer p-2.5 lg:hidden\">\n                                <Menu className=\"in-data-[state=active]:rotate-180 in-data-[state=active]:scale-0 in-data-[state=active]:opacity-0 m-auto size-5 sm:size-6 duration-200\" />\n                                <X className=\"in-data-[state=active]:rotate-0 in-data-[state=active]:scale-100 in-data-[state=active]:opacity-100 absolute inset-0 m-auto size-5 sm:size-6 -rotate-180 scale-0 opacity-0 duration-200\" />\n                            </button>\n                        </div>\n\n                        <div className=\"absolute inset-0 m-auto hidden size-fit lg:block\">\n                            <ul className=\"flex gap-6 lg:gap-8 text-sm\">\n                                {menuItems.map((item, index) => (\n                                    <li key={index}>\n                                        <Link\n                                            href={item.href}\n                                            className=\"text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100 block duration-150\">\n                                            <span>{item.name}</span>\n                                        </Link>\n                                    </li>\n                                ))}\n                            </ul>\n                        </div>\n\n                        <div className={cn(\n                            \"bg-background mb-6 hidden w-full flex-wrap items-center justify-end space-y-8 rounded-3xl border p-6 shadow-2xl shadow-zinc-300/20 md:flex-nowrap lg:m-0 lg:flex lg:w-fit lg:gap-6 lg:space-y-0 lg:border-transparent lg:bg-transparent lg:p-0 lg:shadow-none dark:bg-[hsl(240_10%_3.9%)] dark:shadow-none dark:lg:bg-transparent\",\n                            menuState && \"block lg:flex\"\n                        )}>\n                            <div className=\"lg:hidden\">\n                                <ul className=\"space-y-4 sm:space-y-6 text-sm sm:text-base\">\n                                    {menuItems.map((item, index) => (\n                                        <li key={index}>\n                                            <Link\n                                                href={item.href}\n                                                className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-300 dark:hover:text-neutral-100 block duration-150\">\n                                                <span>{item.name}</span>\n                                            </Link>\n                                        </li>\n                                    ))}\n                                </ul>\n                            </div>\n                            <div className=\"flex flex-col space-y-3 sm:flex-row sm:gap-3 sm:space-y-0 md:w-fit\">\n                                {/* Only show MainThemeToggle in desktop mode or mobile menu */}\n                                <div className={cn(\"flex items-center gap-2\", !menuState && \"hidden lg:flex\")}>\n                                    <MainThemeToggle />\n                                    <LanguageSwitcher />\n                                </div>\n                                <SignInButton>\n                                    <Button\n                                        variant=\"outline\"\n                                        size=\"sm\"\n                                        className={cn(\n                                            \"text-sm sm:text-base font-semibold transition-all duration-300\",\n                                            isDarkMode \n                                                ? \"bg-transparent hover:bg-neutral-800\" \n                                                : \"bg-white hover:bg-neutral-100\",\n                                            isScrolled && 'lg:hidden'\n                                        )}>\n                                        <span>Sign In</span>\n                                    </Button>\n                                </SignInButton>\n                                <SignUpButton>\n                                    <Button\n                                        size=\"sm\"\n                                        className={cn(\n                                            \"text-sm sm:text-base font-semibold transition-all duration-300\",\n                                            isDarkMode \n                                                ? \"hover:bg-neutral-800\" \n                                                : \"hover:bg-neutral-800\",\n                                            isScrolled && 'lg:hidden'\n                                        )}>\n                                        <span>Try for free</span>\n                                    </Button>\n                                </SignUpButton>\n                                <SignUpButton>\n                                    <Button\n                                        size=\"sm\"\n                                        className={cn(\n                                            \"text-sm sm:text-base font-semibold transition-all duration-300\",\n                                            isDarkMode \n                                                ? \"hover:bg-neutral-800\" \n                                                : \"hover:bg-neutral-800\",\n                                            isScrolled ? 'lg:inline-flex' : 'hidden'\n                                        )}>\n                                        <span>Get Started</span>\n                                    </Button>\n                                </SignUpButton>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n        </header>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAYA,MAAM,YAAY;IACd;QAAE,MAAM;QAAW,MAAM;IAAgB;IACzC;QAAE,MAAM;QAAQ,MAAM;IAAQ;CACjC;AAEM,MAAM,aAAa;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,aAAa,UAAU;IAE7B,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,MAAM;qDAAe;oBACjB,cAAc,OAAO,OAAO,GAAG;gBACnC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACtD;+BAAG,EAAE;IAEL,qBACI,6LAAC;QAAO,WAAU;kBACd,cAAA,6LAAC;YACG,cAAY,aAAa;YACzB,WAAU;sBACV,cAAA,6LAAC;gBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACb,oFACA,cAAc;0BAEd,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,+JAAA,CAAA,UAAI;oCACD,MAAK;oCACL,cAAW;oCACX,WAAU;;sDACV,6LAAC,gIAAA,CAAA,UAAK;4CACF,KAAK,aAAa,wBAAwB;4CAC1C,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,UAAU;4CACV,WAAU;;;;;;sDAEd,6LAAC,gIAAA,CAAA,UAAK;4CACF,KAAK,aAAa,6BAA6B;4CAC/C,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,UAAU;4CACV,WAAU;;;;;;;;;;;;8CAIlB,6LAAC;oCACG,SAAS,IAAM,aAAa,CAAC;oCAC7B,cAAY,aAAa,OAAO,eAAe;oCAC/C,WAAU;;sDACV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAG,WAAU;0CACT,UAAU,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;kDACG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACD,MAAM,KAAK,IAAI;4CACf,WAAU;sDACV,cAAA,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;uCAJf;;;;;;;;;;;;;;;sCAWrB,6LAAC;4BAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACb,qUACA,aAAa;;8CAEb,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAG,WAAU;kDACT,UAAU,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;0DACG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACD,MAAM,KAAK,IAAI;oDACf,WAAU;8DACV,cAAA,6LAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;+CAJf;;;;;;;;;;;;;;;8CAUrB,6LAAC;oCAAI,WAAU;;sDAEX,6LAAC;4CAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B,CAAC,aAAa;;8DACxD,6LAAC,kJAAA,CAAA,kBAAe;;;;;8DAChB,6LAAC,4IAAA,CAAA,mBAAgB;;;;;;;;;;;sDAErB,6LAAC,8KAAA,CAAA,eAAY;sDACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACH,SAAQ;gDACR,MAAK;gDACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACR,kEACA,aACM,wCACA,iCACN,cAAc;0DAElB,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;sDAGd,6LAAC,8KAAA,CAAA,eAAY;sDACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACH,MAAK;gDACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACR,kEACA,aACM,yBACA,wBACN,cAAc;0DAElB,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;sDAGd,6LAAC,8KAAA,CAAA,eAAY;sDACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACH,MAAK;gDACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACR,kEACA,aACM,yBACA,wBACN,aAAa,mBAAmB;0DAEpC,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C;GA7Ia;;QAGS,oJAAA,CAAA,eAAY;;;KAHrB", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/containers/main/hero-section.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport Link from 'next/link'\nimport { ArrowRight, ChevronRight } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { TextEffect } from '@/components/motion-primitives/text-effect'\nimport { AnimatedGroup } from '@/components/motion-primitives/animated-group'\nimport { HeroHeader } from '@/containers/main/hero5-header'\nimport { MainThemeProvider } from '@/components/theme/main-theme-provider'\nimport { useMainTheme } from '@/components/theme/main-theme-provider'\nimport { useLanguage } from '@/app/providers/language-provider'\nimport { SignUpButton } from \"@clerk/nextjs\";\n\nconst transitionVariants = {\n    item: {\n        hidden: {\n            opacity: 0,\n            filter: 'blur(12px)',\n            y: 12,\n        },\n        visible: {\n            opacity: 1,\n            filter: 'blur(0px)',\n            y: 0,\n            transition: {\n                type: 'spring',\n                bounce: 0.3,\n                duration: 1.5,\n            },\n        },\n    },\n}\n\nexport default function HeroSection() {\n    return (\n        <MainThemeProvider>\n            <HeroContent />\n        </MainThemeProvider>\n    )\n}\n\nfunction HeroContent() {\n    const { theme } = useMainTheme()\n    const { t } = useLanguage()\n    const isDarkMode = theme === 'dark'\n    \n    return (\n        <>\n            <HeroHeader />\n            <main className=\"overflow-hidden dark:bg-[hsl(240_10%_3.9%)]\">\n                {/* Diagonal light */}\n                <div\n                    aria-hidden\n                    className=\"absolute inset-0 isolate opacity-40 hidden lg:block\">\n                    <div className=\"absolute left-0 top-0 w-[560px] h-[1280px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.04)_0,hsla(0,0%,55%,.01)_50%,hsla(0,0%,45%,0)_80%)]\" />\n                    <div className=\"absolute left-0 top-0 w-[240px] h-[1280px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.03)_0,hsla(0,0%,45%,.01)_80%,transparent_100%)] translate-x-[5%] -translate-y-[50%]\" />\n                    <div className=\"absolute left-0 top-0 w-[240px] h-[1280px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.02)_0,hsla(0,0%,45%,.01)_80%,transparent_100%)]\" />\n                </div>\n                <section>\n                    <div className=\"relative pt-20 sm:pt-24 md:pt-36\">\n                        {/*------------------- Background-------------------- */}\n                        <AnimatedGroup\n                            variants={{\n                                container: {\n                                    visible: {\n                                        transition: {\n                                            delayChildren: 1,\n                                        },\n                                    },\n                                },\n                                item: {\n                                    hidden: {\n                                        opacity: 0,\n                                        y: 20,\n                                    },\n                                    visible: {\n                                        opacity: 1,\n                                        y: 0,\n                                        transition: {\n                                            type: 'spring',\n                                            bounce: 0.3,\n                                            duration: 2,\n                                        },\n                                    },\n                                },\n                            }}\n                            className=\"absolute inset-0 -z-20\">\n                            <div className=\"absolute inset-0 bg-gradient-to-b from-background/10 to-background/20\" />\n                        </AnimatedGroup>\n                        {/* ------------Background----------------*/}\n\n\n                        {/* Background for enhanced visibility */}\n                        <div className=\"absolute inset-0 -z-10 size-full [background:radial-gradient(125%_125%_at_50%_100%,transparent_0%,var(--color-background)_75%)]\"></div>\n                        <div className=\"mx-auto max-w-7xl px-4 sm:px-6\">\n                            <div className=\"text-center sm:mx-auto lg:mr-auto lg:mt-0\">\n                                <AnimatedGroup variants={transitionVariants}>\n                                    <Link\n                                        href=\"#link\"\n                                        className=\"hover:bg-background dark:hover:border-t-border bg-muted group mx-auto flex w-fit items-center gap-2 sm:gap-4 rounded-full border p-1 pl-2 sm:pl-4 shadow-md shadow-zinc-950/5 transition-colors duration-300 dark:border-t-white/5 dark:shadow-zinc-950\">\n                                        <span className=\"text-gray-800 text-xs sm:text-sm dark:text-gray-200\">{t('hero.aiEvidenceSearch')}</span>\n                                        <span className=\"border-gray-300 dark:border-background block h-4 w-0.5 border-l bg-gray-400  dark:bg-neutral-700\"></span>\n\n                                        <div className=\"bg-background group-hover:bg-muted size-5 sm:size-6 overflow-hidden rounded-full duration-500\">\n                                            <div className=\"flex w-10 sm:w-12 -translate-x-1/2 duration-500 ease-in-out group-hover:translate-x-0\">\n                                                <span className=\"flex size-5 sm:size-6\">\n                                                    <ArrowRight className=\"m-auto size-2 sm:size-3 text-gray-800 dark:text-gray-200\" />\n                                                </span>\n                                                <span className=\"flex size-5 sm:size-6\">\n                                                    <ArrowRight className=\"m-auto size-2 sm:size-3 text-gray-800 dark:text-gray-200\" />\n                                                </span>\n                                            </div>\n                                        </div>\n                                    </Link>\n                                </AnimatedGroup>\n\n                                <TextEffect\n                                    preset=\"fade-in-blur\"\n                                    speedSegment={0.3}\n                                    as=\"h1\"\n                                    className=\"mt-6 sm:mt-8 text-balance text-gray-900 dark:text-gray-100 text-4xl sm:text-5xl md:text-6xl lg:text-7xl lg:mt-16 xl:text-[5.25rem]\">\n                                    {t('hero.title')}\n                                </TextEffect>\n                                <TextEffect\n                                    per=\"line\"\n                                    preset=\"fade-in-blur\"\n                                    speedSegment={0.3}\n                                    delay={0.5}\n                                    as=\"p\"\n                                    className=\"mx-auto mt-4 sm:mt-6 md:mt-8 max-w-2xl text-balance text-gray-700 dark:text-gray-300 text-base sm:text-lg\">\n                                    {t('hero.subtitle')}\n                                </TextEffect>\n\n                                <AnimatedGroup\n                                    variants={{\n                                        container: {\n                                            visible: {\n                                                transition: {\n                                                    staggerChildren: 0.05,\n                                                    delayChildren: 0.75,\n                                                },\n                                            },\n                                        },\n                                        ...transitionVariants,\n                                    }}\n                                    className=\"mt-8 sm:mt-10 md:mt-12 flex flex-col items-center justify-center gap-2 md:flex-row\">\n                                    <div\n                                        key={1}\n                                        className=\"bg-foreground/10 rounded-[calc(var(--radius-xl)+0.125rem)] border p-0.5 w-full max-w-xs sm:w-auto\">\n                                        <SignUpButton>\n                                            <Button\n                                                size=\"lg\"\n                                                className=\"rounded-xl px-4 sm:px-5 text-sm sm:text-base w-full sm:w-auto\">\n                                                <span className=\"text-nowrap\">{t('hero.tryForFree')}</span>\n                                            </Button>\n                                        </SignUpButton>\n                                    </div>\n                                  \n                                </AnimatedGroup>\n                            </div>\n                        </div>\n\n                        <AnimatedGroup\n                            variants={{\n                                container: {\n                                    visible: {\n                                        transition: {\n                                            staggerChildren: 0.05,\n                                            delayChildren: 0.75,\n                                        },\n                                    },\n                                },\n                                ...transitionVariants,\n                            }}>\n                            <div className=\"relative mt-8 overflow-hidden px-2 sm:mt-12 md:mt-20\">\n                                \n                                <div className=\"inset-shadow-2xs opacity-90 dark:ring-[hsl(240_10%_3.9%)] dark:inset-shadow-white/20 bg-background dark:bg-[hsl(240_10%_3.9%)] relative mx-auto max-w-6xl overflow-hidden rounded-lg sm:rounded-2xl border\">\n                                    <video \n                                        className=\"aspect-16/9 w-full rounded-lg object-cover\"\n                                        src=\"/Flashcards demo.mp4\"\n                                        autoPlay\n                                        loop\n                                        muted\n                                        playsInline\n                                        controls={false}\n                                    />\n                                </div>\n                            </div>\n                        </AnimatedGroup>\n                    </div>\n                </section>\n                {/*-------------------CUSTOMERS SECTION --------------------------*/}\n                <section className=\"bg-white dark:bg-[hsl(240_10%_3.9%)] mt-16 sm:mt-20 md:mt-28\">\n                    <div className=\"group relative m-auto max-w-5xl px-4 sm:px-6\">\n                        <div className=\"absolute inset-0 z-10 flex scale-95 items-center justify-center opacity-0 duration-500 group-hover:scale-100 group-hover:opacity-100\">\n                            <Link\n                                href=\"/\"\n                                className=\"block text-sm text-gray-900 dark:text-gray-100 duration-150 hover:opacity-75\">\n                                <span>{t('hero.trustedBy')}</span>\n\n                                <ChevronRight className=\"ml-1 inline-block size-3\" />\n                            </Link>\n                        </div>\n                        <h2 className=\"text-center text-xl sm:text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 mb-6 sm:mb-8\">{t('hero.trustedByTitle')}</h2>\n                        <div className=\"group-hover:blur-xs mx-auto mt-4 sm:mt-6 grid max-w-3xl grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 sm:gap-x-6 md:gap-x-8 gap-y-8 sm:gap-y-10 transition-all duration-500 group-hover:opacity-50\">\n                            <div className=\"flex flex-col items-center transition-transform hover:scale-105\">\n                                <div className={`rounded-full p-3 sm:p-4 mb-3 sm:mb-4 ${isDarkMode ? 'bg-primary/20' : 'bg-primary/10'}`}>\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-primary h-6 w-6 sm:h-8 sm:w-8\">\n                                        <path d=\"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z\"></path>\n                                        <path d=\"M8 7V3m8 4V3\"></path>\n                                        <path d=\"M12 12v3\"></path>\n                                        <path d=\"M9 13h6\"></path>\n                                    </svg>\n                                </div>\n                                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 dark:text-gray-100\">{t('hero.medicalStudentsTitle')}</h3>\n                                <p className=\"text-center text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base\">{t('hero.medicalStudentsDesc')}</p>\n                            </div>\n                            \n                            <div className=\"flex flex-col items-center transition-transform hover:scale-105\">\n                                <div className={`rounded-full p-3 sm:p-4 mb-3 sm:mb-4 ${isDarkMode ? 'bg-primary/20' : 'bg-primary/10'}`}>\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-primary h-6 w-6 sm:h-8 sm:w-8\">\n                                        <path d=\"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\"></path>\n                                        <path d=\"M12 5 9.04 7.96a2.17 2.17 0 0 0 0 3.08v0c.82.82 2.13.85 3 .07l2.07-1.9a2.82 2.82 0 0 1 3.79 0l2.96 2.66\"></path>\n                                        <path d=\"m18 15-2-2\"></path>\n                                        <path d=\"m15 18-2-2\"></path>\n                                    </svg>\n                                </div>\n                                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 dark:text-gray-100\">{t('hero.healthcareProfessionalsTitle')}</h3>\n                                <p className=\"text-center text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base\">{t('hero.healthcareProfessionalsDesc')}</p>\n                            </div>\n                            \n                            <div className=\"flex flex-col items-center transition-transform hover:scale-105\">\n                                <div className={`rounded-full p-3 sm:p-4 mb-3 sm:mb-4 ${isDarkMode ? 'bg-primary/20' : 'bg-primary/10'}`}>\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-primary h-6 w-6 sm:h-8 sm:w-8\">\n                                        <path d=\"M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3\"></path>\n                                        <path d=\"M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4\"></path>\n                                        <circle cx=\"20\" cy=\"10\" r=\"2\"></circle>\n                                    </svg>\n                                </div>\n                                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 dark:text-gray-100\">{t('hero.doctorsTitle')}</h3>\n                                <p className=\"text-center text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base\">{t('hero.doctorsDesc')}</p>\n                            </div>\n                        </div>\n                    </div>\n                   \n                </section>\n            </main>\n        </>\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAXA;;;;;;;;;;;AAaA,MAAM,qBAAqB;IACvB,MAAM;QACF,QAAQ;YACJ,SAAS;YACT,QAAQ;YACR,GAAG;QACP;QACA,SAAS;YACL,SAAS;YACT,QAAQ;YACR,GAAG;YACH,YAAY;gBACR,MAAM;gBACN,QAAQ;gBACR,UAAU;YACd;QACJ;IACJ;AACJ;AAEe,SAAS;IACpB,qBACI,6LAAC,oJAAA,CAAA,oBAAiB;kBACd,cAAA,6LAAC;;;;;;;;;;AAGb;KANwB;AAQxB,SAAS;;IACL,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IACxB,MAAM,aAAa,UAAU;IAE7B,qBACI;;0BACI,6LAAC,yIAAA,CAAA,aAAU;;;;;0BACX,6LAAC;gBAAK,WAAU;;kCAEZ,6LAAC;wBACG,aAAW;wBACX,WAAU;;0CACV,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEnB,6LAAC;kCACG,cAAA,6LAAC;4BAAI,WAAU;;8CAEX,6LAAC,2JAAA,CAAA,gBAAa;oCACV,UAAU;wCACN,WAAW;4CACP,SAAS;gDACL,YAAY;oDACR,eAAe;gDACnB;4CACJ;wCACJ;wCACA,MAAM;4CACF,QAAQ;gDACJ,SAAS;gDACT,GAAG;4CACP;4CACA,SAAS;gDACL,SAAS;gDACT,GAAG;gDACH,YAAY;oDACR,MAAM;oDACN,QAAQ;oDACR,UAAU;gDACd;4CACJ;wCACJ;oCACJ;oCACA,WAAU;8CACV,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAMnB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,2JAAA,CAAA,gBAAa;gDAAC,UAAU;0DACrB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACD,MAAK;oDACL,WAAU;;sEACV,6LAAC;4DAAK,WAAU;sEAAuD,EAAE;;;;;;sEACzE,6LAAC;4DAAK,WAAU;;;;;;sEAEhB,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEAAK,WAAU;kFACZ,cAAA,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;kFAE1B,6LAAC;wEAAK,WAAU;kFACZ,cAAA,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAO1C,6LAAC,wJAAA,CAAA,aAAU;gDACP,QAAO;gDACP,cAAc;gDACd,IAAG;gDACH,WAAU;0DACT,EAAE;;;;;;0DAEP,6LAAC,wJAAA,CAAA,aAAU;gDACP,KAAI;gDACJ,QAAO;gDACP,cAAc;gDACd,OAAO;gDACP,IAAG;gDACH,WAAU;0DACT,EAAE;;;;;;0DAGP,6LAAC,2JAAA,CAAA,gBAAa;gDACV,UAAU;oDACN,WAAW;wDACP,SAAS;4DACL,YAAY;gEACR,iBAAiB;gEACjB,eAAe;4DACnB;wDACJ;oDACJ;oDACA,GAAG,kBAAkB;gDACzB;gDACA,WAAU;0DACV,cAAA,6LAAC;oDAEG,WAAU;8DACV,cAAA,6LAAC,8KAAA,CAAA,eAAY;kEACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;4DACH,MAAK;4DACL,WAAU;sEACV,cAAA,6LAAC;gEAAK,WAAU;0EAAe,EAAE;;;;;;;;;;;;;;;;mDANpC;;;;;;;;;;;;;;;;;;;;;8CAerB,6LAAC,2JAAA,CAAA,gBAAa;oCACV,UAAU;wCACN,WAAW;4CACP,SAAS;gDACL,YAAY;oDACR,iBAAiB;oDACjB,eAAe;gDACnB;4CACJ;wCACJ;wCACA,GAAG,kBAAkB;oCACzB;8CACA,cAAA,6LAAC;wCAAI,WAAU;kDAEX,cAAA,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDACG,WAAU;gDACV,KAAI;gDACJ,QAAQ;gDACR,IAAI;gDACJ,KAAK;gDACL,WAAW;gDACX,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,6LAAC;wBAAQ,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACD,MAAK;wCACL,WAAU;;0DACV,6LAAC;0DAAM,EAAE;;;;;;0DAET,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGhC,6LAAC;oCAAG,WAAU;8CAA0G,EAAE;;;;;;8CAC1H,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,kBAAkB,iBAAiB;8DACpG,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;wDAAO,QAAO;wDAAe,aAAY;wDAAI,eAAc;wDAAQ,gBAAe;wDAAQ,WAAU;;0EACxL,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;8DAGhB,6LAAC;oDAAG,WAAU;8DAAqE,EAAE;;;;;;8DACrF,6LAAC;oDAAE,WAAU;8DAA0E,EAAE;;;;;;;;;;;;sDAG7F,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,kBAAkB,iBAAiB;8DACpG,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;wDAAO,QAAO;wDAAe,aAAY;wDAAI,eAAc;wDAAQ,gBAAe;wDAAQ,WAAU;;0EACxL,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;8DAGhB,6LAAC;oDAAG,WAAU;8DAAqE,EAAE;;;;;;8DACrF,6LAAC;oDAAE,WAAU;8DAA0E,EAAE;;;;;;;;;;;;sDAG7F,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,kBAAkB,iBAAiB;8DACpG,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;wDAAO,QAAO;wDAAe,aAAY;wDAAI,eAAc;wDAAQ,gBAAe;wDAAQ,WAAU;;0EACxL,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAK,GAAE;;;;;;0EACR,6LAAC;gEAAO,IAAG;gEAAK,IAAG;gEAAK,GAAE;;;;;;;;;;;;;;;;;8DAGlC,6LAAC;oDAAG,WAAU;8DAAqE,EAAE;;;;;;8DACrF,6LAAC;oDAAE,WAAU;8DAA0E,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzH;GAhNS;;QACa,oJAAA,CAAA,eAAY;QAChB,4IAAA,CAAA,cAAW;;;MAFpB", "debugId": null}}, {"offset": {"line": 1863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/containers/footer/footer.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useLanguage } from '@/app/providers/language-provider';\n\nexport default function Footer() {\n    const { t } = useLanguage();\n\n    return (\n        <footer className=\"py-16 md:py-32 dark:bg-[hsl(240_10%_3.9%)]\">\n            <div className=\"mx-auto max-w-5xl px-6\">\n                <Link href=\"/\" aria-label=\"go home\" className=\"mx-auto block size-fit\">\n                    <Image \n                        src=\"/decodemed_text_dark.svg\" \n                        alt=\"DecodeMed Logo\" \n                        width={180} \n                        height={40}\n                        className=\"h-10 w-auto hidden dark:block\" \n                    />\n                    <Image \n                        src=\"/decodemed_text.svg\" \n                        alt=\"DecodeMed Logo\" \n                        width={180} \n                        height={40}\n                        className=\"h-10 w-auto dark:hidden\" \n                    />\n                </Link>\n\n                <div className=\"my-8 flex flex-wrap justify-center gap-6 text-sm\">\n                    <Link href=\"/home/<USER>\" className=\"text-muted-foreground hover:text-primary block duration-150\">\n                        {t('footer.contact')}\n                    </Link>\n                    <span className=\"text-muted-foreground\">•</span>\n                    <Link href=\"/home/<USER>/privacy-policy\" className=\"text-muted-foreground hover:text-primary block duration-150\">\n                        {t('footer.privacyPolicy')}\n                    </Link>\n                    <span className=\"text-muted-foreground\">•</span>\n                    <Link href=\"/home/<USER>/terms-of-service\" className=\"text-muted-foreground hover:text-primary block duration-150\">\n                        {t('footer.termsOfService')}\n                    </Link>\n                </div>\n                <div className=\"my-8 flex flex-wrap justify-center gap-6 text-sm\">\n                    {/* X icon */}\n                    <Link href=\"https://x.com/Decode_Med\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"X/Twitter\" className=\"text-muted-foreground hover:text-primary block\">\n                        <svg className=\"size-6\" xmlns=\"http://www.w3.org/2000/svg\" width=\"1em\" height=\"1em\" viewBox=\"0 0 24 24\">\n                            <path fill=\"currentColor\" d=\"M10.488 14.651L15.25 21h7l-7.858-10.478L20.93 3h-2.65l-5.117 5.886L8.75 3h-7l7.51 10.015L2.32 21h2.65zM16.25 19L5.75 5h2l10.5 14z\"></path>\n                        </svg>\n                    </Link>\n                    {/* LinkedIn icon */}\n                    <Link href=\"https://www.linkedin.com/company/decodemed\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"LinkedIn\" className=\"text-muted-foreground hover:text-primary block\">\n                        <svg className=\"size-6\" xmlns=\"http://www.w3.org/2000/svg\" width=\"1em\" height=\"1em\" viewBox=\"0 0 24 24\">\n                            <path fill=\"currentColor\" d=\"M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93zM6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.68 1.68 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37z\"></path>\n                        </svg>\n                    </Link>\n                    {/* Facebook icon */}\n                    <Link href=\"https://www.facebook.com/profile.php?id=61571722133982\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"Facebook\" className=\"text-muted-foreground hover:text-primary block\">\n                        <svg className=\"size-6\" xmlns=\"http://www.w3.org/2000/svg\" width=\"1em\" height=\"1em\" viewBox=\"0 0 24 24\">\n                            <path fill=\"currentColor\" d=\"M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-2c-.55 0-1 .45-1 1v2h3v3h-3v6.95c5.05-.5 9-4.76 9-9.95\"></path>\n                        </svg>\n                    </Link>\n                   \n                    {/* Instagram icon */}\n                    <Link href=\"https://www.instagram.com/decodemed01\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"Instagram\" className=\"text-muted-foreground hover:text-primary block\">\n                        <svg className=\"size-6\" xmlns=\"http://www.w3.org/2000/svg\" width=\"1em\" height=\"1em\" viewBox=\"0 0 24 24\">\n                            <path\n                                fill=\"currentColor\"\n                                d=\"M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4zm9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8A1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5a5 5 0 0 1-5 5a5 5 0 0 1-5-5a5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3\"></path>\n                        </svg>\n                    </Link>\n                    {/* TikTok icon */}\n                    <Link href=\"https://www.tiktok.com/@decodemed\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"TikTok\" className=\"text-muted-foreground hover:text-primary block\">\n                        <svg className=\"size-6\" xmlns=\"http://www.w3.org/2000/svg\" width=\"1em\" height=\"1em\" viewBox=\"0 0 24 24\">\n                            <path fill=\"currentColor\" d=\"M16.6 5.82s.51.5 0 0A4.28 4.28 0 0 1 15.54 3h-3.09v12.4a2.59 2.59 0 0 1-2.59 2.5c-1.42 0-2.6-1.16-2.6-2.6c0-1.72 1.66-3.01 3.37-2.48V9.66c-3.45-.46-6.47 2.22-6.47 5.64c0 3.33 2.76 5.7 5.69 5.7c3.14 0 5.69-2.55 5.69-5.7V9.01a7.35 7.35 0 0 0 4.3 1.38V7.3s-1.88.09-3.24-1.48\"></path>\n                        </svg>\n                    </Link>\n                </div>\n                <span className=\"text-muted-foreground block text-center text-sm\"> {new Date().getFullYear()} DecodeMed, {t('footer.allRightsReserved')}</span>\n            </div>\n        </footer>\n    )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACpB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IAExB,qBACI,6LAAC;QAAO,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,cAAW;oBAAU,WAAU;;sCAC1C,6LAAC,gIAAA,CAAA,UAAK;4BACF,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEd,6LAAC,gIAAA,CAAA,UAAK;4BACF,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;8BAIlB,6LAAC;oBAA<PERSON>,WAAU;;sCACX,6LAAC,+JAA<PERSON>,CAAA,UAAI;4BAAC,MAAK;4BAAgB,WAAU;sCAChC,EAAE;;;;;;sCAEP,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;sCACxC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAgC,WAAU;sCAChD,EAAE;;;;;;sCAEP,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;sCACxC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAkC,WAAU;sCAClD,EAAE;;;;;;;;;;;;8BAGX,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAA2B,QAAO;4BAAS,KAAI;4BAAsB,cAAW;4BAAY,WAAU;sCAC7G,cAAA,6LAAC;gCAAI,WAAU;gCAAS,OAAM;gCAA6B,OAAM;gCAAM,QAAO;gCAAM,SAAQ;0CACxF,cAAA,6LAAC;oCAAK,MAAK;oCAAe,GAAE;;;;;;;;;;;;;;;;sCAIpC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAA6C,QAAO;4BAAS,KAAI;4BAAsB,cAAW;4BAAW,WAAU;sCAC9H,cAAA,6LAAC;gCAAI,WAAU;gCAAS,OAAM;gCAA6B,OAAM;gCAAM,QAAO;gCAAM,SAAQ;0CACxF,cAAA,6LAAC;oCAAK,MAAK;oCAAe,GAAE;;;;;;;;;;;;;;;;sCAIpC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAyD,QAAO;4BAAS,KAAI;4BAAsB,cAAW;4BAAW,WAAU;sCAC1I,cAAA,6LAAC;gCAAI,WAAU;gCAAS,OAAM;gCAA6B,OAAM;gCAAM,QAAO;gCAAM,SAAQ;0CACxF,cAAA,6LAAC;oCAAK,MAAK;oCAAe,GAAE;;;;;;;;;;;;;;;;sCAKpC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAwC,QAAO;4BAAS,KAAI;4BAAsB,cAAW;4BAAY,WAAU;sCAC1H,cAAA,6LAAC;gCAAI,WAAU;gCAAS,OAAM;gCAA6B,OAAM;gCAAM,QAAO;gCAAM,SAAQ;0CACxF,cAAA,6LAAC;oCACG,MAAK;oCACL,GAAE;;;;;;;;;;;;;;;;sCAId,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAoC,QAAO;4BAAS,KAAI;4BAAsB,cAAW;4BAAS,WAAU;sCACnH,cAAA,6LAAC;gCAAI,WAAU;gCAAS,OAAM;gCAA6B,OAAM;gCAAM,QAAO;gCAAM,SAAQ;0CACxF,cAAA,6LAAC;oCAAK,MAAK;oCAAe,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAIxC,6LAAC;oBAAK,WAAU;;wBAAkD;wBAAE,IAAI,OAAO,WAAW;wBAAG;wBAAa,EAAE;;;;;;;;;;;;;;;;;;AAI5H;GA3EwB;;QACN,4IAAA,CAAA,cAAW;;;KADL", "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/containers/main/features-section.tsx"], "sourcesContent": ["'use client'\n\nimport { Activity, DraftingCompass, Book, Zap } from \"lucide-react\"\nimport { useLanguage } from '@/app/providers/language-provider'\n\nexport default function FeaturesSection() {\n  const { t } = useLanguage()\n  return (\n    <section className=\"py-12 sm:py-16 md:py-24 lg:py-32 dark:bg-[hsl(240_10%_3.9%)]\">\n      <div className=\"mx-auto max-w-6xl px-4 sm:px-6\">\n        <div className=\"grid items-center gap-8 sm:gap-10 md:gap-12 md:grid-cols-2 lg:grid-cols-5 lg:gap-16 xl:gap-24\">\n          <div className=\"lg:col-span-2\">\n            <div className=\"md:pr-4 lg:pr-0\">\n              <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-semibold text-gray-900 dark:text-gray-100 lg:text-5xl\">{t('features.title')}</h2>\n              <p className=\"mt-4 sm:mt-6 text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300\">\n                {t('features.description')}\n              </p>\n            </div>\n            <ul className=\"mt-6 sm:mt-8 divide-y border-y text-sm sm:text-base text-gray-800 dark:text-gray-200 *:flex *:items-center *:gap-2 sm:*:gap-3 *:py-2 sm:*:py-3\">\n              \n              <li>\n                <Book className=\"size-4 sm:size-5\" />\n                {t('features.listItem1')}\n              </li>\n              <li>\n                <DraftingCompass className=\"size-4 sm:size-5\" />\n                {t('features.listItem2')}\n              </li>\n              <li>\n                <Zap className=\"size-4 sm:size-5\" />\n                {t('features.listItem3')}\n              </li>\n              <li>\n                <Activity className=\"size-4 sm:size-5\" />\n                {t('features.listItem4')}\n              </li>\n            </ul>\n          </div>\n          <div className=\"border-border/50 relative rounded-xl sm:rounded-2xl md:rounded-3xl lg:col-span-3 aspect-76/59 mt-6 sm:mt-0 dark:from-zinc-700\">\n            <video\n              src=\"https://1kl8egk7md.ufs.sh/f/I3tdzkulYJ0GJvhcMg5abeLqrFUinZ07dWQRlxp41hsYkzXE\"\n              className=\"rounded-xl sm:rounded-[15px] w-full h-auto\"\n              controls={false}\n              autoPlay\n              muted\n              loop\n            />\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IACxB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+F,EAAE;;;;;;kDAC/G,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAGP,6LAAC;gCAAG,WAAU;;kDAEZ,6LAAC;;0DACC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,EAAE;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC,+NAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;4CAC1B,EAAE;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd,EAAE;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,EAAE;;;;;;;;;;;;;;;;;;;kCAIT,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAI;4BACJ,WAAU;4BACV,UAAU;4BACV,QAAQ;4BACR,KAAK;4BACL,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA/CwB;;QACR,4IAAA,CAAA,cAAW;;;KADH", "debugId": null}}, {"offset": {"line": 2354, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/containers/main/feature-two-section.tsx"], "sourcesContent": ["'use client'\n\n// No imports needed for video element\nimport { useLanguage } from '@/app/providers/language-provider'\n\nexport default function ContentSection() {\n    const { t } = useLanguage()\n    return (\n        <section className=\"py-12 sm:py-16 md:py-24 lg:py-32 dark:bg-[hsl(240_10%_3.9%)]\">\n            <div className=\"mx-auto max-w-5xl space-y-6 sm:space-y-8 md:space-y-12 lg:space-y-16 px-4 sm:px-6\">\n                <h2 className=\"relative z-10 max-w-xl text-2xl sm:text-3xl md:text-4xl font-medium text-gray-900 dark:text-gray-100 lg:text-5xl\">{t('featureTwo.title')}</h2>\n                <div className=\"grid gap-6 md:gap-8 lg:gap-12 xl:gap-24 md:grid-cols-2\">\n                    <div className=\"relative mb-4 sm:mb-6 md:mb-0 order-2 md:order-1\">\n                        <div className=\"border-border/50 relative rounded-xl sm:rounded-2xl md:rounded-3xl aspect-video dark:from-zinc-700\">\n                            <video \n                                src=\"https://1kl8egk7md.ufs.sh/f/I3tdzkulYJ0GsXxRteZBK1jYgzfUuy9mpxFkvLA5HoRC4teT\" \n                                className=\"rounded-xl sm:rounded-[15px] w-full h-auto\" \n                                controls={false}\n                                autoPlay\n                                muted\n                                loop\n                            />\n                        </div>\n                    </div>\n\n                    <div className=\"relative space-y-3 sm:space-y-4 order-1 md:order-2\">\n                        <p \n                            className=\"text-sm sm:text-base md:text-lg text-muted-foreground dark:text-gray-300\"\n                            dangerouslySetInnerHTML={{ __html: t('featureTwo.description1') }}\n                        >\n                        </p>\n                        <p className=\"text-sm sm:text-base md:text-lg text-muted-foreground dark:text-gray-300\">\n                            {t('featureTwo.description2')}\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </section>\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA,sCAAsC;AACtC;;;AAHA;;AAKe,SAAS;;IACpB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IACxB,qBACI,6LAAC;QAAQ,WAAU;kBACf,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAG,WAAU;8BAAoH,EAAE;;;;;;8BACpI,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCACG,KAAI;oCACJ,WAAU;oCACV,UAAU;oCACV,QAAQ;oCACR,KAAK;oCACL,IAAI;;;;;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCACG,WAAU;oCACV,yBAAyB;wCAAE,QAAQ,EAAE;oCAA2B;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CACR,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GAlCwB;;QACN,4IAAA,CAAA,cAAW;;;KADL", "debugId": null}}]}