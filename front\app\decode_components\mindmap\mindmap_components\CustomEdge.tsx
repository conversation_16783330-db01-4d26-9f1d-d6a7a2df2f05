import React from 'react';
import { EdgeProps, getBezierPath, BaseEdge } from 'reactflow';

const CustomEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {}, // Default to empty object if style is not provided
  markerEnd,
}) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    curvature: 0.4
  });

  // Use stroke from style prop, default to a fallback color if not present
  const edgeStyle = {
    stroke: style.stroke || '#b1b1b7', // Fallback color (light gray)
    strokeWidth: 2, // Make lines slightly thicker
    ...style, // Allow overriding strokeWidth or adding other styles
  };


  return (
    <>
      <BaseEdge id={id} path={edgePath} markerEnd={markerEnd} style={edgeStyle} />
    </>
  );
};

export default CustomEdge; 