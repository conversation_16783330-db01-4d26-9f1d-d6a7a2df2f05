import { X } from 'lucide-react';

interface SearchHistoryProps {
    isHistoryVisible: boolean;
    toggleHistoryVisibility: () => void;
    searchHistory: Array<{ id: number; question: string; created_at: string }>;
    setQuestion: (question: string) => void;
  }
  
// Mobile History Component
const MobileHistory: React.FC<SearchHistoryProps> = ({
  isHistoryVisible,
  toggleHistoryVisibility,
  searchHistory,
  setQuestion,
}) => {
  // Function to organize history items by time period
  const organizeHistoryByTime = (history: typeof searchHistory) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const organized = {
      today: [] as typeof searchHistory,
      yesterday: [] as typeof searchHistory,
      lastWeek: [] as typeof searchHistory,
      lastMonth: [] as typeof searchHistory,
      older: [] as typeof searchHistory,
    };

    history.forEach(item => {
      const itemDate = new Date(item.created_at);
      const itemDay = new Date(itemDate.getFullYear(), itemDate.getMonth(), itemDate.getDate());

      if (itemDay.getTime() === today.getTime()) {
        organized.today.push(item);
      } else if (itemDay.getTime() === yesterday.getTime()) {
        organized.yesterday.push(item);
      } else if (itemDay >= lastWeek && itemDay < yesterday) {
        organized.lastWeek.push(item);
      } else if (itemDay >= lastMonth && itemDay < lastWeek) {
        organized.lastMonth.push(item);
      } else {
        organized.older.push(item);
      }
    });

    return organized;
  };

  // Helper function to render history items
  const renderHistoryItems = (items: typeof searchHistory, title: string) => {
    if (items.length === 0) return null;

    return (
      <div className="mb-4">
        <h3 className="text-xs font-semibold text-gray-500 mb-2">{title}</h3>
        <ul className="space-y-2">
          {items.map((item) => (
            <li key={item.id} className="border-b border-gray-100 pb-2">
              <button
                onClick={() => {
                  setQuestion(item.question);
                  toggleHistoryVisibility();
                }}
                className="text-left hover:bg-slate-200 hover:rounded-lg p-1 w-full text-sm line-clamp-2"
                title={item.question}
              >
                {item.question}
              </button>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  const organizedHistory = organizeHistoryByTime(searchHistory);

  return (
    <>
      <div 
        className="fixed inset-0 bg-black bg-opacity-25 z-40 md:hidden"
        onClick={toggleHistoryVisibility}
      />
      
      <div className={`
        fixed inset-y-0 right-0 w-[50%] bg-white z-50 
        transform transition-transform duration-300 ease-in-out
        overflow-y-auto md:hidden
        ${isHistoryVisible ? 'translate-x-0' : 'translate-x-full'}
      `}>
        <div className="sticky top-0 bg-white border-b z-10">
          <div className="p-4 flex justify-between items-center">
            <span className="text-lg font-medium">History</span>
            <button
              onClick={toggleHistoryVisibility}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <X size={24} />
            </button>
          </div>
        </div>
        
        <div className="p-4">
          {renderHistoryItems(organizedHistory.today, 'Today')}
          {renderHistoryItems(organizedHistory.yesterday, 'Yesterday')}
          {renderHistoryItems(organizedHistory.lastWeek, 'Last Week')}
          {renderHistoryItems(organizedHistory.lastMonth, 'Last Month')}
          {renderHistoryItems(organizedHistory.older, 'Older')}
        </div>
      </div>
    </>
  );
};

export default MobileHistory;