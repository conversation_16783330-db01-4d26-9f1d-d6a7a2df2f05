import unittest
import asyncio
import os
from ..chat_ai.chat_ranking import rank_articles

class TestChatRanking(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # Ensure we have an API key
        if not os.getenv('OPENAI_API_KEY'):
            raise unittest.SkipTest("OPENAI_API_KEY not set in environment variables")

    def setUp(self):
        self.question = "What is the impact of GLP-1 RAs on skin?"
        self.chat_history = []
        # Create a new event loop for each test
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        # Clean up the event loop after each test
        self.loop.close()

    def test_rank_articles(self):
        try:
            # Create and run the coroutine in the test's event loop
            articles = self.loop.run_until_complete(rank_articles(self.question, self.chat_history))
            
            # Test if articles were returned
            self.assertIsNotNone(articles)
            self.assertIsInstance(articles, list)
            
            # Only proceed with other tests if we have articles
            if articles:
                # Test structure of first article
                first_article = articles[0]
                required_keys = [
                    'title', 'authors', 'pmid', 'publisher', 
                    'publication_date', 'doi', 'article_link'
                ]
                
                for key in required_keys:
                    self.assertIn(key, first_article, f"Missing key: {key}")
                
                # Test data types of article fields
                self.assertIsInstance(first_article['title'], str)
                self.assertIsInstance(first_article['authors'], list)
                self.assertIsInstance(first_article['pmid'], str)
                self.assertIsInstance(first_article['publisher'], str)
                self.assertIsInstance(first_article['publication_date'], str)
                
                # Print first 10 articles (or all if less than 10)
                num_articles = min(10, len(articles))
                print(f"\nShowing Top {num_articles} Ranked Articles:")
                
                for i, article in enumerate(articles[:num_articles], 1):
                    print(f"\nArticle {i}:")
                    print(f"Title: {article['title']}")
                    print(f"Authors: <AUTHORS>
                    print(f"PMID: {article['pmid']}")
                    print(f"Publisher: {article['publisher']}")
                    print(f"Publication Date: {article['publication_date']}")
                    print(f"DOI: {article['doi']}")
                    print(f"Article Link: {article['article_link']}")
                    print("-" * 50)

        except Exception as e:
            self.fail(f"Test failed with error: {str(e)}")
