"use client";

import React, { useState } from "react";
import { Live<PERSON>rovider, LiveEditor, LiveError, LivePreview } from "react-live";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Alert<PERSON>riangle, <PERSON>, Sun, Code, Eye, ChevronDown, ChevronUp } from "lucide-react";
import { ThemeProvider, useTheme } from "@/components/theme/theme-provider";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { cn } from "@/lib/utils";

const sections = {
  title: {
    title: "Title Section",
    code: `
const TitleSection = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 bg-background rounded-lg shadow-md text-foreground">
      <h1 className="text-3xl font-bold text-red-700 dark:text-red-500 mb-6 border-b pb-2">Myocardial Infarction (MI): Clinical Notes</h1>
      
      <div className="bg-red-100 dark:bg-red-900/30 p-4 border-l-4 border-red-700 dark:border-red-500 mb-6 flex items-start">
        <AlertTriangle className="text-red-700 dark:text-red-500 mr-2 mt-1 flex-shrink-0" size={20} />
        <div>
          <h3 className="font-bold text-red-700 dark:text-red-500">EMERGENCY CONDITION</h3>
          <p className="text-foreground/90">Immediate assessment and treatment required. Time is myocardium.</p>
        </div>
      </div>
    </div>
  );
};

render(<TitleSection />);
`
  },
  definition: {
    title: "Definition Section",
    code: `
const DefinitionSection = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 bg-background rounded-lg shadow-md text-foreground">
      <section className="mb-6">
        <h2 className="text-xl font-bold text-foreground mb-2">Definition</h2>
        <p className="text-foreground/90 mb-2">
          Myocardial infarction (MI), commonly known as heart attack, occurs when blood flow to a part of the heart is blocked, causing damage to the heart muscle due to lack of oxygen supply.
        </p>
      </section>
    </div>
  );
};

render(<DefinitionSection />);
`
  },
  pathophysiology: {
    title: "Pathophysiology Section",
    code: `
const PathophysiologySection = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 bg-background rounded-lg shadow-md text-foreground">
      <section className="mb-6">
        <h2 className="text-xl font-bold text-foreground mb-2">Pathophysiology</h2>
        <ul className="list-disc pl-6 text-foreground/90 space-y-1">
          <li>Usually results from complete thrombotic occlusion of a coronary artery</li>
          <li>Most commonly caused by rupture of an atherosclerotic plaque</li>
          <li>Results in ischemia and eventual necrosis of myocardial tissue</li>
          <li>Infarct evolution occurs over several hours (6-12 hours for complete necrosis)</li>
          <li>Area at risk may be salvageable with prompt reperfusion therapy</li>
        </ul>
      </section>
    </div>
  );
};

render(<PathophysiologySection />);
`
  },
  clinicalPresentation: {
    title: "Clinical Presentation Section",
    code: `
const ClinicalPresentationSection = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 bg-background rounded-lg shadow-md text-foreground">
      <section>
        <h2 className="text-xl font-bold text-foreground mb-2">Clinical Presentation</h2>
        <ul className="list-disc pl-6 text-foreground/90 space-y-1">
          <li><span className="font-semibold">Chest pain:</span> Crushing, pressure-like, may radiate to jaw/arm</li>
          <li><span className="font-semibold">Associated symptoms:</span> Dyspnea, diaphoresis, nausea</li>
          <li><span className="font-semibold">Physical findings:</span> Anxiety, pallor, clammy skin</li>
          <li><span className="font-semibold">Atypical presentations:</span> More common in elderly, diabetics, and women</li>
          <li><span className="font-semibold">Silent MI:</span> Absence of chest pain (10-15% of cases)</li>
        </ul>
      </section>
    </div>
  );
};

render(<ClinicalPresentationSection />);
`
  }
};

interface SectionEditorProps {
  title: string;
  initialCode: string;
}

function SectionEditor({ title, initialCode }: SectionEditorProps) {
  const [code, setCode] = useState(initialCode);
  const [isExpanded, setIsExpanded] = useState(true);
  const { theme } = useTheme();

  return (
    <div className="mb-8 border rounded-lg overflow-hidden bg-card shadow-sm transition-all duration-200 hover:shadow-md">
      <div 
        className="flex items-center justify-between p-4 bg-muted/50 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold text-foreground">{title}</h2>
          <div className="px-2 py-1 text-xs font-medium rounded-full bg-primary/10 text-primary">
            {isExpanded ? "Expanded" : "Collapsed"}
          </div>
        </div>
        <button 
          className="p-1 rounded-full hover:bg-muted transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
        >
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </button>
      </div>

      <div className={cn(
        "transition-all duration-200 ease-in-out",
        isExpanded ? "max-h-[800px] opacity-100" : "max-h-0 opacity-0"
      )}>
        <LiveProvider 
          code={code} 
          scope={{ useState, AlertTriangle, Moon, Sun }} 
          noInline={true}
        >
          <div className="w-full h-full">
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="grid grid-cols-2 mb-4 p-1 bg-muted/50">
                <TabsTrigger value="editor" className="flex items-center gap-2">
                  <Code size={16} />
                  Editor
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye size={16} />
                  Preview
                </TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="w-full">
                <div className="border rounded-md overflow-hidden">
                  <LiveEditor
                    onChange={(newCode) => setCode(newCode)}
                    style={{
                      fontFamily: "monospace",
                      fontSize: "14px",
                      padding: "16px",
                      minHeight: "200px",
                      backgroundColor: theme === 'dark' ? "#0F172A" : "#F8FAFC",
                      color: theme === 'dark' ? "#E2E8F0" : "#0F172A",
                    }}
                  />
                </div>
              </TabsContent>
              <TabsContent value="preview" className="w-full">
                <div className="border rounded-md p-4 bg-background min-h-[200px] overflow-y-auto max-h-[400px]">
                  <LiveError className="text-red-500 mb-4" />
                  <LivePreview />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </LiveProvider>
      </div>
    </div>
  );
}

function NotesPageContent() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Interactive Medical Notes Editor</h1>
          <p className="text-muted-foreground">Edit and preview each section independently</p>
        </div>
        <ThemeToggle />
      </div>
      
      <div className="space-y-4">
        {Object.entries(sections).map(([key, section]) => (
          <SectionEditor
            key={key}
            title={section.title}
            initialCode={section.code}
          />
        ))}
      </div>

      <div className="mt-8 p-6 bg-muted/30 rounded-lg border">
        <h2 className="text-2xl font-bold mb-4">Instructions</h2>
        <ul className="list-disc ml-6 space-y-2 text-muted-foreground">
          <li>Each section can be expanded or collapsed using the chevron button</li>
          <li>Edit the React code in the editor tab for each section</li>
          <li>See the live preview in the preview tab</li>
          <li>Components like <code className="bg-muted px-1 py-0.5 rounded">useState</code> and <code className="bg-muted px-1 py-0.5 rounded">AlertTriangle</code> are available via scope</li>
          <li><strong>Important:</strong> Always keep the <code className="bg-muted px-1 py-0.5 rounded">render()</code> call at the end of your code</li>
          <li>Use the Sun/Moon icon in the top right to toggle the page theme</li>
        </ul>
      </div>
    </div>
  );
}

export default function NotesPage() {
  return (
    <ThemeProvider defaultTheme="dark">
      <NotesPageContent />
    </ThemeProvider>
  );
}
