package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"decodemed/storage"
)

// SignedURLRequest is the request body for getting a signed URL
type SignedURLRequest struct {
	ObjectName string `json:"object_name" binding:"required"`
	ExpiresIn  int    `json:"expires_in,omitempty"` // Optional: expiration time in seconds
}

// GetSignedURL handles requests for generating signed URLs for accessing storage objects
func GetSignedURL(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req SignedURLRequest
		if err := c.ShouldBind<PERSON>(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid request: " + err.Error(),
			})
			return
		}

		// Get the storage client
		storageClient, err := storage.GetStorage(context.Background())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to initialize storage client: " + err.<PERSON>rror(),
			})
			return
		}

		// Set default expiration time if not provided (1 hour)
		expiration := time.Duration(req.ExpiresIn) * time.Second
		if expiration == 0 {
			expiration = 1 * time.Hour
		}

		// Generate the signed URL
		signedURL, err := storageClient.GetSignedURL(context.Background(), req.ObjectName, expiration)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate signed URL: " + err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"signed_url": signedURL,
			"expires_in": int(expiration.Seconds()),
		})
	}
}
