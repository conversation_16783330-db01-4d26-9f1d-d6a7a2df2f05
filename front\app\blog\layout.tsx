import { Metadata } from 'next/dist/lib/metadata/types/metadata-interface';

export const metadata: Metadata = {
  title: 'DecodeMed Blog - Medical Education Resources & Updates',
  description:
    'Stay updated with the latest in medical education technology, study techniques, and DecodeMed platform updates.',
  // Adding a canonical URL here to consolidate link equity
  alternates: {
    canonical: 'https://decodemed.com/blog'
  },
  openGraph: {
    title: 'DecodeMed Blog - Medical Education Resources & Updates',
    description: 'Expert insights on medical education and study techniques.',
    url: 'https://decodemed.com/blog',
    images: [
      {
        url: 'https://decodemed.com/images/blog-og.jpg',
        width: 1200,
        height: 630,
      },
    ],
    siteName: 'DecodeMed',
    locale: 'en_US',
    type: 'website',
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative">
      {children}
    </div>
  );
} 