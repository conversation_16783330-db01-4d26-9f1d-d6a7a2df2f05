import React, { useState, useRef, useMemo, useCallback } from 'react';
import type { MDXRemoteSerializeResult } from 'next-mdx-remote';
import { LoadingSkeleton } from './LoadingSkeleton';
import { useTheme } from '@/components/theme/theme-provider';
import dynamic from 'next/dynamic';

// Dynamic import with proper ESM handling
const DynamicMDXRemote = dynamic(() => import('next-mdx-remote').then(mod => mod.MDXRemote), {
  ssr: false,
  loading: () => <div>Loading content...</div>
});

interface Article {
  title: string;
  authors: string[];
  publication_date: string;
  journal_title: string;
  citation_count: number;
  doi_link: string;
  pubmed_link: string;
  pdf_link?: string;
  is_open_access: boolean;
}

interface ArticlesSummaryProps {
  mdxSource: MDXRemoteSerializeResult | null;
  isLoading: boolean;
  rawText: string;
  articles: Article[];
}



// Simple tooltip component that doesn't use div elements
const SimpleTooltip: React.FC<{
  content: React.ReactNode;
  children: React.ReactNode;
}> = ({ content, children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const showTooltip = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    setIsVisible(true);
  };
  
  const hideTooltip = () => {
    timeoutRef.current = setTimeout(() => setIsVisible(false), 300);
  };
  
  return (
    <span 
      className="relative inline-block" 
      onMouseEnter={showTooltip} 
      onMouseLeave={hideTooltip}
      onClick={showTooltip}
    >
      {children}
      {isVisible && (
        <span 
          className="absolute z-50 left-0 mt-1 w-64 rounded-md shadow-lg p-3 text-sm"
          style={{
            top: '100%',
            backgroundColor: 'var(--tooltip-bg, #333)',
            color: 'var(--tooltip-text, white)',
            border: '1px solid var(--tooltip-border, #555)'
          }}
          onMouseEnter={showTooltip}
          onMouseLeave={hideTooltip}
        >
          {content}
        </span>
      )}
    </span>
  );
};

export const ArticlesSummary: React.FC<ArticlesSummaryProps> = ({ 
  mdxSource, 
  isLoading, 
  rawText,
  articles 
}) => {
  const { theme } = useTheme();
  
  // Use callback for reference click
  const scrollToArticle = useCallback((number: string) => {
    const element = document.getElementById(`article-${number}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Set tooltip colors based on theme
  React.useEffect(() => {
    document.documentElement.style.setProperty(
      '--tooltip-bg', 
      theme === 'dark' ? '#1b1b1b' : 'white'
    );
    document.documentElement.style.setProperty(
      '--tooltip-text', 
      theme === 'dark' ? '#e5e5e5' : '#333'
    );
    document.documentElement.style.setProperty(
      '--tooltip-border', 
      theme === 'dark' ? '#333' : '#eaeaea'
    );
  }, [theme]);

  // Create components once with useMemo to avoid recreating on every render
  const components = useMemo(() => {
    return {
      Reference: ({ number }: { number: string; children?: React.ReactNode }) => {
        const articleIndex = parseInt(number) - 1;
        const article = articles[articleIndex] || {};
        
        const tooltipContent = (
          <>
            <span className={`block font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              <a 
                href={article.doi_link || article.pubmed_link || '#'} 
                target="_blank" 
                rel="noopener noreferrer"
                className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} text-xs hover:underline`}
              >
                {article.title || 'No title available'}
              </a>
            </span>
            <span className={`block text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
              {article.authors ? (article.authors.slice(0, 1).join(', ') + (article.authors.length > 1 ? ' et al.' : '')) : 'No authors available'}
              {article.publication_date ? ` • ${article.publication_date}` : ''}
              {article.journal_title ? ` • ${article.journal_title}` : ''}
            </span>
          </>
        );
        
        return (
          <SimpleTooltip content={tooltipContent}>
            <span 
              onClick={() => scrollToArticle(number)}
              className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-800 text-white text-xs cursor-pointer hover:bg-gray-700 mx-0.5"
            >
              {number}
            </span>
          </SimpleTooltip>
        );
      },
      p: ({ children, ...props }: React.HTMLAttributes<HTMLParagraphElement>) => {
        // We need to process children to ensure no invalid HTML nesting
        const renderedChildren = React.Children.map(children, (child) => {
          // If it's a Reference component or any component that might render invalid HTML in a <p>,
          // we need to handle it specially
          if (React.isValidElement(child) && 
             (typeof child.type === 'function' || child.type === 'div')) {
            // Convert it to a span
            return <span key={child.key}>{child}</span>;
          }
          return child;
        });
        
        return <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'} my-2`} {...props}>{renderedChildren}</p>;
      },
      ul: ({ children, ...props }: React.HTMLAttributes<HTMLUListElement>) => (
        <ul className={`list-disc pl-6 my-4 space-y-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`} {...props}>
          {children}
        </ul>
      ),
      li: ({ children, ...props }: React.HTMLAttributes<HTMLLIElement>) => (
        <li className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`} {...props}>
          {children}
        </li>
      ),
      h1: ({ children, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h1 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-6 pb-2 border-b-2 inline-block`} {...props}>
          {children}
        </h1>
      ),
      h2: (props: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h2 className={`text-xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-black'} mb-2 mt-2 relative`} {...props} />
      ),
      h3: (props: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-black'} mb-2 mt-2 relative`} {...props} />
      ),
      h4: (props: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h4 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'} mb-2 mt-2 relative`} {...props} />
      ),
      strong: ({ children, ...props }: React.HTMLAttributes<HTMLElement>) => (
        <strong className={`font-bold ${theme === 'dark' ? 'text-white' : 'text-black'}`} {...props}>{children}</strong>
      ),
      a: ({ href, children, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => (
        <a 
          href={href} 
          className={`${theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-black'} underline`} 
          {...props}
        >
          {children}
        </a>
      ),
      // For safety, override everything that might create block elements
      div: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
        <div {...props}>{children}</div>
      ),
      span: ({ children, ...props }: React.HTMLAttributes<HTMLSpanElement>) => (
        <span {...props}>{children}</span>
      )
    };
  }, [theme, articles, scrollToArticle]);

  // Process the rawText to replace references with our custom format
  const processedRawText = useMemo(() => {
    if (!rawText) return "";
    // Replace markdown reference format with our custom reference component syntax
    return rawText.replace(/\[(\d+)\]/g, '<Reference number="$1" />');
  }, [rawText]);

  return (
    <>
      {mdxSource ? (
        <div className={`mt-4 ${theme === 'dark' ? 'bg-[#1b1b1b]' : 'bg-gradient-to-b from-gray-50 to-gray-200'} p-3 sm:p-4 md:p-6 rounded-lg shadow-md`}>
          <div className="prose prose-xs sm:prose-sm md:prose-base lg:prose-lg max-w-none dark:prose-invert">
            <DynamicMDXRemote {...mdxSource} components={components} />
          </div>
        </div>
      ) : isLoading ? (
        <LoadingSkeleton />
      ) : null}

      {rawText && !mdxSource && (
        <div className={`mt-4 ${theme === 'dark' ? 'bg-[#1b1b1b]' : 'bg-gradient-to-b from-white to-gray-100'} p-3 sm:p-4 md:p-6 rounded-lg shadow-md`}>
          <div className="prose prose-xs sm:prose-sm md:prose-base lg:prose-lg max-w-none dark:prose-invert">
            <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}>{processedRawText}</p>
          </div>
        </div>
      )}
    </>
  );
};

export default ArticlesSummary;
