import { SubscriptionData, SubscriptionDetails, SubscriptionType, UsageStats } from './stripe';
import { API_URL } from './decode_api';

/**
 * Makes an authenticated API request
 */
export const authenticatedFetch = async (
  url: string, 
  getToken: () => Promise<string | null>,
  options: RequestInit = {}
) => {
  // Get a fresh token for each request
  const token = await getToken();
  
  if (!token) {
    throw new Error('No authentication token available');
  }
  
  // Create headers with authentication
  const headers = {
    ...options.headers,
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };
  
  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include'
  });
  
  // Handle common error responses
  if (!response.ok) {
    if (response.status === 401) {
      throw new Error('Your session has expired. Please sign in again.');
    }
    
    try {
      const errorData = await response.json();
      throw new Error(errorData.error || `Request failed with status ${response.status}`);
    } catch (e) {
      if (e instanceof Error && e.message.includes('JSON')) {
        throw new Error(`Request failed with status ${response.status}`);
      }
      throw e;
    }
  }
  
  return response;
};

/**
 * Creates a checkout session for subscription purchase
 */
export const createCheckoutSession = async (
  getToken: () => Promise<string | null>,
  subscriptionType: SubscriptionType,
): Promise<{ sessionId: string }> => {
  const response = await authenticatedFetch(
    `${API_URL}/api/subscription-api/create-checkout-session`,
    getToken,
    {
      method: 'POST',
      body: JSON.stringify({
        subscriptionType,
        successUrl: `${window.location.origin}/subscriptions/success`,
        cancelUrl: `${window.location.origin}/subscriptions/pricing`,
      }),
    }
  );
  
  return response.json();
};

/**
 * Fetches the current subscription status
 */
export const getSubscriptionStatus = async (
  getToken: () => Promise<string | null>
): Promise<SubscriptionData> => {
  const response = await authenticatedFetch(
    `${API_URL}/api/subscription-api/status`,
    getToken,
    { method: 'GET' }
  );
  
  return response.json();
};

/**
 * Maps subscription data from API to frontend usage format
 */
export const mapSubscriptionToUsage = (
  data: SubscriptionData
): UsageStats => {
  const freeProjectsCount = data.free_projects_count || 0;
  const freeProjectsLimit = data.free_projects_limit || 10;
  const isPremium = data.status === 'active' || data.status === 'trialing';

  const usageStats: UsageStats = {
    subscription_type: data.details?.plan_type === 'unknown' ? 'none' : (data.details?.plan_type || 'none'),
    status: data.status,
    current_period_end: data.details?.current_period_end || null,
    will_cancel: data.details?.cancel_at_period_end || false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    search: { 
      usage: isPremium ? 0 : freeProjectsCount,
      limit: isPremium ? -1 : freeProjectsLimit
    },
    chat: { 
      usage: freeProjectsCount,
      limit: freeProjectsLimit
    }
  };
  return usageStats;
};

/**
 * Cancels a subscription
 */
export const cancelSubscription = async (
  getToken: () => Promise<string | null>
): Promise<{ success: boolean; message: string }> => {
  const response = await authenticatedFetch(
    `${API_URL}/api/subscription-api/cancel`,
    getToken,
    { method: 'POST' }
  );
  
  return response.json();
};

/**
 * Resumes a canceled subscription
 */
export const resumeSubscription = async (
  getToken: () => Promise<string | null>
): Promise<{ success: boolean; message: string }> => {
  const response = await authenticatedFetch(
    `${API_URL}/api/subscription-api/resume`,
    getToken,
    { method: 'POST' }
  );
  
  return response.json();
};

/**
 * Updates a subscription to a different plan
 */
export const updateSubscription = async (
  getToken: () => Promise<string | null>,
  subscriptionType: SubscriptionType
): Promise<{ success: boolean; message: string; subscription: SubscriptionDetails }> => {
  const response = await authenticatedFetch(
    `${API_URL}/api/subscription-api/update`,
    getToken,
    {
      method: 'POST',
      body: JSON.stringify({ subscriptionType }),
    }
  );
  
  return response.json();
};

/**
 * Creates a Stripe Customer Portal session
 */
export const createCustomerPortalSession = async (
  getToken: () => Promise<string | null>,
  returnUrl?: string
): Promise<{ url: string }> => {
  const response = await authenticatedFetch(
    `${API_URL}/api/subscription-api/create-portal-session`,
    getToken,
    {
      method: 'POST',
      body: JSON.stringify({
        returnUrl: returnUrl || `${window.location.origin}/subscriptions/subscription-management`,
      }),
    }
  );
  
  return response.json();
}; 