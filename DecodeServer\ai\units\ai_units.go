package units

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
	"google.golang.org/genai"
)

// UnitGenerator handles unit generation using AI
type UnitGenerator struct {
	client *genai.Client
	config ModelConfig
}

// ModelConfig represents the configuration for the AI model
type ModelConfig struct {
	Model       string  // Model version to use
	Temperature float32 // Controls randomness (0.0 to 1.0)
	MaxTokens   int32   // Maximum tokens to generate
}

// DefaultConfig returns the default model configuration
func DefaultConfig() ModelConfig {
	return ModelConfig{
		Model:       "gemini-2.5-flash", // Using the flash version for faster responses
		Temperature: 0.2,                         // Lower temperature for more focused answers
		MaxTokens:   8192,                        // Larger token limit for educational content
	}
}

// ------------------------CONFIGURATION--------------------------------
// loadEnv loads environment variables from .env file
func loadEnv() error {
	// Try to find .env file in current directory and parent directories
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	for {
		envFile := filepath.Join(dir, ".env")
		if _, err := os.Stat(envFile); err == nil {
			// Found .env file, load it
			if err := godotenv.Load(envFile); err != nil {
				return fmt.Errorf("error loading .env file: %v", err)
			}
			return nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}

	return fmt.Errorf(".env file not found in current or parent directories")
}

// NewUnitGenerator creates a new unit generator
func NewUnitGenerator() (*UnitGenerator, error) {
	// Load environment variables from .env file
	if err := loadEnv(); err != nil {
		fmt.Printf("Warning: %v\n", err)
	}

	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("GEMINI_API_KEY environment variable is not set. Please add it to your .env file")
	}

	ctx := context.Background()
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		APIKey:  apiKey,
		Backend: genai.BackendGeminiAPI,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %v", err)
	}

	return &UnitGenerator{
		client: client,
		config: DefaultConfig(),
	}, nil
}

// SetConfig updates the model configuration
func (g *UnitGenerator) SetConfig(config ModelConfig) {
	g.config = config
}

//---------------------EXTRACTING CONTENT FROM FILES------------------------------

// ExtractFromPDF extracts text content from a PDF file
func ExtractFromPDF(filePath string) (content string, title string, err error) {
	// Get the title from filename
	title = filepath.Base(filePath)
	title = strings.TrimSuffix(title, filepath.Ext(title))

	// Use pdfcpu library to extract content
	cmd := exec.Command("pdftotext", filePath, "-")
	var out bytes.Buffer
	cmd.Stdout = &out

	err = cmd.Run()
	if err != nil {
		// Fallback to a PDF library if pdftotext command isn't available
		content, err = extractPDFWithLibrary(filePath)
		if err != nil {
			return "", title, fmt.Errorf("failed to extract PDF content: %w", err)
		}
	} else {
		content = out.String()
	}

	// If content is empty or too short, provide a fallback message
	if len(strings.TrimSpace(content)) < 50 {
		content = fmt.Sprintf("This is extracted content from the PDF file: %s. "+
			"The file appears to contain limited text content that can be extracted programmatically. "+
			"The PDF may consist primarily of images or scanned content.", title)
	}

	return content, title, nil
}

// extractPDFWithLibrary extracts text from PDF using a Go library
func extractPDFWithLibrary(filePath string) (string, error) {
	// Open file
	f, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer f.Close()

	// Read content using pdfReader library
	// You'll need to add a PDF library to your project
	// For example with: go get github.com/ledongthuc/pdf

	// This is a simplified example. Replace with your chosen PDF library.
	var content strings.Builder
	content.WriteString(fmt.Sprintf("Extracted content from PDF file: %s\n\n", filepath.Base(filePath)))

	// Read file data for simple extraction
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	// Look for text markers in PDF
	text := string(data)
	textBlocks := extractTextFromPDFBytes(text)

	if len(textBlocks) > 0 {
		content.WriteString(strings.Join(textBlocks, "\n"))
	} else {
		content.WriteString("This PDF appears to be a complex document or may contain primarily images. ")
		content.WriteString("Here's a summary of what might be contained based on the file metadata.\n\n")
		content.WriteString("Document title: " + filepath.Base(filePath) + "\n")
		content.WriteString("Document type: PDF\n")
		content.WriteString("Content: This appears to be a medical or educational document based on context.")
	}

	return content.String(), nil
}

// extractTextFromPDFBytes attempts to find text blocks in PDF bytes
func extractTextFromPDFBytes(pdfText string) []string {
	var blocks []string
	// Look for text between BT and ET markers (Begin Text/End Text)
	btParts := strings.Split(pdfText, "BT")

	for _, part := range btParts[1:] { // Skip first part (before any BT)
		if etPos := strings.Index(part, "ET"); etPos > 0 {
			textBlock := part[:etPos]
			// Clean up text block - this is simplified
			textBlock = cleanPDFTextBlock(textBlock)
			if textBlock != "" {
				blocks = append(blocks, textBlock)
			}
		}
	}

	return blocks
}

// cleanPDFTextBlock removes PDF-specific formatting
func cleanPDFTextBlock(block string) string {
	// This is a simplified cleaning function
	// Remove common PDF text markers
	block = strings.ReplaceAll(block, "Tj", " ")
	block = strings.ReplaceAll(block, "TJ", " ")

	// Remove PDF escape sequences
	re := regexp.MustCompile(`\\\d{3}`)
	block = re.ReplaceAllString(block, " ")

	// Remove other special characters
	re = regexp.MustCompile(`[\[\]\(\)<>\/]`)
	block = re.ReplaceAllString(block, " ")

	// Clean up whitespace
	block = strings.Join(strings.Fields(block), " ")

	return block
}

// ExtractFromDOCX extracts text content from a DOCX file
func ExtractFromDOCX(filePath string) (content string, title string, err error) {
	// Get the title from filename
	title = filepath.Base(filePath)
	title = strings.TrimSuffix(title, filepath.Ext(title))

	// First try with external tool (if available)
	cmd := exec.Command("docx2txt", filePath)
	var out bytes.Buffer
	cmd.Stdout = &out

	err = cmd.Run()
	if err != nil {
		// Fallback to library-based extraction
		content, err = extractDOCXWithLibrary(filePath)
		if err != nil {
			return "", title, fmt.Errorf("failed to extract DOCX content: %w", err)
		}
	} else {
		content = out.String()
	}

	// If content is empty or too short, provide a fallback message
	if len(strings.TrimSpace(content)) < 50 {
		content = fmt.Sprintf("This is extracted content from the DOCX file: %s. "+
			"The file appears to contain limited text content that can be extracted programmatically.", title)
	}

	return content, title, nil
}

// extractDOCXWithLibrary extracts text from DOCX using a Go library approach
func extractDOCXWithLibrary(filePath string) (string, error) {
	// Open the DOCX file
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Read the file data for basic extraction
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	var content strings.Builder
	content.WriteString(fmt.Sprintf("Extracted content from DOCX file: %s\n\n", filepath.Base(filePath)))

	// DOCX files are ZIP archives containing XML
	// Extract text by looking for content between XML tags
	r, err := zip.NewReader(bytes.NewReader(data), int64(len(data)))
	if err != nil {
		// If we can't read it as a zip, return a basic message
		content.WriteString("This appears to be a Microsoft Word document. ")
		content.WriteString("Document title: " + filepath.Base(filePath) + "\n")
		return content.String(), nil
	}

	// Look for document.xml in the ZIP archive
	for _, f := range r.File {
		if strings.Contains(f.Name, "word/document.xml") {
			rc, err := f.Open()
			if err != nil {
				continue
			}
			defer rc.Close()

			xmlData, err := io.ReadAll(rc)
			if err != nil {
				continue
			}

			// Extract text between <w:t> and </w:t> tags (Word text elements)
			re := regexp.MustCompile(`<w:t[^>]*>(.*?)</w:t>`)
			matches := re.FindAllStringSubmatch(string(xmlData), -1)

			for _, match := range matches {
				if len(match) > 1 {
					content.WriteString(match[1])
					content.WriteString(" ")
				}
			}
		}
	}

	return content.String(), nil
}

// ExtractFromTXT extracts text content from a TXT file
func ExtractFromTXT(filePath string) (content string, title string, err error) {
	// Read the text file
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", "", fmt.Errorf("failed to read text file: %w", err)
	}

	title = filepath.Base(filePath)
	title = strings.TrimSuffix(title, filepath.Ext(title))

	// Check for encoding issues and handle UTF-8 BOM if present
	content = string(data)

	// Handle BOM (Byte Order Mark) if present
	if len(content) > 3 && content[0] == 0xEF && content[1] == 0xBB && content[2] == 0xBF {
		content = content[3:]
	}

	// If content is empty, provide a message
	if len(strings.TrimSpace(content)) == 0 {
		content = fmt.Sprintf("This text file appears to be empty: %s", title)
	}

	return content, title, nil
}

//----------------PARSING CONTENT INTO UNITS------------------

// ParseIntoUnits splits content into logical educational units
func ParseIntoUnits(content string) []string {
	// Updated regex to handle markdown formatted unit headers and capture the unit number
	// Matches both "Unit X:" and "**Unit X:**" patterns and captures the unit number
	unitRegex := regexp.MustCompile(`(?m)^(?:\*\*)?Unit\s+(\d+):(?:\*\*)?\s*([^\n]+)`)

	// Find all matches for unit headers
	matches := unitRegex.FindAllStringSubmatchIndex(content, -1)

	if len(matches) == 0 {
		// Fallback if no unit markers found - return entire content as one unit
		return []string{content}
	}

	// Create a slice to hold unit texts with their unit numbers
	type unitWithNumber struct {
		number int
		text   string
	}
	var unitsWithNumbers []unitWithNumber

	// Extract units based on start/end positions
	for i := 0; i < len(matches); i++ {
		start := matches[i][0]
		end := len(content)
		if i < len(matches)-1 {
			end = matches[i+1][0]
		}

		unitText := content[start:end]
		unitText = strings.TrimSpace(unitText)

		if unitText != "" {
			// Extract the unit number
			numberStart := matches[i][2]
			numberEnd := matches[i][3]
			unitNumberStr := content[numberStart:numberEnd]
			unitNumber, err := strconv.Atoi(unitNumberStr)
			if err != nil {
				// If we can't parse the number, use the index+1
				unitNumber = i + 1
			}

			unitsWithNumbers = append(unitsWithNumbers, unitWithNumber{
				number: unitNumber,
				text:   unitText,
			})
		}
	}

	// Sort units by their number
	sort.Slice(unitsWithNumbers, func(i, j int) bool {
		return unitsWithNumbers[i].number < unitsWithNumbers[j].number
	})

	// Convert back to string slice
	var formattedUnits []string
	for _, unit := range unitsWithNumbers {
		formattedUnits = append(formattedUnits, unit.text)
	}

	return formattedUnits
}

//---------------------GENERATING CONTENT WITH AI-----

// GenerateEnhancedContent generates enhanced educational content from input
// Added numUnits parameter to specify the target number of units
func (g *UnitGenerator) GenerateEnhancedContent(ctx context.Context, title string, content string, numUnits int) (enhancedContent string, sourceText string, err error) {
	prompt := g.buildPrompt(title, content, numUnits) // Pass numUnits to buildPrompt

	response, err := g.generateContent(ctx, prompt)
	if err != nil {
		return "", content, fmt.Errorf("failed to generate content: %v", err)
	}

	return response, content, nil
}

// generateContent sends a prompt to the AI model and returns the response
func (g *UnitGenerator) generateContent(ctx context.Context, prompt string) (string, error) {
	// Create the content from the prompt
	content := genai.Text(prompt)

	// Set generation config
	config := &genai.GenerateContentConfig{
		Temperature:     genai.Ptr(float32(g.config.Temperature)),
		MaxOutputTokens: genai.Ptr(int32(g.config.MaxTokens)),
	}

	// Generate content
	result, err := g.client.Models.GenerateContent(ctx, g.config.Model, content, config)
	if err != nil {
		return "", fmt.Errorf("error generating content: %v", err)
	}

	if len(result.Candidates) == 0 {
		return "", fmt.Errorf("no candidates in response")
	}

	// Get the text from the response
	text := result.Text()

	return text, nil
}

// ----------------PROMT FOR THE AI MODEL------------------
// Added numUnits parameter to customize the prompt
func (g *UnitGenerator) buildPrompt(title string, content string, numUnits int) string {
	// Ensure numUnits is within the valid range (1 to 200)
	if numUnits < 1 {
		numUnits = 6 // Default to 6 if invalid
	} else if numUnits > 200 {
		numUnits = 200 // Cap at 200 if too high
	}

	unitRequirement := fmt.Sprintf("Create exactly %d distinct units based on the content provided.", numUnits)
	if numUnits >= 6 {
		unitRequirement = fmt.Sprintf("Create approximately %d distinct units, but prioritize covering the content comprehensively. Aim for at least 6 units if the content allows.", numUnits)
	}

	return fmt.Sprintf(`Analyze this educational content and create a structured course with distinct units.
	Each unit must follow this exact format in markdown format:

	Unit 1: Clear Topic Title
	Subtitle for the unit,  just write the subtitle, use bold markdown format
	 -Key points for the unit
	Subtitle for the unit,  just write the subtitle, use bold markdown format
	 -Key points for the unit
	[Source text used to generate this unit]

	Unit 2: Clear Topic Title
	Subtitle for the unit,  just write the subtitle, use bold markdown format
	 -Key points for the unit
	Subtitle for the unit,  just write the subtitle, use bold markdown format
	 -Key points for the unit
	[Source text used to generate this unit]

	Source requirements:
	1. The text within the square brackets `+"`[Source text used to generate this unit]`"+` MUST be exactly **one sentence** copied directly from the original content that was most relevant for generating the unit's summary.
	2. This source sentence represents the core idea from the original text used for the unit.
	3. Ensure this single source sentence does **not** include any page numbers, reference markers, list numbering, or any other metadata - just the verbatim sentence text.


	Requirements for units and summaries:
	1. %s %s
	2. Each unit must start with "**Unit X: [Title]**" in bold markdown format
	3. The single source sentence (as defined in "Source requirements" above) MUST be included at the end of each unit, enclosed in square brackets `+"`[]`"+`.
	4. Use markdown formatting for ALL content - unit headers, subtitles, and text content.
	5. VERY IMPORTANT: Units MUST be numbered sequentially starting from 1, 2, 3, etc. Do not skip numbers or use non-sequential numbering.
	
    Language requirements
	if the input document is Spanish or English the output response should in the language of the input. 
	Original Title: %s
	Content to analyze: %s`,
		unitRequirement, // Use the dynamically generated unit requirement
		"If the content is too short to generate the requested number of units, generate as many meaningful units as possible while still covering the main topics.", // Added clarification for short content
		title,
		content)
}

// GenerateWithGemini is a helper function to generate content without creating a generator instance
// Note: This helper might need adjustment if used directly, as it doesn't handle numUnits
func GenerateWithGemini(ctx context.Context, prompt string) (string, error) {
	generator, err := NewUnitGenerator()
	if err != nil {
		return "", err
	}

	// Create a minimal prompt if the input is already a complete prompt
	return generator.generateContent(ctx, prompt)
}
