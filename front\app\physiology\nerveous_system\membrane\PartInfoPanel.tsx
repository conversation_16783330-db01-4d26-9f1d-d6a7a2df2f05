import React from 'react'

interface PartInfoPanelProps {
  progress: number
}

// Define the phases of the action potential
const phases = [
  {
    name: "Resting Potential",
    threshold: 0,
    description:
      "Membrane is polarized with more Na+ outside and more K+ inside. Membrane potential is around -70mV.",
  },
  {
    name: "Depolarization",
    threshold: 20,
    description: "Na+ channels open, Na+ rushes in, membrane potential rises rapidly toward +30mV.",
  },
  {
    name: "Repolarization",
    threshold: 60,
    description:
      "Na+ channels close, K+ channels open, K+ flows out, membrane potential returns toward resting level.",
  },
  {
    name: "Hyperpolarization",
    threshold: 80,
    description: "K+ channels remain open briefly, causing membrane potential to drop below resting level.",
  },
  {
    name: "Return to Resting",
    threshold: 100,
    description: "Na+/K+ pump restores ion concentrations, membrane returns to resting potential.",
  },
]

export default function PartInfoPanel({ progress }: PartInfoPanelProps) {
  // Get current phase based on progress
  const getCurrentPhase = () => {
    for (let i = phases.length - 1; i >= 0; i--) {
      if (progress >= phases[i].threshold) {
        return phases[i]
      }
    }
    return phases[0]
  }

  const currentPhase = getCurrentPhase()

  return (
    <aside className="h-full bg-[hsl(240_10%_3.9%)] rounded-lg p-4 overflow-y-auto">
      <h3 className="text-lg font-bold mb-2 text-white">{currentPhase.name}</h3>
      <p className="text-sm text-gray-300">{currentPhase.description}</p>
    </aside>
  )
}
