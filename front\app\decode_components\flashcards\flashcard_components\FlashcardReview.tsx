"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { FlashcardModel, fetchWithTokenRetry } from '../flashcards';
import { API_URL } from '@/app/utils/decode_api';
// Add CSS for card flip animation
const styles = `
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }
  
  .backface-hidden {
    backface-visibility: hidden;
  }
  
  .rotate-y-180 {
    transform: rotateY(180deg);
  }
  
  .flip-card.flipped .flip-card-inner {
    transform: rotateY(180deg);
  }
  
  .flip-card-front,
  .flip-card-back {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
    border-radius: 1.5rem; /* Match rounded-3xl */
    overflow: hidden;
  }
  
  .flip-card:hover .flip-card-front,
  .flip-card:hover .flip-card-back {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
  
  /* Ensure card content respects rounded corners */
  .flip-card-front > div,
  .flip-card-back > div {
    border-radius: 1.5rem;
    overflow: hidden;
  }
`;

interface FlashcardReviewProps {
  projectId: string;
}

// Progress stats interface
interface FlashcardStats {
  total: number;
  reviewed: number;
  due: number;
}

export default function FlashcardReview({ projectId }: FlashcardReviewProps) {
  const { getToken } = useAuth();
  const [flashcard, setFlashcard] = useState<FlashcardModel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [stats, setStats] = useState<FlashcardStats>({ total: 0, reviewed: 0, due: 0 });
  const [allFlashcards, setAllFlashcards] = useState<FlashcardModel[]>([]);
  const [dueFlashcards, setDueFlashcards] = useState<FlashcardModel[]>([]);
  const [reviewedCardIds, setReviewedCardIds] = useState<Set<number>>(new Set());
  const [responseStartTime, setResponseStartTime] = useState<Date | null>(null);

  // API methods specific to FlashcardReview component
  const reviewFlashcard = async (reviewInput: { flashcard_id: number, difficulty_rating: number, project_id: string, response_time?: number }): Promise<void> => {
    try {
      const flashcardId = reviewInput.flashcard_id;
      if (!flashcardId || flashcardId <= 0) {
        throw new Error(`Invalid flashcard ID: ${flashcardId}`);
      }

      console.log('Reviewing flashcard with input:', reviewInput);
      
      // Map difficulty_rating (1-3) to grade (0-5) for FSRS algorithm
      // 1 (Hard) -> 1, 2 (Medium) -> 3, 3 (Easy) -> 5
      const grade = reviewInput.difficulty_rating === 1 ? 1 : 
                    reviewInput.difficulty_rating === 2 ? 3 : 5;
      
      // Get the current flashcard so we can preserve the tags field
      const currentFlashcard = allFlashcards.find(fc => 
        (fc.id === flashcardId || fc.ID === flashcardId)
      );
      
      if (!currentFlashcard) {
        throw new Error(`Could not find flashcard with ID ${flashcardId} in loaded flashcards`);
      }
      
      // Create the minimal payload for the review
      // We need to keep the complete review input structure as simple as possible
      const backendInput = {
        grade: grade,
        time: new Date().toISOString(),
        response_time: Math.round((reviewInput.response_time || 5.0) * 100) / 100
      };
      
      // Convert to JSON manually to ensure exact format
      const jsonPayload = JSON.stringify(backendInput);
      
      console.log(`Sending review to backend for flashcard ID ${flashcardId} with payload:`, jsonPayload);
      
      // Use fetch directly instead of the wrapper to ensure complete control over the request
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }
      
      const response = await fetch(`${API_URL}/api/decode/flashcards/${flashcardId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: jsonPayload
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error reviewing flashcard: ${response.status} ${response.statusText}`, errorText);
        
        // If we get a 500 error, it's likely due to the tags field issue
        if (response.status === 500) {
          console.warn("Received 500 error - this could be due to the tags field formatting issue");
          console.warn("The backend needs to be fixed to handle empty tags properly");
          // We throw the error anyway to be handled by the caller
        }
        
        throw new Error(`Failed to review flashcard: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Flashcard review response:', data);
    } catch (error) {
      console.error('Error in reviewFlashcard:', error);
      throw error;
    }
  };

  const getAllFlashcards = async (): Promise<FlashcardModel[]> => {
    try {
      console.log('Fetching all flashcards for project:', projectId);
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/flashcards/by-project?project_id=${projectId}`,
        { method: 'GET' },
        getToken
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error fetching all flashcards: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Failed to fetch all flashcards: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('All flashcards response:', data);
      
      // Return empty array if null or undefined
      if (!data) return [];
      
      // Process the response to normalize ID fields
      let flashcards: FlashcardModel[] = [];
      
      if (Array.isArray(data)) {
        flashcards = data;
      } else {
        // Handle nested response formats
        flashcards = data.flashcards || data.data || data.cards || data.items || data.results || [];
      }
      
      // Ensure each flashcard has a valid id property
      return flashcards.map(fc => ({
        ...fc,
        id: fc.id || fc.ID || 0  // Set id to fc.id, fc.ID, or 0 if both are undefined
      }));
    } catch (error) {
      console.error('Error in getAllFlashcards:', error);
      throw error;
    }
  };

  const getDueFlashcards = async (): Promise<FlashcardModel[]> => {
    try {
      console.log('Fetching due flashcards for project:', projectId);
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/flashcards/due?project_id=${projectId}`,
        { method: 'GET' },
        getToken
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error fetching due flashcards: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Failed to fetch due flashcards: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Due flashcards response:', data);
      
      // Return empty array if null or undefined
      if (!data) return [];
      
      // Process the response to normalize ID fields
      let flashcards: FlashcardModel[] = [];
      
      if (Array.isArray(data)) {
        flashcards = data;
      } else {
        // Handle nested response formats
        flashcards = data.flashcards || data.data || data.cards || data.items || data.results || [];
      }
      
      // Ensure each flashcard has a valid id property
      return flashcards.map(fc => ({
        ...fc,
        id: fc.id || fc.ID || 0  // Set id to fc.id, fc.ID, or 0 if both are undefined
      }));
    } catch (error) {
      console.error('Error in getDueFlashcards:', error);
      throw error;
    }
  };

  // Load all flashcards and stats
  const loadFlashcards = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Get all flashcards first for stats
      const allCards = await getAllFlashcards();
      setAllFlashcards(allCards);
      console.log(`Loaded ${allCards.length} total flashcards`);
      
      // Then get due flashcards for review
      const dueCards = await getDueFlashcards();
      setDueFlashcards(dueCards);
      console.log(`Loaded ${dueCards.length} due flashcards`);
      
      // Filter out any cards we've already reviewed in this session
      const filteredDueCards = dueCards.filter(card => {
        const cardId = card.id || card.ID || 0;
        return !reviewedCardIds.has(cardId);
      });
      
      // Calculate stats
      // Total is all flashcards
      const total = allCards.length;
      
      // Due is the cards that are due for review and haven't been reviewed in this session
      const due = filteredDueCards.length;
      
      // Reviewed includes:
      // 1. Cards reviewed in this session (reviewedCardIds)
      // 2. Cards that have been previously reviewed (total - originalDueCards)
      const reviewedInSession = reviewedCardIds.size;
      const reviewedPreviously = total - dueCards.length;
      const reviewed = reviewedInSession + reviewedPreviously;
      
      setStats({
        total,
        reviewed,
        due
      });
      
      console.log(`Stats: Total=${total}, Due=${due}, Reviewed=${reviewed} (${reviewedInSession} in session + ${reviewedPreviously} previously)`);
      
      // Set the first due card for review if available
      if (filteredDueCards.length > 0) {
        const cardToReview = filteredDueCards[0];
        console.log('Setting active flashcard for review:', {
          id: cardToReview.id, 
          ID: cardToReview.ID,
          questionPreview: cardToReview.question?.substring(0, 30)
        });
        setFlashcard(cardToReview);
      } else {
        setFlashcard(null);
      }
    } catch (error) {
      console.error('Error loading flashcards:', error);
      setError('Failed to load flashcards. Please try again.');
      setFlashcard(null);
    } finally {
      setLoading(false);
    }
  };

  // Get the next flashcard without reloading from API
  const moveToNextCard = () => {
    // Check if we have any due cards after filtering out already reviewed ones
    const remainingDueCards = dueFlashcards.filter(card => {
      const cardId = card.id || card.ID || 0;
      return !reviewedCardIds.has(cardId);
    });
    
    // If we have any remaining cards, show the first one
    if (remainingDueCards.length > 0) {
      const nextCard = remainingDueCards[0];
      setFlashcard(nextCard);
    } else {
      // No more cards to review
      setFlashcard(null);
    }
    
    // Update stats to reflect the new state
    // total stays the same
    const totalCount = allFlashcards.length;
    
    // due is the count of remaining unreviewed cards
    const dueCount = remainingDueCards.length;
    
    // reviewed includes cards already reviewed in database + cards reviewed in this session
    const reviewedInSession = reviewedCardIds.size;
    const reviewedPreviously = totalCount - dueFlashcards.length;
    const reviewedCount = reviewedInSession + reviewedPreviously;
    
    console.log(`Updating stats: Total=${totalCount}, Due=${dueCount}, Reviewed=${reviewedCount} (${reviewedInSession} in session + ${reviewedPreviously} previously)`);
    
    setStats({
      total: totalCount,
      reviewed: reviewedCount,
      due: dueCount
    });
  };

  // Submit a review for the current flashcard
  const submitReview = async (difficulty: number) => {
    if (!flashcard) {
      console.error('Cannot submit review: Flashcard is null');
      setError('Cannot submit review: No flashcard loaded');
      return;
    }
    
    // Check if we have a valid ID (either camelCase 'id' or PascalCase 'ID')
    const flashcardId = flashcard.id || flashcard.ID || 0; // Default to 0 if both are undefined
    const hasValidId = flashcardId > 0; // Only consider positive numbers as valid IDs
    
    if (!hasValidId) {
      console.error('Cannot submit review: Invalid flashcard ID', flashcard);
      setError('Cannot submit review: Invalid flashcard ID');
      return;
    }
    
    setLoading(true);
    
    try {
      // Calculate response time in seconds (time between showing answer and rating)
      const responseTime = responseStartTime 
        ? (new Date().getTime() - responseStartTime.getTime()) / 1000 
        : 5.0; // Default if timing isn't available
      
      console.log(`Submitting review for flashcard ${flashcardId} with difficulty ${difficulty} and response time ${responseTime}s`);
      
      // Add this card to our locally tracked reviewed cards before API call
      // so we don't show it again even if the API fails
      setReviewedCardIds(prevIds => {
        const newIds = new Set(prevIds);
        newIds.add(flashcardId);
        return newIds;
      });
      
      // Now that the backend is fixed to handle empty tags correctly, we can safely call the API
      try {
        await reviewFlashcard({
          flashcard_id: Number(flashcardId), // Ensure flashcardId is a number
          difficulty_rating: difficulty,
          project_id: projectId,
          response_time: responseTime
        });
        console.log('Review successfully saved to backend');
        
        // After successful review, move to the next card
        moveToNextCard();
      } catch (apiError) {
        console.error('Error saving review to backend:', apiError);
        
        // Even if there's an API error, continue with the next card to ensure smooth UX
        moveToNextCard();
      }
    } catch (error) {
      console.error('Error in submit review workflow:', error);
      setError('Failed to complete review workflow: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
      setShowAnswer(false);
      setResponseStartTime(null); // Reset timer
    }
  };

  // Handle showing the answer and start the response timer
  const handleShowAnswer = () => {
    setResponseStartTime(new Date()); // Start timing when answer is shown
    setShowAnswer(true);
  };

  // Handle keyboard shortcuts
  const handleKeyPress = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!flashcard) return;
    
    if (!showAnswer && e.key === ' ') {
      // Space to show answer
      e.preventDefault();
      handleShowAnswer();
    } else if (showAnswer) {
      // 1, 2, 3 for difficulty ratings
      if (e.key === '1') {
        e.preventDefault();
        submitReview(1); // Hard
      } else if (e.key === '2') {
        e.preventDefault();
        submitReview(2); // Medium
      } else if (e.key === '3') {
        e.preventDefault();
        submitReview(3); // Easy
      }
    }
  };

  // Progress bar component
  const ProgressBar = ({ stats }: { stats: FlashcardStats }) => {
    const progressPercentage = stats.total > 0 ? (stats.reviewed / stats.total) * 100 : 0;
    
    return (
      <div className="w-full mb-6">
        <div className="flex justify-between mb-2">
          <span className="text-sm text-muted-foreground">Progress</span>
          <span className="text-sm font-medium">{Math.round(progressPercentage)}%</span>
        </div>
        <div className="h-2 bg-muted rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary rounded-full" 
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
          <span>{stats.reviewed} reviewed</span>
          <span>{stats.due} due</span>
          <span>{stats.total} total</span>
        </div>
      </div>
    );
  };

  // Initial load of flashcards
  useEffect(() => {
    loadFlashcards();
    // Reset our reviewed cards when project changes
    setReviewedCardIds(new Set());
  }, [projectId]);

  // Render component
  return (
    <div 
      className="flex flex-col items-center w-full h-full"
      tabIndex={0}
      onKeyDown={handleKeyPress}
    >
      {/* Include the CSS styles */}
      <style jsx>{styles}</style>
      
      {loading ? (
        <div className="flex flex-col items-center justify-center p-8 w-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-muted-foreground">Loading flashcards...</p>
        </div>
      ) : error ? (
        <div className="p-6 bg-destructive/10 rounded-lg w-full">
          <h3 className="font-medium text-destructive mb-2">Error</h3>
          <p className="text-muted-foreground">{error}</p>
          <Button 
            onClick={() => loadFlashcards()} 
            className="mt-4"
            variant="outline"
          >
            Try Again
          </Button>
        </div>
      ) : (
        <>
          {/* Flashcard content first */}
          {flashcard ? (
            <div className="w-full mt-20 ">
              <div 
                className={`flip-card w-full h-[300px] bg-transparent perspective-1000 cursor-pointer ${showAnswer ? 'flipped' : ''}`}
                onClick={handleShowAnswer}
              >
                <div className="flip-card-inner relative w-full h-full transition-transform duration-500 transform-style-preserve-3d">
                  {/* Front of card (Question) */}
                  <div className="flip-card-front absolute w-full h-full backface-hidden overflow-hidden rounded-3xl">
                    <Card className="w-full h-full flex flex-col rounded-3xl border-0 bg-gray-100 dark:bg-[#1e1e1e] overflow-hidden">
                      <CardHeader>
                        <h3 className="text-xl font-semibold">Question</h3>
                      </CardHeader>
                      <CardContent className="flex-1 flex items-center">
                        <p className="text-center w-full">{flashcard.question}</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Back of card (Answer) */}
                  <div className="flip-card-back absolute w-full h-full backface-hidden rotate-y-180 overflow-hidden rounded-3xl">
                    <Card className="w-full h-full flex flex-col rounded-3xl border-0 bg-gray-100 dark:bg-[#1e1e1e] overflow-hidden">
                      <CardHeader>
                        <h3 className="text-xl font-semibold">Answer</h3>
                      </CardHeader>
                      <CardContent className="flex-1 flex items-center">
                        <p className="text-center w-full">{flashcard.answer}</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                {showAnswer && (
                  <div className="grid grid-cols-3 gap-4">
                    <Button
                      onClick={() => submitReview(1)}
                      variant="destructive"
                    >
                      Hard
                    </Button>
                    <Button
                      onClick={() => submitReview(2)}
                      variant="secondary"
                    >
                      Medium
                    </Button>
                    <Button
                      onClick={() => submitReview(3)}
                      variant="default"
                    >
                      Easy
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : stats.total > 0 ? (
            <div className="text-center p-6 bg-muted/50 rounded-lg w-full">
              <h3 className="font-medium mb-2">No Flashcards Due</h3>
              <p className="text-muted-foreground mb-4">
                You do not have any flashcards due for review at this time.
                {stats.reviewed === stats.total ? (
                  <span className="block mt-2 font-medium text-green-600">
                    Congratulations! You have reviewed all your flashcards.
                  </span>
                ) : (
                  <span className="block mt-2">
                    You have reviewed {stats.reviewed} out of {stats.total} flashcards.
                  </span>
                )}
              </p>
              <Button 
                onClick={() => loadFlashcards()} 
                variant="outline"
              >
                Check Again
              </Button>
            </div>
          ) : (
            <div className="text-center p-6 bg-muted/50 rounded-lg w-full">
              <h3 className="font-medium mb-2">No Flashcards Available</h3>
              <p className="text-muted-foreground mb-4">
                There are no flashcards available for this project yet.
                Try generating some flashcards first.
              </p>
            </div>
          )}
          
          {/* Progress bar moved to the bottom */}
          {stats.total > 0 && (
            <Card className="w-full mt-6 rounded-3xl border-transparent bg-card dark:bg-[#1e1e1e]">
              <CardContent className="pt-6">
                <ProgressBar stats={stats} />
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
} 