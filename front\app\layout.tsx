import type { Metada<PERSON> } from "next/dist/lib/metadata/types/metadata-interface";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { LanguageProvider } from './providers/language-provider'
import ClerkThemeProvider from '@/components/theme/clerk-theme-provider';
import { ThemeProvider } from '../components/theme/theme-provider';
import { Analytics } from "@vercel/analytics/react"
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI-Powered medical education platform",
  description: "AI-Powered medical education platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider>
          <ClerkThemeProvider>
            <LanguageProvider>
              {children}
            </LanguageProvider>
          </ClerkThemeProvider>
        </ThemeProvider>
        <Analytics />
      </body>
    </html>
  );
}
