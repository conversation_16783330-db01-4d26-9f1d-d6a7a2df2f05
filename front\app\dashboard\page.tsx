"use client";

import React, { useState, useEffect } from "react";
import { useTheme } from "@/components/theme/theme-provider";
import { motion, AnimatePresence } from "framer-motion";
import {
  GraduationCap,
  Microscope,
  BookOpen,
  Search,
  CodeSquare,
  Brain,
 
} from "lucide-react";
import Link from "next/link";
import NavLayout from "@/components/NavLayout";
import { useLanguage } from "@/app/providers/language-provider";

export default function Dashboard() {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const sections = [
    {
      id: "clinical-notes",
      title: t("dashboard.sections.clinical_notes.title"),
      icon: <Brain size={24} />,
      color: theme === "dark" ? "#3b82f6" : "#2563eb",
      description: t("dashboard.sections.clinical_notes.description"),
      items: [],
    },
    {
      id: "medical-research",
      title: t("dashboard.sections.medical_research.title"),
      icon: <Microscope size={24} />,
      color: theme === "dark" ? "#ec4899" : "#db2777",
      description: t("dashboard.sections.medical_research.description"),
      items: [
        { name: t("dashboard.sections.medical_research.items.search"), icon: <Search size={18} />, path: "/chat" },
        { name: t("dashboard.sections.medical_research.items.evidence"), icon: <BookOpen size={18} />, path: "/evidence" },
      ],
    },
    {
      id: "decode-knowledge",
      title: t("dashboard.sections.decode_knowledge.title"),
      icon: <GraduationCap size={24} />,
      color: theme === "dark" ? "#6366f1" : "#4f46e5",
      description: t("dashboard.sections.decode_knowledge.description"),
      items: [
        { name: t("dashboard.sections.decode_knowledge.items.decode_studio"), icon: <CodeSquare size={18} />, path: "/studio" },
      ],
    },
    {
      id: "decode-tutor",
      title: t("dashboard.sections.decode_tutor.title"),
      icon: <BookOpen size={24} />,
      color: theme === "dark" ? "#10b981" : "#059669",
      description: t("dashboard.sections.decode_tutor.description"),
      items: [],
    },
  ];

  return (
    <NavLayout>
      <div className={`min-h-screen ${theme === "dark" ? "bg-[hsl(0_0%_7.0%)] text-white" : "bg-white text-gray-800"}`}>
        <div className="container mx-auto px-4 py-8">
          <AnimatePresence>
            {isLoaded && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
              </motion.div>
            )}
          </AnimatePresence>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <style jsx global>{`
              @media (min-width: 1024px) {
                .medical-research {
                  grid-column: 3;
                }
                .decode-knowledge {
                  grid-column: 2;
                }
              }
            `}</style>
            {sections.map((section, index) => (
              <AnimatePresence key={section.id}>
                {isLoaded && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className={`rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ${
                      theme === "dark" 
                        ? "bg-[hsl(0_0%_10%)] border border-[#1e1e1e] hover:border-[#2a2a2a]" 
                        : "bg-white border border-gray-100 hover:border-gray-200"
                    }`}
                    onMouseEnter={() => setActiveSection(section.id)}
                    onMouseLeave={() => setActiveSection(null)}
                  >
                    <div className="flex items-center mb-4">
                      <div 
                        className="p-3 rounded-lg mr-4" 
                        style={{ 
                          backgroundColor: theme === "dark" 
                            ? `${section.color}20` 
                            : `${section.color}10`,
                          color: section.color
                        }}
                      >
                        {section.icon}
                      </div>
                      <h2 className="text-xl font-semibold">{section.title}</h2>
                    </div>
                    
                    <p className={`mb-6 ${theme === "dark" ? "text-gray-400" : "text-gray-600"}`}>
                      {section.description}
                    </p>
                    
                    <div className="space-y-3">
                      {section.items.map((item, itemIndex) => (
                        <motion.div
                          key={itemIndex}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ 
                            opacity: activeSection === section.id || activeSection === null ? 1 : 0.7,
                            x: 0 
                          }}
                          transition={{ duration: 0.3, delay: itemIndex * 0.1 }}
                        >
                          <Link 
                            href={item.path}
                            className={`flex items-center p-2 rounded-lg transition-all duration-200 ${
                              theme === "dark" 
                                ? "hover:bg-[hsl(0_0%_15%)]" 
                                : "hover:bg-gray-100"
                            }`}
                          >
                            <div className="mr-3 text-gray-500">{item.icon}</div>
                            <span>{item.name}</span>
                          </Link>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            ))}
          </div>
        </div>
      </div>
    </NavLayout>
  );
}
