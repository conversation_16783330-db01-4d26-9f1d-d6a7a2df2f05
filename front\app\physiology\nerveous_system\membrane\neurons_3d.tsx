"use client"

import { useRef, useEffect, useState, createContext, useContext, RefObject, MutableRefObject } from "react"
import { Canvas, useFrame, extend } from "@react-three/fiber"
import { OrbitControls, Effects } from "@react-three/drei"
import * as THREE from "three"
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass"

// Extend Three.js with the UnrealBloomPass for glow effects
extend({ UnrealBloomPass })

// Helper types for neuron lines
type XYZ = { x: number; y: number; z: number }
type ExtendedLine = THREE.Line<THREE.BufferGeometry, THREE.LineBasicMaterial> & { startPoint?: XYZ; endPoint?: XYZ; isActive?: boolean }

// Graph data for action potentials
type Phase = 'resting' | 'depolarizing' | 'repolarizing'
type VoltagePoint = { time: number; value: number; phase: Phase }

// Define context types
interface AnimationContextType {
  propagationSpeed: number
  firingRate: number
  isAnimating: boolean
  glowIntensity: number
  showGraphs: boolean
  setPropagationSpeed: (speed: number) => void
  setFiringRate: (rate: number) => void
  setIsAnimating: (animating: boolean) => void
  setGlowIntensity: (intensity: number) => void
  setShowGraphs: (show: boolean) => void
  triggerActionPotential: MutableRefObject<() => void>
  voltageData: VoltagePoint[][]
}

// Create context with default placeholders
const AnimationContext = createContext<AnimationContextType>({
  propagationSpeed: 25,
  firingRate: 1500,
  isAnimating: true,
  glowIntensity: 1.5,
  showGraphs: true,
  setPropagationSpeed: () => {},
  setFiringRate: () => {},
  setIsAnimating: () => {},
  setGlowIntensity: () => {},
  setShowGraphs: () => {},
  triggerActionPotential: { current: () => {} },
  voltageData: [[]],
})

// Base colors for neurons
const NEURON_COLORS = [
  "#00e8e8", // bright teal
  "#00e8e8", // bright teal
  "#ff9fff", // bright pink
  "#ff3333", // bright red
]

// Action potential colors with high luminosity
const ACTION_POTENTIAL_COLORS = {
  resting: "#444444", // dark gray for resting state
  depolarizing: "#ffffff", // bright white for depolarization
  repolarizing: "#ffff00", // yellow for repolarization
}

// Simple neuron structure using basic THREE.Line with action potential
function NeuronStructure({ position = [0, 0, 0], colorIndex = 0 }) {
  const groupRef = useRef<THREE.Group | null>(null)
  const linesRef = useRef<ExtendedLine[]>([])
  const actionPotentialsRef = useRef<any[]>([])
  const timerRef = useRef<any>(null)
  const branchPointsRef = useRef<XYZ[]>([])

  // Get animation settings and voltage data from context
  const {
    propagationSpeed,
    firingRate,
    isAnimating,
    triggerActionPotential: contextTrigger,
    voltageData,
  } = useContext(AnimationContext)

  // Function to trigger a new action potential at a random branch point
  const triggerActionPotential = () => {
    if (linesRef.current.length === 0) return
    const startPoints = branchPointsRef.current
    if (startPoints.length === 0) return

    const startPoint =
      startPoints[Math.floor(Math.random() * startPoints.length)]
    const connected = linesRef.current.filter(
      (line) =>
        line.startPoint &&
        Math.abs(line.startPoint.x - startPoint.x) < 0.1 &&
        Math.abs(line.startPoint.y - startPoint.y) < 0.1 &&
        Math.abs(line.startPoint.z - startPoint.z) < 0.1
    )

    if (connected.length) {
      connected.forEach((line) => {
        actionPotentialsRef.current.push({
          currentLine: line,
          progress: 0,
          nextLines: [],
          phase: "depolarizing",
          age: 0,
        })
      })
      const data = voltageData[colorIndex]
      if (data) {
        data.push({ time: Date.now(), value: 40, phase: "depolarizing" })
      }
    }
  }

  // Share trigger with context
  useEffect(() => {
    contextTrigger.current = triggerActionPotential
  }, [contextTrigger])

  // Initialize neuron structure and timer
  useEffect(() => {
    if (!groupRef.current) return
    // clear
    groupRef.current.clear()
    linesRef.current = []
    actionPotentialsRef.current = []
    branchPointsRef.current = []
    createNeuronStructure(
      groupRef.current,
      NEURON_COLORS[colorIndex % NEURON_COLORS.length],
      linesRef,
      branchPointsRef
    )
    startTimer()
    return () => clearInterval(timerRef.current)
  }, [colorIndex])

  // restart timer on rate/animation change
  useEffect(() => {
    startTimer()
    return () => clearInterval(timerRef.current)
  }, [firingRate, isAnimating])

  function startTimer() {
    clearInterval(timerRef.current)
    if (isAnimating) {
      timerRef.current = setInterval(
        triggerActionPotential,
        firingRate + Math.random() * 500
      )
    }
  }

  // update loop
  useFrame((_, delta) => {
    if (!isAnimating) return
    // reset colors
    linesRef.current.forEach((ln) => {
      ln.material.color.set(
        ln.isActive
          ? ACTION_POTENTIAL_COLORS.resting
          : NEURON_COLORS[colorIndex % NEURON_COLORS.length]
      )
    })
    actionPotentialsRef.current = actionPotentialsRef.current.filter((ap) => {
      ap.age += delta
      ap.progress += delta * propagationSpeed
      if (ap.progress >= 1 && ap.phase === "depolarizing") {
        ap.phase = "repolarizing"
        ap.progress = 0
        const data = voltageData[colorIndex]
        data.push({ time: Date.now(), value: -80, phase: "repolarizing" })
        setTimeout(() => {
          data.push({ time: Date.now(), value: -70, phase: "resting" })
        }, 100)
      }
      if (ap.phase === "repolarizing" && ap.progress >= 1) return false
      const mat = ap.currentLine.material
      mat.color.set(
        ap.phase === "depolarizing"
          ? ACTION_POTENTIAL_COLORS.depolarizing
          : ACTION_POTENTIAL_COLORS.repolarizing
      )
      mat.linewidth = ap.phase === "depolarizing" ? 2 : 1.5
      return true
    })
    if (groupRef.current) {
      groupRef.current.rotation.y =
        Math.sin(performance.now() * 0.001 * 0.1) * 0.1
    }
  })

  return <group ref={groupRef} position={position} />
}

function findConnectedLines(
  pt: XYZ,
  allLines: ExtendedLine[],
  cur: ExtendedLine
): ExtendedLine[] {
  const res = []
  const thr = 0.1
  allLines.forEach((ln) => {
    if (ln === cur) return
    if (
      ln.startPoint &&
      Math.abs(ln.startPoint.x - pt.x) < thr &&
      Math.abs(ln.startPoint.y - pt.y) < thr &&
      Math.abs(ln.startPoint.z - pt.z) < thr
    ) {
      res.push(ln)
    }
  })
  return res
}

function createNeuronStructure(
  parent: THREE.Group,
  color: string,
  linesRef: RefObject<ExtendedLine[]>,
  branchPointsRef: RefObject<XYZ[]>
) {
  createSoma(parent, color, linesRef, branchPointsRef)
  const end = createMainTrunk(parent, color, linesRef, branchPointsRef)
  createBranches(parent, end, color, 3, linesRef, branchPointsRef)
}

function createSoma(
  parent: THREE.Group,
  color: string,
  linesRef: RefObject<ExtendedLine[]>,
  branchPointsRef: RefObject<XYZ[]>
) {
  for (let i = 0; i < 20; i++) {
    const x = (Math.random() - 0.5) * 2
    const y = (Math.random() - 0.5) * 2
    const z = (Math.random() - 0.5) * 2
    const pts = [new THREE.Vector3(0, 0, 0), new THREE.Vector3(x, y, z)]
    const geo = new THREE.BufferGeometry().setFromPoints(pts)
    const mat = new THREE.LineBasicMaterial({ color })
    const line = new THREE.Line(geo, mat) as ExtendedLine
    line.startPoint = { x: 0, y: 0, z: 0 }
    line.endPoint = { x, y, z }
    line.isActive = true
    parent.add(line)
    linesRef.current.push(line)
  }
  branchPointsRef.current.push({ x: 0, y: 0, z: 0 })
}

function createMainTrunk(
  parent: THREE.Group,
  color: string,
  linesRef: RefObject<ExtendedLine[]>,
  branchPointsRef: RefObject<XYZ[]>
): XYZ {
  const height = 8 + Math.random() * 4
  const segs = 10
  const segH = height / segs
  let px = 0, py = 0, pz = 0
  let cx = 0, cy = 0, cz = 0
  for (let i = 0; i < segs; i++) {
    px = cx; py = cy; pz = cz
    cy += segH; cx += (Math.random() - 0.5) * 0.3; cz += (Math.random() - 0.5) * 0.3
    const pts = [new THREE.Vector3(px, py, pz), new THREE.Vector3(cx, cy, cz)]
    const geo = new THREE.BufferGeometry().setFromPoints(pts)
    const mat = new THREE.LineBasicMaterial({ color })
    const line = new THREE.Line(geo, mat) as ExtendedLine
    line.startPoint = { x: px, y: py, z: pz }
    line.endPoint = { x: cx, y: cy, z: cz }
    line.isActive = true
    parent.add(line)
    linesRef.current.push(line)
    if (i % 2 === 0) branchPointsRef.current.push({ x: cx, y: cy, z: cz })
  }
  return { x: cx, y: cy, z: cz }
}

function createBranches(
  parent: THREE.Group,
  startPt: XYZ,
  color: string,
  depth: number,
  linesRef: RefObject<ExtendedLine[]>,
  branchPointsRef: RefObject<XYZ[]>
) {
  if (depth <= 0) return
  const nb = 2 + Math.floor(Math.random() * 3)
  for (let i = 0; i < nb; i++) {
    const len = 1 + Math.random() * 2
    const segs = 3 + Math.floor(Math.random() * 3)
    const segL = len / segs
    let px = startPt.x, py = startPt.y, pz = startPt.z
    let cx = px, cy = py, cz = pz
    let dx = (Math.random() - 0.5) * 2, dy = 0.3 + Math.random() * 0.7, dz = (Math.random() - 0.5) * 2
    const mag = Math.sqrt(dx*dx+dy*dy+dz*dz)
    if (mag > 1e-4) { dx/=mag; dy/=mag; dz/=mag } else { dx=0;dy=1;dz=0 }
    for (let j = 0; j < segs; j++) {
      px= cx; py= cy; pz= cz
      cx += dx*segL + (Math.random()-0.5)*0.2
      cy += dy*segL + (Math.random()-0.5)*0.2
      cz += dz*segL + (Math.random()-0.5)*0.2
      const pts = [new THREE.Vector3(px,py,pz), new THREE.Vector3(cx,cy,cz)]
      const geo = new THREE.BufferGeometry().setFromPoints(pts)
      const mat = new THREE.LineBasicMaterial({ color })
      const line = new THREE.Line(geo, mat) as ExtendedLine
      line.startPoint = { x: px, y: py, z: pz }
      line.endPoint = { x: cx, y: cy, z: cz }
      line.isActive = true
      parent.add(line)
      linesRef.current.push(line)
    }
    branchPointsRef.current.push({ x: cx, y: cy, z: cz })
    if (depth > 1 && Math.random() > 0.3)
      createBranches(parent, { x: cx, y: cy, z: cz }, color, depth-1, linesRef, branchPointsRef)
  }
}

function GlowEffects() {
  const { glowIntensity } = useContext(AnimationContext)
  return (
    <Effects disableGamma>
      <unrealBloomPass threshold={0.1} strength={glowIntensity} radius={1} />
    </Effects>
  )
}

function ActionPotentialGraph({ neuronIndex }: { neuronIndex: number }) {
  const { voltageData, showGraphs } = useContext(AnimationContext)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const width = 500, height = 200, pad = 20, maxP = 200

  useEffect(() => {
    if (!canvasRef.current || !showGraphs) return
    const ctx = canvasRef.current.getContext("2d")
    if (!ctx) return
    ctx.clearRect(0,0, width, height)
    ctx.fillStyle = "rgba(0,0,0,0.7)"
    ctx.fillRect(0,0,width, height)
    ctx.strokeStyle="#666"; ctx.lineWidth=1
    ctx.beginPath(); ctx.moveTo(pad, pad); ctx.lineTo(pad, height-pad); ctx.stroke()
    const restY = mapVoltageToY(-70)
    ctx.beginPath(); ctx.moveTo(pad, restY); ctx.lineTo(width-pad, restY); ctx.stroke()
    ctx.fillStyle="#999"; ctx.font="8px Arial"; ctx.textAlign="right"
    ctx.fillText("+40", pad-2, mapVoltageToY(40))
    ctx.fillText("-70", pad-2, mapVoltageToY(-70)+3)
    ctx.fillText("-80", pad-2, mapVoltageToY(-80))
    const data = voltageData[neuronIndex] || []
    const recent = data.slice(-maxP)
    if (recent.length <2) {
      ctx.strokeStyle="#888"; ctx.beginPath(); ctx.moveTo(pad, restY); ctx.lineTo(width-pad, restY); ctx.stroke();
    } else {
      const now = Date.now(); const win = 2000; const old = now-win
      ctx.strokeStyle = NEURON_COLORS[neuronIndex]
      ctx.lineWidth=2; ctx.beginPath(); ctx.moveTo(pad, restY)
      recent.forEach((pt, i) => {
        const x = pad + ((pt.time-old)/(win))*(width-2*pad)
        const y = mapVoltageToY(pt.value)
        i? ctx.lineTo(x,y): ctx.moveTo(x,y)
      })
      const last = recent[recent.length-1]
      if (last.value !== -70) ctx.lineTo(width-pad, restY)
      ctx.stroke()
    }
    function mapVoltageToY(v: number) {
      const mn=-90, mx=50, avail=height-2*pad
      return height-pad - ((v-mn)/(mx-mn))*avail
    }
  }, [voltageData, neuronIndex, showGraphs])
  if (!showGraphs) return null
  return (
    <div className="w-full p-4 bg-gray-800">
      <canvas ref={canvasRef} width={width} height={height} className="rounded-md w-full" />
      <div className="text-sm text-center text-gray-300 mt-2">Neuron {neuronIndex+1} Action Potential</div>
    </div>
  )
}

export default function NeuronVisualization() {
  const [propagationSpeed, setPropagationSpeed] = useState(25)
  const [firingRate, setFiringRate] = useState(1500)
  const [isAnimating, setIsAnimating] = useState(true)
  const [glowIntensity, setGlowIntensity] = useState(1.5)
  const [showGraphs, setShowGraphs] = useState(true)
  const triggerRef = useRef<{ current: () => void }>({ current: () => {} })
  const [voltageData, setVoltageData] = useState<VoltagePoint[][]>([[]])

  useEffect(() => {
    const id = setInterval(() => {
      const now = Date.now(), win = 2000
      setVoltageData((prev) => prev.map((nd) => nd.filter((p) => now-p.time < win)))
    }, 1000)
    return () => clearInterval(id)
  }, [])

  return (
    <AnimationContext.Provider value={{
      propagationSpeed, setPropagationSpeed,
      firingRate, setFiringRate,
      isAnimating, setIsAnimating,
      glowIntensity, setGlowIntensity,
      showGraphs, setShowGraphs,
      triggerActionPotential: triggerRef,
      voltageData,
    }}>
      <div className="w-full h-screen flex bg-[hsl(240_10%_3.9%)]">
        <div className="w-1/3 h-full overflow-auto">
          <ActionPotentialGraph neuronIndex={0} />
        </div>
        <div className="flex-1 relative">
          <Canvas className="w-full h-full" camera={{ position: [0,5,20], fov:50 }}>
            <color attach="background" args={['hsl(240,10%,3.9%)']} />
            <ambientLight intensity={0.5} />
            <pointLight position={[10,10,10]} intensity={1} />
            <NeuronStructure position={[0, 0, 0]} colorIndex={0} />
            <OrbitControls />
            <gridHelper args={[20,20,'#444444','#222222']} position={[0,-5,0]} />
            <GlowEffects />
          </Canvas>
        </div>
      </div>
    </AnimationContext.Provider>
  )
}
