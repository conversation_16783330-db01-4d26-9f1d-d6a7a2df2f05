package flashcards

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"google.golang.org/genai"
)

// FlashcardGenerator handles flashcard generation using AI
type FlashcardGenerator struct {
	client *genai.Client
	config ModelConfig
}

// ModelConfig represents the configuration for the AI model
type ModelConfig struct {
	Model       string  // Model version to use
	Temperature float32 // Controls randomness (0.0 to 1.0)
	MaxTokens   int32   // Maximum tokens to generate
}

// DefaultConfig returns the default model configuration
func DefaultConfig() ModelConfig {
	return ModelConfig{
		Model:       "gemini-2.5-flash", // Using the flash version for faster responses
		Temperature: 0.2,                // Moderate temperature for creative yet focused flashcards
		MaxTokens:   4096,               // Sufficient token limit for flashcard generation
	}
}

// Flashcard represents a single flashcard with question and answer
type Flashcard struct {
	Question       string     `json:"question"`
	Answer         string     `json:"answer"`
	DifficultyRank int        `json:"difficulty_rank"` // 1-5 scale
	Tags           []string   `json:"tags,omitempty"`
	LastReviewed   *time.Time `json:"last_reviewed,omitempty"`
	NextReview     *time.Time `json:"next_review,omitempty"`
	ReviewCount    int        `json:"review_count"`
	Stability      float64    `json:"stability"`      // FSRS parameter
	Difficulty     float64    `json:"difficulty"`     // FSRS parameter
	ElapsedDays    float64    `json:"elapsed_days"`   // FSRS parameter
	ScheduledDays  float64    `json:"scheduled_days"` // FSRS parameter
	Retrievability float64    `json:"retrievability"` // FSRS probability of recall
}

// FSRS Parameters
const (
	// Default FSRS parameters based on research
	InitialStability    = 1.0
	InitialDifficulty   = 0.3
	DifficultyDecay     = 0.1
	StabilityDecay      = 0.2
	RequestRetainFactor = 0.9
)

// ------------------------CONFIGURATION--------------------------------
// loadEnv loads environment variables from .env file
func loadEnv() error {
	// Try to find .env file in the project root directory
	envFile := filepath.Join("..", "..", ".env")
	if err := godotenv.Load(envFile); err != nil {
		return fmt.Errorf("error loading .env file: %v", err)
	}
	return nil
}

// NewFlashcardGenerator creates a new flashcard generator
func NewFlashcardGenerator() (*FlashcardGenerator, error) {
	// Load environment variables from .env file
	if err := loadEnv(); err != nil {
		fmt.Printf("Warning: %v\n", err)
	}

	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("GEMINI_API_KEY environment variable is not set. Please add it to your .env file")
	}

	ctx := context.Background()
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		APIKey:  apiKey,
		Backend: genai.BackendGeminiAPI,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %v", err)
	}

	return &FlashcardGenerator{
		client: client,
		config: DefaultConfig(),
	}, nil
}

// SetConfig updates the model configuration
func (g *FlashcardGenerator) SetConfig(config ModelConfig) {
	g.config = config
}

// GenerateFlashcardsFromContent generates flashcards from unit content
func (g *FlashcardGenerator) GenerateFlashcardsFromContent(ctx context.Context, title string, content string, contentType string) ([]Flashcard, error) {
	// Build prompt based on content type
	prompt := g.buildFlashcardPrompt(title, content, contentType)

	// Generate content
	response, err := g.generateContent(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to generate flashcards: %v", err)
	}

	// Parse response into flashcards
	flashcards, err := g.parseFlashcards(response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse flashcards: %v", err)
	}

	// Initialize FSRS parameters for each flashcard
	now := time.Now()
	for i := range flashcards {
		flashcards[i].LastReviewed = &now
		flashcards[i].ReviewCount = 0
		flashcards[i].Stability = InitialStability
		flashcards[i].Difficulty = InitialDifficulty

		// Calculate next review date based on initial stability
		nextReview := now.Add(time.Duration(24*InitialStability) * time.Hour)
		flashcards[i].NextReview = &nextReview
		flashcards[i].ScheduledDays = InitialStability
		flashcards[i].ElapsedDays = 0
		flashcards[i].Retrievability = 0.9 // Initial retrievability
	}

	return flashcards, nil
}

// generateContent sends a prompt to the AI model and returns the response
func (g *FlashcardGenerator) generateContent(ctx context.Context, prompt string) (string, error) {
	// Create the content from the prompt
	content := genai.Text(prompt)

	// Set generation config
	config := &genai.GenerateContentConfig{
		Temperature:     genai.Ptr(float32(g.config.Temperature)),
		MaxOutputTokens: genai.Ptr(int32(g.config.MaxTokens)),
	}

	// Generate content
	result, err := g.client.Models.GenerateContent(ctx, g.config.Model, content, config)
	if err != nil {
		return "", fmt.Errorf("error generating content: %v", err)
	}

	if len(result.Candidates) == 0 {
		return "", fmt.Errorf("no candidates in response")
	}

	// Get the text from the response
	text := result.Text()

	return text, nil
}

// buildFlashcardPrompt creates a prompt for the AI model to generate flashcards
func (g *FlashcardGenerator) buildFlashcardPrompt(title string, content string, contentType string) string {
	return fmt.Sprintf(`Create effective flashcards for spaced repetition learning from the following educational content.
	Focus on key concepts, definitions, relationships, and facts that would benefit from memorization.

	Follow these requirements:
	1. Create 10-15 high-quality flashcards
	2. Format each flashcard exactly like this:
	   Q: [Clear, concise question]
	   A: [Precise, focused answer]
	   TAGS: [comma-separated list of tags]
	   DIFFICULTY: [1-5 scale, where 1 is easiest and 5 is hardest]

	3. Questions should be specific, unambiguous, and test a single concept
	4. Answers should be concise but comprehensive
	5. Include a mix of difficulty levels
	6. Focus on principles and concepts rather than trivial details
	7. For medical content: emphasize clinically relevant information, pathophysiology, diagnostics, and treatments
	8. For %s content: focus on the most important information
	8. The language output should come from the language input


    
	

	Content Title: %s
	Content: %s`, contentType, title, content)
}

// parseFlashcards parses the AI response into structured flashcards
func (g *FlashcardGenerator) parseFlashcards(response string) ([]Flashcard, error) {
	var flashcards []Flashcard

	// Split the response by flashcard (each starts with "Q: ")
	parts := strings.Split(response, "Q: ")

	// Skip the first part if it doesn't contain a question
	startIdx := 0
	if !strings.Contains(parts[0], "A: ") {
		startIdx = 1
	}

	for i := startIdx; i < len(parts); i++ {
		part := strings.TrimSpace(parts[i])
		if part == "" {
			continue
		}

		var flashcard Flashcard

		// Extract question
		flashcard.Question = part
		if aIdx := strings.Index(part, "A: "); aIdx != -1 {
			flashcard.Question = strings.TrimSpace(part[:aIdx])
			part = part[aIdx+3:]
		} else {
			continue // Skip if no answer
		}

		// Extract answer
		if tagsIdx := strings.Index(part, "TAGS: "); tagsIdx != -1 {
			flashcard.Answer = strings.TrimSpace(part[:tagsIdx])
			part = part[tagsIdx+6:]
		} else if diffIdx := strings.Index(part, "DIFFICULTY: "); diffIdx != -1 {
			flashcard.Answer = strings.TrimSpace(part[:diffIdx])
			part = part[diffIdx+12:]
		} else {
			flashcard.Answer = strings.TrimSpace(part)
			continue // No tags or difficulty
		}

		// Extract tags
		if tagsIdx := strings.Index(part, "TAGS: "); tagsIdx != -1 {
			part = part[tagsIdx+6:]

			if diffIdx := strings.Index(part, "DIFFICULTY: "); diffIdx != -1 {
				tagStr := strings.TrimSpace(part[:diffIdx])
				flashcard.Tags = strings.Split(tagStr, ",")
				for i, tag := range flashcard.Tags {
					flashcard.Tags[i] = strings.TrimSpace(tag)
				}
				part = part[diffIdx+12:]
			}
		}

		// Extract difficulty
		if diffIdx := strings.Index(part, "DIFFICULTY: "); diffIdx != -1 {
			part = part[diffIdx+12:]
			diffStr := strings.TrimSpace(strings.Split(part, "\n")[0])
			var difficultyRank int
			_, err := fmt.Sscanf(diffStr, "%d", &difficultyRank)
			if err != nil {
				// Default to medium difficulty if parsing fails
				difficultyRank = 3
			}
			flashcard.DifficultyRank = difficultyRank
		}

		flashcards = append(flashcards, flashcard)
	}

	return flashcards, nil
}

// UpdateFlashcardWithFSRS updates flashcard scheduling using the FSRS algorithm
// based on a review grade (0-5 where 0=completely forgotten, 5=perfect recall)
func UpdateFlashcardWithFSRS(card *Flashcard, grade int, reviewTime time.Time) {
	// Calculate elapsed days since last review
	var elapsedDays float64
	if card.LastReviewed != nil {
		elapsedDays = reviewTime.Sub(*card.LastReviewed).Hours() / 24
	}
	card.ElapsedDays = elapsedDays

	// Calculate retrievability at the moment of review
	// R = e^(-t/S), where t is time elapsed and S is stability
	retrievability := 0.0
	if card.Stability > 0 {
		retrievability = exponentialDecay(elapsedDays, card.Stability)
	}
	card.Retrievability = retrievability

	// Update difficulty based on performance
	// Lower grade = higher difficulty
	difficultyDelta := calculateDifficultyDelta(grade)
	card.Difficulty = boundValue(card.Difficulty+difficultyDelta*DifficultyDecay, 0.1, 1.0)

	// Update stability based on performance and current parameters
	newStability := calculateNewStability(card.Stability, card.Difficulty, retrievability, grade)
	card.Stability = newStability

	// Calculate days until next review
	scheduledDays := calculateInterval(newStability, grade)
	card.ScheduledDays = scheduledDays

	// Set next review date
	nextReview := reviewTime.Add(time.Duration(24*scheduledDays) * time.Hour)
	card.NextReview = &nextReview

	// Update review count and last reviewed
	card.ReviewCount++
	card.LastReviewed = &reviewTime
}

// Helper functions for FSRS calculations

// exponentialDecay calculates e^(-t/S)
func exponentialDecay(elapsed, stability float64) float64 {
	if stability <= 0 {
		return 0
	}
	return math.Exp(-elapsed / stability)
}

// calculateDifficultyDelta adjusts difficulty based on review grade
func calculateDifficultyDelta(grade int) float64 {
	switch grade {
	case 0, 1:
		return 0.1 // Increase difficulty for poor recall
	case 2:
		return 0.05
	case 3:
		return 0.0 // No change for expected performance
	case 4:
		return -0.05
	case 5:
		return -0.1 // Decrease difficulty for perfect recall
	default:
		return 0.0
	}
}

// calculateNewStability determines the new stability value
func calculateNewStability(oldStability, difficulty, retrievability float64, grade int) float64 {
	// Failed recall (grade < 3) resets stability with penalty
	if grade < 3 {
		return oldStability * 0.5 * (float64(grade) / 3.0)
	}

	// Calculate stability increase for successful recall
	// Better performance = higher stability increase
	retrievabilityFactor := (1-retrievability)*0.5 + 0.5
	difficultyFactor := 1 - difficulty*0.5
	performanceFactor := float64(grade-2) / 3.0 // 0 for grade=3, 1 for grade=5

	stabilityIncrease := retrievabilityFactor * difficultyFactor * (1 + performanceFactor)
	newStability := oldStability * (1 + stabilityIncrease)

	return math.Max(1.0, newStability) // Minimum stability of 1 day
}

// calculateInterval determines days until next review
func calculateInterval(stability float64, grade int) float64 {
	// For failed cards, shorter interval to reinforce
	if grade < 3 {
		return 1.0 // Review again tomorrow
	}

	// For successful cards, interval based on stability
	// Add variance based on grade to avoid same-day reviews
	gradeFactor := 0.9 + 0.1*float64(grade-2) // 0.9 for grade=3, 1.1 for grade=5
	interval := stability * gradeFactor

	// Ensure minimum interval and apply some randomness
	randomFactor := 0.95 + 0.1*rand.Float64() // 0.95-1.05 random factor
	return math.Max(1.0, interval*randomFactor)
}

// boundValue ensures a value is within specified bounds
func boundValue(value, min, max float64) float64 {
	if value < min {
		return min
	}
	if value > max {
		return max
	}
	return value
}

// GenerateWithGemini is a helper function to generate flashcards without creating a generator instance
func GenerateWithGemini(ctx context.Context, title string, content string, contentType string) ([]Flashcard, error) {
	generator, err := NewFlashcardGenerator()
	if err != nil {
		return nil, err
	}

	return generator.GenerateFlashcardsFromContent(ctx, title, content, contentType)
}
