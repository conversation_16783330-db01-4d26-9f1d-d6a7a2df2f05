package advanced_flashcards

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/joho/godotenv"
)

// OcrResponse is the struct for the OCR API response
type OcrResponse struct {
	Pages     interface{} `json:"pages"`
	CreatedAt string      `json:"created_at"`
	ID        string      `json:"id"`
}

// ProcessDocumentOCR processes a document URL and returns an OCR result using the Mistral API
func ProcessDocumentOCR(documentURL string) (*OcrResponse, error) {
	// Load environment variables from .env file
	if err := findAndLoadEnv(); err != nil {
		fmt.Printf("Warning: %v\n", err)
	}

	// Retrieve API key
	apiKey := os.Getenv("MISTRAL_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("MISTRAL_API_KEY environment variable is not set")
	}

	// Define API endpoint
	url := "https://api.mistral.ai/v1/ocr"

	// Create the request body exactly according to Mistral's documentation
	requestBody := map[string]interface{}{
		"model": "mistral-ocr-latest",
		"document": map[string]interface{}{
			"type":         "document_url",
			"document_url": documentURL,
		},
		"include_image_base64": true,
	}

	// Marshal to JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Set headers exactly as in curl example
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// Create HTTP client
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: false},
		},
	}

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-200 status code: %d", resp.StatusCode)
	}

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	// Parse the raw API response to see what we're getting
	var rawResponse map[string]interface{}
	if err := json.Unmarshal(responseBody, &rawResponse); err != nil {
		return nil, fmt.Errorf("error parsing raw JSON response: %v", err)
	}

	// Create our response object with the data we have
	// Since the Mistral API might not return ID and CreatedAt fields,
	// we'll generate them ourselves
	result := &OcrResponse{
		Pages: rawResponse["pages"],
		// Generate our own ID and CreatedAt if they don't exist in the response
		ID:        fmt.Sprintf("doc-%d", time.Now().UnixNano()),
		CreatedAt: time.Now().Format(time.RFC3339),
	}

	// If the API actually provides these fields, use them instead
	if id, ok := rawResponse["id"].(string); ok && id != "" {
		result.ID = id
	}
	if createdAt, ok := rawResponse["created_at"].(string); ok && createdAt != "" {
		result.CreatedAt = createdAt
	}

	return result, nil
}

// findAndLoadEnv loads environment variables from .env file
func findAndLoadEnv() error {
	// Try to find .env file in current directory and parent directories
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	for {
		envFile := filepath.Join(dir, ".env")
		if _, err := os.Stat(envFile); err == nil {
			// Found .env file, load it
			if err := godotenv.Load(envFile); err != nil {
				return fmt.Errorf("error loading .env file: %v", err)
			}
			return nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}

	return fmt.Errorf(".env file not found in current or parent directories")
}

// Helper function to get minimum of two integers
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}
