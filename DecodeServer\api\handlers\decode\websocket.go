package handlers

import (
	"encoding/json"
	"log"

	"decodemed/api/handlers/async"

	"github.com/gin-gonic/gin"
)

// HandleWebSocket handles WebSocket connections for real-time updates
func HandleWebSocket(c *gin.Context) {
	conn, err := async.Upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	// Generate a unique connection ID
	connectionID := c.Query("client_id")
	if connectionID == "" {
		connectionID = c.GetString("user_id")
	}

	// Clean up connection when done
	defer func() {
		async.ConnectionsMutex.Lock()
		delete(async.QuizConnections, connectionID)
		delete(async.MindmapConnections, connectionID)
		delete(async.FlashcardConnections, connectionID)
		async.ConnectionsMutex.Unlock()
		log.Printf("WebSocket connection closed for client %s", connectionID)
	}()

	// Handle WebSocket connection
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			log.Printf("Error reading message: %v", err)
			break
		}

		// Handle different message types
		var request struct {
			Type    string      `json:"type"`
			Payload interface{} `json:"payload"`
		}
		if err := json.Unmarshal(message, &request); err != nil {
			log.Printf("Error unmarshaling message: %v", err)
			continue
		}

		// Handle different request types
		switch request.Type {
		case "subscribe_quiz":
			// Subscribe to quiz updates
			async.ConnectionsMutex.Lock()
			async.QuizConnections[connectionID] = conn
			async.ConnectionsMutex.Unlock()
			log.Printf("Client %s subscribed to quiz updates", connectionID)

		case "unsubscribe_quiz":
			// Unsubscribe from quiz updates
			async.ConnectionsMutex.Lock()
			delete(async.QuizConnections, connectionID)
			async.ConnectionsMutex.Unlock()
			log.Printf("Client %s unsubscribed from quiz updates", connectionID)

		case "subscribe_mindmap":
			// Subscribe to mindmap updates
			async.ConnectionsMutex.Lock()
			async.MindmapConnections[connectionID] = conn
			async.ConnectionsMutex.Unlock()
			log.Printf("Client %s subscribed to mindmap updates", connectionID)

		case "unsubscribe_mindmap":
			// Unsubscribe from mindmap updates
			async.ConnectionsMutex.Lock()
			delete(async.MindmapConnections, connectionID)
			async.ConnectionsMutex.Unlock()
			log.Printf("Client %s unsubscribed from mindmap updates", connectionID)

		case "subscribe_flashcard":
			// Subscribe to flashcard updates
			async.ConnectionsMutex.Lock()
			async.FlashcardConnections[connectionID] = conn
			async.ConnectionsMutex.Unlock()
			log.Printf("Client %s subscribed to flashcard updates", connectionID)

		case "unsubscribe_flashcard":
			// Unsubscribe from flashcard updates
			async.ConnectionsMutex.Lock()
			delete(async.FlashcardConnections, connectionID)
			async.ConnectionsMutex.Unlock()
			log.Printf("Client %s unsubscribed from flashcard updates", connectionID)
		}

		// Send acknowledgment to the client
		response := map[string]interface{}{
			"type":    "subscription_ack",
			"payload": map[string]string{"request_type": request.Type, "status": "success"},
		}
		responseJSON, _ := json.Marshal(response)
		if err := conn.WriteMessage(messageType, responseJSON); err != nil {
			log.Printf("Error writing message: %v", err)
			break
		}
	}
}
