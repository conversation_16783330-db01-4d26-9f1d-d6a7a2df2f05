from django.urls import path
from . import views
from . import consumers

websocket_urlpatterns = [
    path('ws/chat/', consumers.ChatConsumer.as_asgi(), name='chat'),
]

conversations_urlpatterns = [
    path('ws/conversations/', consumers.ChatConsumer.as_asgi(), name='conversations'),
]

urlpatterns = [
    path('chat/', views.chat_view, name='chat'),
    path('conversations/<str:user_id>/', views.get_user_conversations, name='get_user_conversations'),  
    path('history/<str:user_id>/', views.get_chat_history, name='get_chat_history'),
]
