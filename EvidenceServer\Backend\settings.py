"""
Django settings for Backend project.

Generated by 'django-admin startproject' using Django 5.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv
import dj_database_url

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Add after BASE_DIR
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

 # SECURITY WARNING: keep the secret key used in production secret!

SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')

#--------- DEBUG: SECURITY WARNING: don't run with debug turned on in production!------
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'


#--------- Domain origins Allowed Hosts Backend or server ---------
ALLOWED_HOSTS = ['0.0.0.0'] # 0.0.0.0 is the default IP address for the server

if DEBUG:
    # --------- development origins ---------
    ALLOWED_HOSTS.extend([
        'localhost',          # Localhost
        'coreevidence.loca.lt',  # Allow all localtunnel subdomains
        '***********',        # Add IP without port
        '127.0.0.1',          # Also add localhost IP
        'django-backend-production-fe36.up.railway.app',
    ])
else:
    # --------- production origins ---------
    ALLOWED_HOSTS.extend([
       
        
        'django-backend-production-fe36.up.railway.app',
        'www.decodemed.app',
        'decodemed.app',
    ])

# --------- Frontend CORS Settings ----------
CORS_ALLOWED_ORIGINS = [
    "coreevidence://*",           # Expo mobile
    "http://***********:8000",          # Expo android app IP frontend
]
if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True
    CORS_ALLOW_CREDENTIALS = True
    # --------- DEBUG:TRUE development origins ---------
    CORS_ALLOWED_ORIGINS.extend([
        "http://localhost:3000",           # React frontend
        "http://localhost:3001",           # React frontend
        "http://localhost:8081",           # Expo web frontend
        "exp://***********:8081",        # Expo web frontend
        "http://***********:8000",        # Expo android app IP frontend
        'https://coreevidence.loca.lt',            # Localtunnel subdomains
        'http://coreevidence.loca.lt',            # HTTP version of Localtunnel subdomains

       
        
    ])
# --------- DEBUG:FALSE production origins ---------
else:
    CORS_ALLOWED_ORIGINS.extend([
        "http://***********:8000",          # Expo android app IP frontend
      
       
        "https://www.decodemed.app",
        "https://decodemed.app",
        "coreevidence://*",                 # Mobile app deep linking scheme
      
        "wss://django-backend-production-fe36.up.railway.app", # WebSocket secure for Railway
    ])

    # Additional mobile app specific settings
    CORS_ALLOW_CREDENTIALS = True            # Allow credentials in production
    CORS_ORIGIN_ALLOW_ALL = True           # Temporarily allow all origins to diagnose WebSocket issues
    
    # Allow specific headers needed for mobile apps
    CORS_ALLOW_HEADERS = [
        'accept',
        'accept-encoding',
        'authorization',
        'content-type',
        'dnt',
        'origin',
        'user-agent',
        'x-csrftoken',
        'x-requested-with',
        'app-version',        # Custom header for app versioning
        'platform',           # Custom header for platform identification
    ]

CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]



# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'search_api',
    'chat_api',
    'rest_framework',
    'corsheaders',
    'rest_framework.authtoken',
    'channels',
    'redis',
    'subscriptions',
    
    

]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Add this line
    'corsheaders.middleware.CorsMiddleware',  # Corsheaders middleware
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'subscriptions.middleware.WsgiAuthenticationMiddleware',  # Use WSGI version
    'subscriptions.middleware.WsgiSubscriptionMiddleware',  # Use WSGI version
]
#------- Webhook path exemption ---------
# Add this setting to exclude webhook path from authentication
SUBSCRIPTION_AUTH_EXEMPT_PATHS = [
    '/subscription-api/webhook/',
    '/subscription-api/revenuecat-webhook/',
 
]
CORS_URLS_REGEX = r'^/(?!subscription-api/webhook/|subscription-api/revenuecat-webhook/|v1/).*$'
CORS_ALLOW_METHODS = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']



ROOT_URLCONF = 'Backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'Backend.wsgi.application'


# ----------------------DATABASE----------------------------------

# Database configuration

# Use DATABASE_URL if provided, otherwise use local PostgreSQL database
if DEBUG:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': 'history_db',
            'USER': 'postgres',
            'PASSWORD': 'postgres',
            'HOST': 'localhost',
            'PORT': '5432',
        }
    }
else:
    DATABASES = {
        'default': dj_database_url.config(
            default=os.environ.get('DATABASE_URL'),
            conn_max_age=600,
            conn_health_checks=True,
        )
    }







# --------Password validation------------
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True




# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


# ASGI application 
ASGI_APPLICATION = 'Backend.asgi.application'



# --------REDIS connection details from environment-----------------
if DEBUG:
    # Local Redis URL
    REDIS_URL = 'redis://localhost:6379/0'
else:
    # Production Redis URL from environment variable
    REDIS_URL = os.environ.get('REDIS_URL')

# ----------------- Channels settings -----------------
if DEBUG:
    # Use in-memory channel layer for local development
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels.layers.InMemoryChannelLayer"
        }
    }
else:
    # Use Redis channel layer for production
    # For rediss:// URLs, we need to handle ssl properly
    if REDIS_URL and REDIS_URL.startswith('rediss://'):
        # For secure Redis connections
        CHANNEL_LAYERS = {
            "default": {
                "BACKEND": "channels_redis.core.RedisChannelLayer",
                "CONFIG": {
                    "hosts": [{"address": REDIS_URL}],
                },
            },
        }
    else:
        # For regular Redis connections
        CHANNEL_LAYERS = {
            "default": {
                "BACKEND": "channels_redis.core.RedisChannelLayer",
                "CONFIG": {
                    "hosts": [REDIS_URL],
                },
            },
        }

#--------------REDIS CACHE -------------------
if DEBUG:
    # Use local memory cache for development
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "unique-snowflake",
        }
    }
else:
    # Use Redis cache for production
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": REDIS_URL,
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
                "CONNECTION_POOL_KWARGS": {
                    "ssl_cert_reqs": None
                } if REDIS_URL.startswith('rediss://') else {},
            }
        }
    }

# REST framework settings
REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.MultiPartParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.JSONParser',
    ],
}

# Security Settings
if not DEBUG:
    # HTTPS/SSL Settings
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    
    # Session and CSRF Settings
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    
    # HSTS Settings
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    
    # Content Security
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    
    # Frame Options
    X_FRAME_OPTIONS = 'DENY'

# Additional Security Settings (both DEBUG and Production)
# Session Settings
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 1209600  # 2 weeks in seconds
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Set SameSite attribute for cookies
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SAMESITE = 'Lax'

