"use client";

import React, { useState } from 'react';
import LeftSidebar from '@/components/left_tab/LeftTab';
import NavBar from '@/components/header/nav-bar';

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  // State to control LeftTab visibility
  const [isLeftTabVisible, setIsLeftTabVisible] = useState(false);
  
 
  

  return (
    <div className="flex flex-col min-h-screen">
      {/* LeftTab that is positioned at the very top of the screen, on top of the navbar */}
      {isLeftTabVisible && (
        <LeftSidebar 
          onHide={() => setIsLeftTabVisible(false)}
        />
      )}
      
      {/* NavBar with show button when LeftTab is hidden */}
      <div className="fixed top-0 left-0 right-0 z-10">
        <NavBar 
          isLeftTabVisible={isLeftTabVisible} 
          onShowLeftTab={() => setIsLeftTabVisible(true)} 
        />
      </div>
      
      {/* Main content area with padding for the navbar and margin for LeftTab */}
      <div className="flex flex-1 pt-12">
        <main className={`flex-1 transition-all duration-300 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
          {children}
        </main>
      </div>
    </div>
  );
}
