<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Loading Placeholder</title>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle cx="100" cy="100" r="90" stroke="#E0E0E0" stroke-width="8"></circle>
        <path d="M100,10 A90,90 0 0,1 190,100" stroke="#3B82F6" stroke-width="8" stroke-linecap="round">
            <animateTransform 
                attributeName="transform" 
                type="rotate"
                from="0 100 100"
                to="360 100 100" 
                dur="1.5s" 
                repeatCount="indefinite" />
        </path>
        <text font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#666666" text-anchor="middle" x="100" y="105">
            Loading...
        </text>
    </g>
</svg>
