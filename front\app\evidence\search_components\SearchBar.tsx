import React, { useRef, useEffect } from 'react';
import { Search } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';

interface SearchBarProps {
  question: string;
  setQuestion: React.Dispatch<React.SetStateAction<string>>;
  placeholder?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({ question, setQuestion, placeholder = "Ask a question" }) => {
  const { theme } = useTheme();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setQuestion(value);
  };

  useEffect(() => {
    const textarea = inputRef.current;
    if (textarea) {
      const adjustHeight = () => {
        textarea.style.height = 'auto';
        textarea.style.height = `${Math.max(textarea.scrollHeight, 50)}px`;
      };
      adjustHeight();
      textarea.addEventListener('input', adjustHeight);
      return () => textarea.removeEventListener('input', adjustHeight);
    }
  }, [question]);

  

  // autocompletions.tsx
return (
  <div className='flex flex-row relative'>
    <textarea
      ref={inputRef}
      value={question}
      onChange={handleInputChange}
      placeholder={placeholder}
      className={`border w-full ${
        theme === 'dark' 
          ? 'border-neutral-700 bg-transparent text-neutral-100 hover:bg-transparent' 
          : 'border-gray-200 bg-white text-gray-900 hover:bg-gray-100'
      } pl-3 pt-4 pr-10 focus:outline-none resize-none rounded-full overflow-hidden`}
      style={{
        minHeight: '30px',
        maxHeight: '60px',
        height: 'auto'
      }}
    
    />
    <button 
      type="submit" 
      className={`absolute right-3 top-4 rounded-full ${
        theme === 'dark' 
          ? 'text-neutral-300 hover:bg-neutral-700' 
          : 'text-blue-900 hover:bg-gray-100'
      } focus:outline-none`}
    >
      <Search size={24} />
    </button>
  </div>
);
};
