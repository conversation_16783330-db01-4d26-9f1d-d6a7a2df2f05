package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

// LoadEnvironment loads environment variables from .env file
func LoadEnvironment() error {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: Error loading .env file:", err)
		// Not returning error as .env file may not exist in production
	}

	// Log that environment variables were loaded
	log.Println("Environment variables loaded")
	return nil
}

// GetEnv gets an environment variable or returns a default value
func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// RequireEnv gets an environment variable and panics if it's not set
func RequireEnv(key string) string {
	value := os.Getenv(key)
	if value == "" {
		log.Fatalf("Required environment variable %s is not set", key)
	}
	return value
}
