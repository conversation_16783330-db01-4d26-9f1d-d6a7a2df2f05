package handlers

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	// Relative imports assuming we're in the same module
	"decodemed/ai/flashcards"
	"decodemed/models"
)

// FlashcardHandler handles flashcard-related API endpoints
type FlashcardHandler struct {
	DB *gorm.DB
}

// FlashcardReviewInput represents the input for a flashcard review
type FlashcardReviewInput struct {
	Grade        int       `json:"grade"`         // 0-5 scale (0: failed, 5: perfect)
	Time         time.Time `json:"time"`          // Review time
	ResponseTime float64   `json:"response_time"` // Time taken to respond in seconds
}

// FlashcardGenerateInput represents the input for flashcard generation
type FlashcardGenerateInput struct {
	UnitID uint   `json:"unit_id"`
	Type   string `json:"type"` // 'document' or 'youtube'
}

// ProjectStatusUpdateInput represents the input for updating project status
type ProjectStatusUpdateInput struct {
	FlashcardsGenerated bool `json:"flashcards_generated"`
}

// FlashcardWithTags is a wrapper for Flashcard that handles tags field conversion
type FlashcardWithTags struct {
	models.Flashcard
	TagsJSON string `gorm:"column:tags" json:"-"`
}

// TableName specifies the table name for FlashcardWithTags
func (FlashcardWithTags) TableName() string {
	return "flashcards"
}

// AfterFind converts TagsJSON to Tags slice after fetching from DB
func (f *FlashcardWithTags) AfterFind(tx *gorm.DB) error {
	if f.TagsJSON != "" {
		return json.Unmarshal([]byte(f.TagsJSON), &f.Tags)
	}
	return nil
}

// BeforeSave converts Tags slice to TagsJSON before saving to DB
func (f *FlashcardWithTags) BeforeSave(tx *gorm.DB) error {
	// Always ensure Tags is initialized to at least an empty array
	if f.Tags == nil {
		f.Tags = []string{}
	}

	// Convert Tags slice to JSON string
	tagsJSON, err := json.Marshal(f.Tags)
	if err != nil {
		return err
	}
	f.TagsJSON = string(tagsJSON)
	return nil
}

// NewFlashcardHandler creates a new flashcard handler
func NewFlashcardHandler(db *gorm.DB) *FlashcardHandler {
	return &FlashcardHandler{
		DB: db,
	}
}

// UpdateProjectFlashcardsStatus updates the FlashcardsGenerated field of a project
func (h *FlashcardHandler) UpdateProjectFlashcardsStatus(c *gin.Context) {
	projectID, err := strconv.ParseUint(c.Param("project_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	var input ProjectStatusUpdateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Update the project's flashcards_generated field
	result := h.DB.Model(&models.Project{}).Where("id = ?", projectID).Update("flashcards_generated", input.FlashcardsGenerated)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project status", "details": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":              "Project status updated successfully",
		"project_id":           projectID,
		"flashcards_generated": input.FlashcardsGenerated,
	})
}

// GenerateFlashcards generates flashcards for a unit
func (h *FlashcardHandler) GenerateFlashcards(c *gin.Context) {
	var input FlashcardGenerateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Log the request
	log.Printf("Generating flashcards for unit ID: %d, Type: %s", input.UnitID, input.Type)

	// Validate input type
	input.Type = strings.ToLower(input.Type)
	if input.Type != string(models.TypeDocument) && input.Type != string(models.TypeYouTube) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":       "Invalid input type",
			"type":        input.Type,
			"valid_types": []string{string(models.TypeDocument), string(models.TypeYouTube)},
		})
		return
	}

	// Retrieve the unit with eager loading of related data
	var unit models.Unit
	if err := h.DB.First(&unit, input.UnitID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found", "unit_id": input.UnitID})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error while fetching unit", "details": err.Error()})
		}
		return
	}

	// Log unit details for debugging
	log.Printf("Found unit: ID=%d, Title=%s, Type=%s, HasContent=%v, HasYoutubeSummary=%v",
		unit.ID, unit.Title, unit.Type, unit.Content != "", unit.YoutubeSummary != "")

	// If unit type is empty, try to determine it from content and from the requested type
	var typeUpdated bool
	if unit.Type == "" {
		typeUpdated = true

		// First, try to determine from content
		if unit.YoutubeSummary != "" {
			unit.Type = models.TypeYouTube
		} else if unit.Content != "" {
			unit.Type = models.TypeDocument
		} else {
			// If no content to determine type, use the requested type
			unit.Type = models.ContentType(input.Type)
		}

		// Update the unit type in the database
		if unit.Type != "" {
			if err := h.DB.Model(&unit).Update("type", unit.Type).Error; err != nil {
				log.Printf("Warning: Failed to update unit type: %v", err)
			}

			// Log the basis for the type determination
			var basis string
			if unit.YoutubeSummary != "" {
				basis = "YouTube summary"
			} else if unit.Content != "" {
				basis = "document content"
			} else {
				basis = "requested type"
			}

			log.Printf("Updated unit type to: %s based on %s", unit.Type, basis)
		}
	}

	// Now validate the content matches the type (rather than validating types match)
	var hasValidContent bool

	if unit.Type == models.TypeDocument {
		hasValidContent = unit.Content != ""
	} else if unit.Type == models.TypeYouTube {
		hasValidContent = unit.YoutubeSummary != ""
	}

	if !hasValidContent {
		contentType := ""
		if unit.Type == models.TypeDocument {
			contentType = "document content"
		} else if unit.Type == models.TypeYouTube {
			contentType = "YouTube summary"
		}

		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Unit does not have appropriate content for its type",
			"unit_id": unit.ID,
			"type":    unit.Type,
			"needs":   contentType,
		})
		return
	}

	// Only after determining and validating content, check if the requested type matches
	if !typeUpdated && models.ContentType(input.Type) != unit.Type {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":          "Unit type mismatch",
			"requested_type": input.Type,
			"actual_type":    unit.Type,
			"unit_id":        unit.ID,
		})
		return
	}

	// Determine content type and get content
	var content string
	var contentType string

	if unit.Type == models.TypeDocument {
		content = unit.Content
		contentType = "document"
	} else if unit.Type == models.TypeYouTube {
		content = unit.YoutubeSummary
		contentType = "youtube"
	}

	// Validate content is not empty (already checked above, but double-check)
	if strings.TrimSpace(content) == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Unit content is empty",
			"unit_id": input.UnitID,
			"type":    string(unit.Type),
		})
		return
	}

	title := unit.Title
	if strings.TrimSpace(title) == "" {
		title = "Untitled Unit"
	}

	// Log content details for debugging
	log.Printf("Generating flashcards for unit %d: Type=%s, Title=%s, ContentLength=%d",
		unit.ID, unit.Type, title, len(content))

	// Generate flashcards using AI
	aiFlashcards, err := flashcards.GenerateWithGemini(context.Background(), title, content, contentType)
	if err != nil {
		if strings.Contains(err.Error(), "GEMINI_API_KEY") {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "AI service configuration error", "details": "GEMINI_API_KEY not configured"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate flashcards", "details": err.Error()})
		}
		return
	}

	// Convert AI flashcards to database model and save
	var dbFlashcards []FlashcardWithTags
	now := time.Now()
	for _, card := range aiFlashcards {
		// Marshal tags as JSON
		tagsJSON, err := json.Marshal(card.Tags)
		if err != nil {
			log.Printf("Warning: Failed to marshal tags for flashcard: %v", err)
			tagsJSON = []byte("[]") // Default to empty array
		}

		dbFlashcard := FlashcardWithTags{
			Flashcard: models.Flashcard{
				UnitID:         input.UnitID,
				Question:       card.Question,
				Answer:         card.Answer,
				DifficultyRank: card.DifficultyRank,
				Tags:           card.Tags,
				LastReviewed:   &now,
				NextReview:     &now, // Set initial review time to now so cards show up immediately
				ReviewCount:    0,
				Stability:      1.0, // Initial stability
				Difficulty:     0.3, // Initial difficulty (moderate)
				ElapsedDays:    0,
				ScheduledDays:  0,
				Retrievability: 1.0, // Initial retrievability
			},
			TagsJSON: string(tagsJSON),
		}

		dbFlashcards = append(dbFlashcards, dbFlashcard)
	}

	// Save all flashcards to the database
	if err := h.DB.Create(&dbFlashcards).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save flashcards", "details": err.Error()})
		return
	}

	// Convert back to regular flashcards for the response
	var responseFlashcards []models.Flashcard
	for _, fc := range dbFlashcards {
		responseFlashcards = append(responseFlashcards, fc.Flashcard)
	}

	log.Printf("Successfully generated %d flashcards for unit ID: %d", len(dbFlashcards), input.UnitID)

	c.JSON(http.StatusOK, gin.H{
		"message":    "Flashcards generated successfully",
		"flashcards": responseFlashcards,
	})
}

// GetFlashcardsByUnit gets all flashcards for a unit
func (h *FlashcardHandler) GetFlashcardsByUnit(c *gin.Context) {
	unitID, err := strconv.ParseUint(c.Param("unit_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid unit ID"})
		return
	}

	var flashcardsWithTags []FlashcardWithTags
	if err := h.DB.Where("unit_id = ?", unitID).Find(&flashcardsWithTags).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve flashcards"})
		return
	}

	// Convert to response format
	var responseFlashcards []models.Flashcard
	for _, fc := range flashcardsWithTags {
		responseFlashcards = append(responseFlashcards, fc.Flashcard)
	}

	c.JSON(http.StatusOK, responseFlashcards)
}

// GetFlashcardsByProject gets all flashcards for a project
func (h *FlashcardHandler) GetFlashcardsByProject(c *gin.Context) {
	// Try to get project_id from path parameter first, then from query parameter
	projectID := c.Param("project_id")
	if projectID == "" {
		// Try from query parameter
		projectID = c.Query("project_id")
		if projectID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
			return
		}
	}

	log.Printf("Getting flashcards for project ID: %s", projectID)

	var flashcardsWithTags []FlashcardWithTags
	query := h.DB.Table("flashcards").
		Preload("Unit").
		Joins("JOIN units ON units.id = flashcards.unit_id").
		Where("units.project_id = ?", projectID)

	if err := query.Find(&flashcardsWithTags).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve project flashcards", "details": err.Error()})
		return
	}

	// Convert to response format
	var responseFlashcards []models.Flashcard
	for _, fc := range flashcardsWithTags {
		responseFlashcards = append(responseFlashcards, fc.Flashcard)
	}

	log.Printf("Found %d flashcards for project ID: %s", len(responseFlashcards), projectID)
	c.JSON(http.StatusOK, responseFlashcards)
}

// GetDueFlashcards gets all due flashcards for review
func (h *FlashcardHandler) GetDueFlashcards(c *gin.Context) {
	now := time.Now()
	projectID := c.Query("project_id")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
		return
	}

	log.Printf("Getting due flashcards for project ID: %s", projectID)

	// First check if the project exists
	var projectCount int64
	if err := h.DB.Model(&models.Project{}).Where("id = ?", projectID).Count(&projectCount).Error; err != nil {
		log.Printf("Error checking project existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to verify project existence",
			"details": err.Error(),
		})
		return
	}

	if projectCount == 0 {
		log.Printf("Project with ID %s not found", projectID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// Try a simpler query that just gets all flashcards for the project
	var allFlashcards []FlashcardWithTags
	if err := h.DB.Table("flashcards").
		Joins("JOIN units ON units.id = flashcards.unit_id").
		Where("units.project_id = ?", projectID).
		Find(&allFlashcards).Error; err != nil {
		log.Printf("Error retrieving all flashcards: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve flashcards",
			"details": err.Error(),
		})
		return
	}

	log.Printf("Retrieved %d flashcards total for project ID: %s", len(allFlashcards), projectID)

	// Filter for due flashcards in memory to avoid complex SQL
	var dueFlashcards []FlashcardWithTags
	for _, fc := range allFlashcards {
		// A flashcard is due if:
		// 1. It has a next_review date that's before or equal to now, or
		// 2. It has never been reviewed (review_count = 0)
		if (fc.NextReview != nil && !fc.NextReview.After(now)) ||
			fc.ReviewCount == 0 {
			// Try to parse tags JSON if needed
			if fc.Tags == nil && fc.TagsJSON != "" {
				var tags []string
				if err := json.Unmarshal([]byte(fc.TagsJSON), &tags); err == nil {
					fc.Tags = tags
				} else {
					log.Printf("Warning: Failed to parse tags JSON for flashcard %d: %v", fc.ID, err)
					fc.Tags = []string{} // Set to empty array on error
				}
			}
			dueFlashcards = append(dueFlashcards, fc)
		}
	}

	// If no flashcards are due, return empty array
	if len(dueFlashcards) == 0 {
		log.Printf("No due flashcards found for project ID: %s", projectID)
		c.JSON(http.StatusOK, []models.Flashcard{})
		return
	}

	// Convert to response format
	var responseFlashcards []models.Flashcard
	for _, fc := range dueFlashcards {
		responseFlashcards = append(responseFlashcards, fc.Flashcard)
	}

	log.Printf("Found %d due flashcards for project ID: %s", len(responseFlashcards), projectID)
	c.JSON(http.StatusOK, responseFlashcards)
}

// ReviewFlashcard updates a flashcard after review
func (h *FlashcardHandler) ReviewFlashcard(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid flashcard ID"})
		return
	}

	var input FlashcardReviewInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	// Validate grade
	if input.Grade < 0 || input.Grade > 5 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Grade must be between 0 and 5"})
		return
	}

	// Find the flashcard
	var flashcardWithTags FlashcardWithTags
	if err := h.DB.First(&flashcardWithTags, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Flashcard not found"})
		return
	}

	// Ensure Tags is initialized to avoid empty string JSON error
	if flashcardWithTags.Tags == nil {
		flashcardWithTags.Tags = []string{}
	}

	// Use review time or current time if not provided
	reviewTime := input.Time
	if reviewTime.IsZero() {
		reviewTime = time.Now()
	}

	// Create a review record
	review := models.FlashcardReview{
		FlashcardID:    uint(id),
		Grade:          input.Grade,
		ElapsedDays:    flashcardWithTags.ElapsedDays,
		Stability:      flashcardWithTags.Stability,
		Difficulty:     flashcardWithTags.Difficulty,
		Retrievability: flashcardWithTags.Retrievability,
		ReviewTime:     reviewTime,
		ResponseTime:   input.ResponseTime,
		Correct:        input.Grade >= 3,
	}

	// Log the review data for debugging
	log.Printf("Processing review for flashcard %d with grade %d", id, input.Grade)
	log.Printf("Current flashcard state: Tags=%v, TagsJSON=%s",
		flashcardWithTags.Tags, flashcardWithTags.TagsJSON)

	// Start a transaction
	tx := h.DB.Begin()

	// Save the review record
	if err := tx.Create(&review).Error; err != nil {
		tx.Rollback()
		log.Printf("Error creating review record: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save review"})
		return
	}

	// Update flashcard with FSRS
	aiFlashcard := flashcards.Flashcard{
		Question:       flashcardWithTags.Question,
		Answer:         flashcardWithTags.Answer,
		DifficultyRank: flashcardWithTags.DifficultyRank,
		LastReviewed:   flashcardWithTags.LastReviewed,
		NextReview:     flashcardWithTags.NextReview,
		ReviewCount:    flashcardWithTags.ReviewCount,
		Stability:      flashcardWithTags.Stability,
		Difficulty:     flashcardWithTags.Difficulty,
		ElapsedDays:    flashcardWithTags.ElapsedDays,
		ScheduledDays:  flashcardWithTags.ScheduledDays,
		Retrievability: flashcardWithTags.Retrievability,
	}

	flashcards.UpdateFlashcardWithFSRS(&aiFlashcard, input.Grade, reviewTime)

	// Update flashcard with new values
	flashcardWithTags.LastReviewed = aiFlashcard.LastReviewed
	flashcardWithTags.NextReview = aiFlashcard.NextReview
	flashcardWithTags.ReviewCount = aiFlashcard.ReviewCount
	flashcardWithTags.Stability = aiFlashcard.Stability
	flashcardWithTags.Difficulty = aiFlashcard.Difficulty
	flashcardWithTags.ElapsedDays = aiFlashcard.ElapsedDays
	flashcardWithTags.ScheduledDays = aiFlashcard.ScheduledDays
	flashcardWithTags.Retrievability = aiFlashcard.Retrievability

	// Log the flashcard state before saving
	marshaled, _ := json.Marshal(flashcardWithTags.Tags)
	log.Printf("Saving flashcard with Tags=%v, marshaled as %s",
		flashcardWithTags.Tags, string(marshaled))

	if err := tx.Save(&flashcardWithTags).Error; err != nil {
		tx.Rollback()
		log.Printf("Error updating flashcard: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update flashcard"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "Flashcard reviewed successfully",
		"flashcard":   flashcardWithTags.Flashcard,
		"next_review": flashcardWithTags.NextReview,
	})
}

// GetFlashcardStats gets statistics about flashcards
func (h *FlashcardHandler) GetFlashcardStats(c *gin.Context) {
	projectID := c.Query("project_id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
		return
	}

	// Base query to filter by project
	baseQuery := h.DB.Table("flashcards").
		Joins("JOIN units ON units.id = flashcards.unit_id").
		Where("units.project_id = ?", projectID)

	// Count total flashcards
	var totalCount int64
	baseQuery.Count(&totalCount)

	// Count due flashcards
	var dueCount int64
	baseQuery.Where("(next_review <= ? OR review_count = 0)", time.Now()).Count(&dueCount)

	// Get review counts by day for the last 7 days
	var dailyReviews []struct {
		Day   string
		Count int64
	}

	h.DB.Table("flashcard_reviews").
		Select("DATE(review_time) as day, COUNT(*) as count").
		Joins("JOIN flashcards ON flashcards.id = flashcard_reviews.flashcard_id").
		Joins("JOIN units ON units.id = flashcards.unit_id").
		Where("units.project_id = ?", projectID).
		Where("review_time >= CURRENT_DATE - INTERVAL '7 days'").
		Group("DATE(review_time)").
		Order("day").
		Scan(&dailyReviews)

	// Calculate mastery level (average retrievability of all cards)
	var avgRetrievability float64
	baseQuery.Select("COALESCE(AVG(retrievability), 0)").Row().Scan(&avgRetrievability)

	// Get review accuracy for the project
	var totalCorrect, totalReviews int64
	reviewQuery := h.DB.Table("flashcard_reviews").
		Joins("JOIN flashcards ON flashcards.id = flashcard_reviews.flashcard_id").
		Joins("JOIN units ON units.id = flashcards.unit_id").
		Where("units.project_id = ?", projectID)

	reviewQuery.Where("correct = ?", true).Count(&totalCorrect)
	reviewQuery.Count(&totalReviews)

	accuracy := 0.0
	if totalReviews > 0 {
		accuracy = float64(totalCorrect) / float64(totalReviews) * 100
	}

	stats := map[string]interface{}{
		"total_flashcards":    totalCount,
		"due_flashcards":      dueCount,
		"mastery_level":       avgRetrievability * 100, // as percentage
		"review_accuracy":     accuracy,                // as percentage
		"total_reviews":       totalReviews,
		"daily_review_counts": dailyReviews,
	}

	c.JSON(http.StatusOK, stats)
}

// GetFlashcardReviewHistory gets the review history for a flashcard
func (h *FlashcardHandler) GetFlashcardReviewHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid flashcard ID"})
		return
	}

	var reviews []models.FlashcardReview
	if err := h.DB.Where("flashcard_id = ?", id).Order("review_time desc").Find(&reviews).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve review history"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// Init initializes the flashcard handler and database schema
func (h *FlashcardHandler) Init() error {
	// Auto-migrate the flashcard models
	if err := h.DB.AutoMigrate(&models.Flashcard{}, &models.FlashcardReview{}); err != nil {
		return err
	}

	// Ensure the flashcard_with_tags view or table doesn't cause issues
	// This is a defensive measure to avoid the "relation flashcard_with_tags does not exist" error
	h.DB.Exec("DROP VIEW IF EXISTS flashcard_with_tags")

	// Log initialization success
	log.Println("Flashcard models successfully migrated")
	return nil
}
