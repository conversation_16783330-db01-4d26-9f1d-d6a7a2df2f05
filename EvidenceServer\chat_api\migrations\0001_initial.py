# Generated by Django 5.1.2 on 2024-10-13 01:56

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChatEntry',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_id', models.CharField(max_length=255)),
                ('question', models.TextField()),
                ('response', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('conversation_id', models.UUIDField(default=uuid.uuid4)),
                ('sources', models.JSONField(default=list)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
