"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useTheme } from "@/components/theme/theme-provider";
import {
  Menu,
  LayoutDashboard,
  Settings,
  CreditCard,
  Activity,
  BookOpen,
  Search,
  GraduationCap,
  Microscope,
  CodeSquare,
  DollarSign,
} from "lucide-react";
import { useLanguage } from "@/app/providers/language-provider";
import { usePathname } from "next/navigation";

interface LeftSidebarProps {
  isExpanded?: boolean;
  toggleSidebar?: () => void;
  onHide?: () => void;
}

export default function LeftSidebar({
  isExpanded: expandedProp,
  toggleSidebar: toggleSidebarProp,
  onHide,
}: LeftSidebarProps) {
  const { t } = useLanguage();
  const { theme } = useTheme();
  const pathname = usePathname();
  const [isExpanded, setIsExpanded] = useState(typeof expandedProp !== 'undefined' ? expandedProp : true);
  const [showSettings, setShowSettings] = useState(false);

  // Use the prop if provided, otherwise use internal state
  useEffect(() => {
    if (typeof expandedProp !== 'undefined') {
      setIsExpanded(expandedProp);
    }
  }, [expandedProp]);

  // Handle hide sidebar - call the onHide callback
  const handleHide = () => {
    if (onHide) {
      onHide();
    }
    // If toggleSidebar prop exists, call it too
    if (toggleSidebarProp) {
      toggleSidebarProp();
    } else {
      setIsExpanded(false);
    }
  };

  // Toggle settings visibility
  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };

  // Get the appropriate logo based on theme and sidebar state
  const getLogo = () => {
    if (isExpanded) {
      return theme === "dark" ? "/decodemed_text_dark.svg" : "/decodemed_text.svg";
    } else {
      return theme === "dark" ? "/decodemed_dark.svg" : "/decodemed.svg";
    }
  };

  return (
    <div
      className={`fixed top-0 left-0 h-screen flex-shrink-0 z-30 transition-all duration-300 ${
        isExpanded ? "w-64" : "w-16"
      } ${theme === "dark" ? "bg-[#080808] text-white border-[#121212]" : "bg-gray-100 text-neutral-800 border-neutral-200"}`}
    >
      <div className="h-full flex flex-col">
        <div className="flex items-center p-4">
          {isExpanded ? (
            <div className="flex items-center justify-between w-full">
              <Link href="/studio" className="text-xl font-bold" key="dashboard-logo">
                <Image src={getLogo()} alt="DecodeMed" width={150} height={30} />
              </Link>
              <button
                onClick={handleHide}
                className="p-1 rounded-md hover:bg-neutral-200 dark:hover:bg-neutral-700 focus:outline-none"
              >
                <Menu />
              </button>
            </div>
          ) : (
            <div className="flex flex-col items-center w-full">
              <Link href="/studio" key="collapsed-logo" className="mb-2">
                <Image src={getLogo()} alt="DecodeMed" width={28} height={28} />
              </Link>
              <button
                onClick={handleHide}
                className="p-1 rounded-md hover:bg-neutral-200 dark:hover:bg-neutral-700 focus:outline-none"
                key="toggle-sidebar-button"
              >
                <Menu />
              </button>
            </div>
          )}
        </div>

        <div className="flex-1 overflow-y-auto">
          <nav className="space-y-2 px-2">
            {/* Medical School Section */}
            <div className="relative">
              <Link
                href="/dashboard"
                className={`flex items-center w-full p-2 rounded-md ${
                  pathname === "/dashboard"
                    ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                    : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                }`}
                key="dashboard-nav"
              >
                <LayoutDashboard size={20} />
                {isExpanded && (
                  <div className="flex items-center justify-between w-full">
                    <span className="ml-3" key="dashboard-label">{t('left_sidebar.dashboard')}</span>
                  </div>
                )}
              </Link>
              <button
                className={`flex items-center w-full p-2 rounded-md`}
                key="medical-school-nav"
              >
                <GraduationCap size={20} />
                {isExpanded && (
                  <div className="flex items-center justify-between w-full">
                    <span className="ml-3" key="medical-school-label">{t('left_sidebar.medical_school')}</span>
                  </div>
                )}
              </button>
              
              {/* Medical School Dropdown Menu */}
              {isExpanded && (
                <div className="pl-8 mt-1 space-y-1">
                  {/* Decode Studio Link (renamed from Dashboard) */}
                  <Link
                    href="/studio"
                    className={`flex items-center p-2 rounded-md ${
                      pathname === "/studio"
                        ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                        : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                    }`}
                    key="decode-studio-nav"
                  >
                    <CodeSquare size={16} />
                    <span className="ml-2 text-sm">{t('left_sidebar.decode_studio')}</span>
                  </Link>
                </div>
              )}
            </div>

            {/* Medical Research Section */}
            <div className="relative">
              <button
                className={`flex items-center w-full p-2 rounded-md ${
                  pathname.includes("/research")
                    ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                    : ""
                }`}
                key="medical-research-nav"
              >
                <Microscope size={20} />
                {isExpanded && (
                  <div className="flex items-center justify-between w-full">
                    <span className="ml-3" key="medical-research-label">{t('left_sidebar.medical_research')}</span>
                  </div>
                )}
              </button>
              
              {/* Medical Research Dropdown Menu */}
              {isExpanded && (
                <div className="pl-8 mt-1 space-y-1">
                  <Link
                    href="/chat"
                    className={`flex items-center p-2 rounded-md ${
                      pathname === "/chat"
                        ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                        : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                    }`}
                    key="research-search-nav"
                  >
                    <Search size={16} />
                    <span className="ml-2 text-sm">{t('left_sidebar.research.search')}</span>
                  </Link>
                  <Link
                    href="/evidence"
                    className={`flex items-center p-2 rounded-md ${
                      pathname === "/evidence"
                        ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                        : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                    }`}
                    key="evidence-nav"
                  >
                    <BookOpen size={16} />
                    <span className="ml-2 text-sm">{t('left_sidebar.research.evidence')}</span>
                  </Link>
                </div>
              )}
            </div>
          </nav>
        </div>
        
        {/* Settings section moved to bottom */}
        <div className="mt-auto  border-neutral-200 dark:border-neutral-700 pt-2 px-2 pb-4">
          <div className="relative">
            <button
              onClick={toggleSettings}
              className={`flex items-center w-full p-2 rounded-md ${
                pathname.includes("/subscriptions")
                  ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                  : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
              }`}
              key="settings-nav"
            >
              <Settings size={20} />
              {isExpanded && (
                <div className="flex items-center justify-between w-full">
                  <span className="ml-3" key="settings-label">{t('left_sidebar.settings')}</span>
                  <span className="text-xs">
                    {showSettings ? "▲" : "▼"}
                  </span>
                </div>
              )}
            </button>
            
            {/* Subscription Dropdown Menu */}
            {isExpanded && showSettings && (
              <div className="pl-8 mt-1 space-y-1">
                <Link
                  href="/subscriptions/subscription-management"
                  className={`flex items-center p-2 rounded-md ${
                    pathname === "/subscriptions/subscription-management"
                      ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                      : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                  }`}
                  key="subscription-management-nav"
                >
                  <CreditCard size={16} />
                  <span className="ml-2 text-sm">{t('left_sidebar.subscription_management')}</span>
                </Link>
                <Link
                  href="/subscriptions/pricing"
                  className={`flex items-center p-2 rounded-md ${
                    pathname === "/subscriptions/pricing"
                      ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                      : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                  }`}
                  key="pricing-nav"
                >
                  <DollarSign size={16} />
                  <span className="ml-2 text-sm">{t('left_sidebar.pricing')}</span>
                </Link>
                <Link
                  href="/subscriptions/usage"
                  className={`flex items-center p-2 rounded-md ${
                    pathname === "/subscriptions/usage"
                      ? "bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200"
                      : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
                  }`}
                  key="usage-nav"
                >
                  <Activity size={16} />
                  <span className="ml-2 text-sm">{t('left_sidebar.usage')}</span>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
