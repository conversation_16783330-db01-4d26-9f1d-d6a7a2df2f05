'use client'; // Add this if you are using Next.js 14 and this component needs to be a client component

import React, { useState, useEffect } from 'react';

const levels = [
  { id: 'clinical-practice-guideline', name: ' Guidelines', y: 0, height: 40 },
  { id: 'meta-analysis', name: 'Meta-analysis', y: 30, height: 40 },
  { id: 'systematic-review', name: 'Systematic Review', y: 60, height: 30 },
  { id: 'randomized-controlled-trial', name: 'Randomized Controlled Trial', y: 90, height: 40 },
  { id: 'cohort-study', name: 'Cohort Study', y: 120, height: 40 },
  { id: 'case-control-study', name: 'Case-Control Study', y: 150, height: 40 },
  { id: 'cross-sectional-study', name: 'Cross-sectional Study', y: 180, height: 40 },
  { id: 'case-series-or-case-report', name: 'Case Series / Case Report', y: 210, height: 40 },
  { id: 'narrative-review-expert-opinion-or-editorial-opinion', name: 'Narrative Review/ Expert Opinion / Editorial Opinion', y: 240, height: 40 },
  { id: 'in-vitro-study-or-in-vivo-study', name: 'In vitro / In vivo study', y: 270, height: 40 },
];

const levelColors = [
  '#D1D5DB', '#D1D5DB', '#D1D5DB', // Changed to Gray 300
  '#D1D5DB', '#D1D5DB', '#D1D5DB', '#D1D5DB', '#D1D5DB', 
  '#D1D5DB', '#D1D5DB' // Gray 300
];

const activeColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1',
  '#66CC99', '#FFA07A', '#20B2AA', '#9370DB', '#F0E68C',
  '#DDA0DD', '#98FB98'
];

interface EvidencePyramidProps {
  studyDesign: string; // Specify the type for studyDesign
  isIcon?: boolean;    // Optional prop
}

export const EvidencePyramid: React.FC<EvidencePyramidProps> = ({ studyDesign, isIcon = false }) => {
  const [activeLevel, setActiveLevel] = useState<string | null>(null); // Specify the type for activeLevel

  useEffect(() => {

    const normalizedStudyDesign = studyDesign.trim().toLowerCase();

    const matchedLevel = levels.find(level => {
      const normalizedLevelName = level.name.toLowerCase();
      
      const isInVitroOrInVivo = normalizedStudyDesign.includes('in vitro') || normalizedStudyDesign.includes('in vivo');
      const isNarrativeReviewOrExpertOpinion = 
        normalizedStudyDesign.includes('narrative review') || 
        normalizedStudyDesign.includes('expert opinion') || 
        normalizedStudyDesign.includes('editorial opinion');
      const isCaseSeriesOrCaseReport = 
        normalizedStudyDesign.includes('case series') || 
        normalizedStudyDesign.includes('case report');
      
      return (isInVitroOrInVivo && (normalizedLevelName.includes('in vitro') || normalizedLevelName.includes('in vivo'))) ||
             (isNarrativeReviewOrExpertOpinion && normalizedLevelName.includes('narrative review')) ||
             (isCaseSeriesOrCaseReport && normalizedLevelName.includes('case series')) ||
             normalizedStudyDesign.includes(normalizedLevelName) ||
             normalizedLevelName.includes(normalizedStudyDesign);
    });

    setActiveLevel(matchedLevel ? matchedLevel.id : null);
  }, [studyDesign]);

  const svgContent = (
    <svg width={isIcon ? "24" : "300"} height={isIcon ? "24" : "300"} viewBox="0 0 200 400" aria-labelledby="ebm-pyramid-title" role="img">
      <title id="ebm-pyramid-title">Evidence-Based Medicine Pyramid</title>
      {levels.map((level, index) => (
        <g key={level.id}>
          <polygon
            points={`${100 - (level.y / 2)} ${level.y}, ${100 + (level.y / 2)} ${level.y}, ${100 + ((level.y + level.height) / 2)} ${level.y + level.height}, ${100 - ((level.y + level.height) / 2)} ${level.y + level.height}`}
            fill={activeLevel === level.id ? activeColors[index] : levelColors[index]}
            stroke="#FFFFFF"
            strokeWidth="1"
          />
          {!isIcon && (
            <text
              x="100"
              y={level.y + level.height / 2}
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="8"
              fontWeight="bold"
              fill={activeLevel === level.id ? 'white' : 'black'}
            >
              {level.name}
            </text>
          )}
        </g>
      ))}
    </svg>
  );

  if (isIcon) {
    return svgContent;
  }

  return (
    <div className="flex flex-col items-center p-2">
      {svgContent}
    </div>
  );
};
