"use client"

import { createContext, useContext, useEffect, useState, useCallback } from "react"
import type { Locale, Translations } from '@/app/utils/types/language'

// Helper function to get nested values from an object using a dot notation string
const getNestedValue = (obj: Record<string, unknown> | null, key: string): string | undefined => {
  if (!obj) return undefined;

  const result = key.split('.').reduce((acc: unknown, k: string): unknown => {
    // Check if acc is an indexable object and key exists
    if (typeof acc === 'object' && acc !== null && k in acc) {
      // Access the value safely
      return (acc as Record<string, unknown>)[k];
    }
    // If not indexable or key doesn't exist, stop traversal
    return undefined;
  }, obj); // Start with the initial object

  // Return the result only if it's a string
  return typeof result === 'string' ? result : undefined;
}

type LanguageProviderProps = {
  children: React.ReactNode
}

type LanguageContextType = {
  language: Locale
  setLanguage: (language: Locale) => void
  t: (key: string, params?: Record<string, string | number>) => string // Translation function with parameters
  loading: boolean // Add loading state
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Locale>('en') // Default to 'en'
  const [translations, setTranslations] = useState<Translations | null>(null)
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    const savedLanguage = localStorage.getItem('language') as Locale
    if (savedLanguage) {
      setLanguage(savedLanguage)
    } else {
      // Detect browser language only if nothing is saved
      let detectedLanguage: Locale = 'en' // Default to English

      // Try navigator.languages first (more robust)
      if (navigator.languages && navigator.languages.length > 0) {
        for (const lang of navigator.languages) {
          const primaryLang = lang.split('-')[0] as Locale
          if (primaryLang === 'es' || primaryLang === 'pt') {
            detectedLanguage = primaryLang
            break // Found a supported language
          }
        }
      }
      // Fallback to navigator.language if navigator.languages didn't yield a supported language
      else if (navigator.language) {
        const primaryLang = navigator.language.split('-')[0] as Locale
        if (primaryLang === 'es') {
          detectedLanguage = 'es'
        } else if (primaryLang === 'pt') {
          detectedLanguage = 'pt'
        }
      }
      // Set the determined language
      setLanguage(detectedLanguage)
    }
  }, []) // Run only once on mount

  useEffect(() => {
    if (!mounted) return

    const loadTranslations = async () => {
      setLoading(true)
      try {
        const translationsModule = await import(`@/locales/${language}.json`)
        setTranslations(translationsModule.default)
      } catch (error) {
        console.error(`Failed to load translations for ${language}:`, error)
        // Fallback to English if loading fails
        if (language !== 'en') {
          const fallbackTranslationsModule = await import(`@/locales/en.json`)
          setTranslations(fallbackTranslationsModule.default)
        } else {
          setTranslations({}) // Set empty if English fails too
        }
      }
      setLoading(false)
    }

    loadTranslations()
  }, [language, mounted])

  const handleLanguageChange = useCallback((newLanguage: Locale) => {
    if (!mounted) return
    setLanguage(newLanguage)
    localStorage.setItem('language', newLanguage)
  }, [mounted])

  // Translation function
  const t = useCallback((key: string, params?: Record<string, string | number>): string => {
    if (loading || !translations) {
      return key // Return key or a loading indicator while loading
    }
    let translatedValue = getNestedValue(translations, key)
    if (translatedValue && params) {
      // Replace parameters in the translation string
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        translatedValue = translatedValue?.replace(`{${paramKey}}`, String(paramValue))
      })
    }
    return translatedValue || key // Return key if translation not found
  }, [translations, loading])

  // Avoid rendering children until the initial language and translations are potentially loaded
  if (!mounted) {
    return null
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleLanguageChange, t, loading }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
} 