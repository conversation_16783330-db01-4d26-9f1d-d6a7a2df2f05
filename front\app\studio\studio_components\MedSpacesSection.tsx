import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChevronDown, Edit, Trash2, Loader2 } from 'lucide-react';
// Remove the useLanguage import since we'll get t from props
import { Project } from '@/app/studio/page';

export interface MedSpace {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  projects?: Project[];
  isExpanded?: boolean;
  thumbnailStatus?: 'loading' | 'ready' | 'error';
}

interface MedSpacesSectionProps {
  t: (key: string) => string;
  theme: string;
  medSpaces: MedSpace[];
  isCreatingMedSpace: boolean;
  setIsCreatingMedSpace: (value: boolean) => void;
  newMedSpaceName: string;
  setNewMedSpaceName: (value: string) => void;
  newMedSpaceDescription: string;
  setNewMedSpaceDescription: (value: string) => void;
  activeMedSpace: MedSpace | null;
  isShowingAllProjects: boolean;
  editingMedSpaceId: number | null;
  setEditingMedSpaceId: (value: number | null) => void;
  medSpaceWithOpenMenu: number | null;
  setMedSpaceWithOpenMenu: (value: number | null) => void;
  confirmationMedSpaceId: number | null;
  loading: boolean;
  createMedSpace: () => Promise<void>;
  updateMedSpace: (id: number, name: string, description: string) => Promise<void>;
  setMedSpaces: React.Dispatch<React.SetStateAction<MedSpace[]>>;
  handleMedSpaceClick: (medSpace: MedSpace) => void;
  showAllProjects: () => void;
  handleMedSpaceDropdownClick: (medSpace: MedSpace) => void;
  handleMedSpaceDelete: (id: number) => void;
  confirmMedSpaceDelete: () => Promise<void>;
  cancelMedSpaceDelete: () => void;
}

const MedSpacesSection: React.FC<MedSpacesSectionProps> = ({
  theme,
  medSpaces,
  isCreatingMedSpace,
  setIsCreatingMedSpace,
  newMedSpaceName,
  setNewMedSpaceName,
  newMedSpaceDescription,
  setNewMedSpaceDescription,
  activeMedSpace,
  isShowingAllProjects,
  editingMedSpaceId,
  setEditingMedSpaceId,
  medSpaceWithOpenMenu,
  setMedSpaceWithOpenMenu,
  confirmationMedSpaceId,
  loading,
  createMedSpace,
  updateMedSpace,
  setMedSpaces,
  handleMedSpaceClick,
  showAllProjects,
  handleMedSpaceDropdownClick,
  handleMedSpaceDelete,
  confirmMedSpaceDelete,
  cancelMedSpaceDelete,
  t,
}) => {
  return (
    <div className="mb-8">
      
      {/* MedSpaces Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
        {/* "All Projects" Option */}
        <div 
          onClick={showAllProjects}
          className={`flex flex-col items-center justify-center p-4 rounded-3xl cursor-pointer border transition-all ${
            isShowingAllProjects
              ? (theme === 'dark' ? 'bg-neutral-900 border-transparent' : 'bg-gray-100 border-transparent')
              : (theme === 'dark' ? 'bg-[#1e1e1e] border-transparent hover:bg-neutral-800/50' : 'bg-gray-100/70 border-gray-200 hover:bg-gray-100')
          }`}
        >
          <Folder 
            size={28} 
            className={isShowingAllProjects 
              ? (theme === 'dark' ? 'text-gray-400 mb-2' : 'text-gray-600 mb-2')
              : `mb-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`
            } 
          />
          <span className={`text-sm font-medium text-center ${
            isShowingAllProjects
              ? (theme === 'dark' ? 'text-gray-300' : 'text-gray-900')
              : (theme === 'dark' ? 'text-gray-300' : 'text-gray-700')
          }`}>
            {t('common.projectCreation.allProjects')}
          </span>
        </div>
        
        {/* MedSpaces */}
        {medSpaces.map(medSpace => (
          <div key={medSpace.id} className="relative">
            {/* MedSpace Item */}
            <div 
              onClick={() => handleMedSpaceClick(medSpace)}
              className={`flex flex-col items-center justify-center p-4 rounded-3xl cursor-pointer border transition-all ${
                activeMedSpace?.id === medSpace.id
                  ? (theme === 'dark' ? 'bg-neutral-900 border-transparent' : 'bg-gray-100 border-transparent')
                  : (theme === 'dark' ? 'bg-[#1e1e1e] border-transparent hover:bg-neutral-800/50' : 'bg-gray-100/70 border-gray-200 hover:bg-gray-100')
              }`}
            >
              <Folder 
                size={28} 
                className={activeMedSpace?.id === medSpace.id 
                  ? (theme === 'dark' ? 'text-gray-400 mb-2' : 'text-gray-600 mb-2')
                  : `mb-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`
                } 
              />
              
              {editingMedSpaceId === medSpace.id ? (
                <input
                  type="text"
                  value={medSpace.name}
                  onChange={(e) => {
                    setMedSpaces(prevMedSpaces => 
                      prevMedSpaces.map(ms => 
                        ms.id === medSpace.id ? { ...ms, name: e.target.value } : ms
                      )
                    );
                  }}
                  className={`w-full p-1 rounded text-center text-sm ${
                    theme === 'dark' 
                      ? 'bg-gray-800/30 text-white border-gray-600' 
                      : 'bg-gray-100 text-gray-900 border-gray-300'
                  } border focus:outline-none focus:ring-1 focus:ring-[#333333]`}
                  autoFocus
                  onBlur={() => {
                    if (medSpace.name.trim()) {
                      updateMedSpace(medSpace.id, medSpace.name, medSpace.description || '');
                    } else {
                      setMedSpaces(prevMedSpaces => 
                        prevMedSpaces.map(ms => 
                          ms.id === medSpace.id ? { ...ms, name: 'Unnamed MedSpace' } : ms
                        )
                      );
                      setEditingMedSpaceId(null);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      if (medSpace.name.trim()) {
                        updateMedSpace(medSpace.id, medSpace.name, medSpace.description || '');
                      } else {
                        setMedSpaces(prevMedSpaces => 
                          prevMedSpaces.map(ms => 
                            ms.id === medSpace.id ? { ...ms, name: 'Unnamed MedSpace' } : ms
                          )
                        );
                        setEditingMedSpaceId(null);
                      }
                    } else if (e.key === 'Escape') {
                      setEditingMedSpaceId(null);
                    }
                  }}
                />
              ) : (
                <>
                  <span className={`text-sm font-medium text-center truncate max-w-full ${
                    activeMedSpace?.id === medSpace.id
                      ? (theme === 'dark' ? 'text-gray-400' : 'text-gray-900')
                      : (theme === 'dark' ? 'text-gray-300' : 'text-gray-700')
                  }`} title={medSpace.name}>{medSpace.name}</span>
                  
                  <span className={`text-xs mt-1 ${
                    theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    {medSpace.projects?.length || 0} projects
                  </span>
                </>
              )}
              
              {/* Actions Menu Button */}
              {editingMedSpaceId !== medSpace.id && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMedSpaceDropdownClick(medSpace);
                  }}
                  className={`absolute top-1 right-1 p-1 rounded-full ${
                    theme === 'dark' 
                      ? 'hover:bg-gray-700 text-gray-400' 
                      : 'hover:bg-gray-200 text-gray-500'
                  }`}
                >
                  <ChevronDown size={14} />
                </button>
              )}
            </div>
            
            {/* Actions Menu Dropdown */}
            {medSpaceWithOpenMenu === medSpace.id && (
              <div className={`absolute right-0 top-full mt-1 w-36 py-1 rounded-md shadow-lg z-10 ${
                theme === 'dark' ? 'bg-gray-800 text-white border border-gray-700' : 'bg-white text-gray-800 border border-gray-200'
              }`}>
                <button
                  onClick={() => {
                    setEditingMedSpaceId(medSpace.id);
                    setMedSpaceWithOpenMenu(null);
                  }}
                  className={`block w-full text-left px-4 py-2 text-sm ${
                    theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center">
                    <Edit size={14} className="mr-2" />
                    Edit
                  </div>
                </button>
                <button
                  onClick={() => {
                    handleMedSpaceDelete(medSpace.id);
                    setMedSpaceWithOpenMenu(null);
                  }}
                  className={`block w-full text-left px-4 py-2 text-sm ${
                    theme === 'dark' ? 'hover:bg-gray-700 text-red-400' : 'hover:bg-gray-100 text-red-500'
                  }`}
                >
                  <div className="flex items-center">
                    <Trash2 size={14} className="mr-2" />
                    Delete
                  </div>
                </button>
              </div>
            )}
            
            {/* Confirmation Dialog */}
            {confirmationMedSpaceId === medSpace.id && (
              <div className={`absolute z-10 left-0 right-0 p-3 mt-2 rounded-lg shadow-lg ${
                theme === 'dark' ? 'bg-gray-800 border border-red-800' : 'bg-white border border-red-200'
              }`}>
                <p className={`text-sm mb-2 ${
                  theme === 'dark' ? 'text-red-300' : 'text-red-700'
                }`}>
                  Delete this MedSpace? Projects will not be deleted.
                </p>
                <div className="flex justify-end space-x-2">
                  <button
                    onClick={cancelMedSpaceDelete}
                    className={`px-2 py-1 text-xs rounded ${
                      theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                    }`}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmMedSpaceDelete}
                    className={`px-2 py-1 text-xs rounded ${
                      theme === 'dark' ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-red-500 hover:bg-red-600 text-white'
                    }`}
                  >
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
        
        {/* Create New MedSpace Button */}
        <div className="relative h-full">
          {!isCreatingMedSpace ? (
            <div 
              onClick={() => setIsCreatingMedSpace(true)}
              className={`flex flex-col items-center justify-center p-4 rounded-3xl cursor-pointer border transition-all h-full
                ${theme === 'dark' ? 'bg-[#1e1e1e] border-transparent hover:bg-neutral-800/50' : 'bg-gray-100/70 border-gray-200 hover:bg-gray-100'}`}
            >
              <FolderPlus 
                size={28} 
                className={`mb-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} 
              />
              <span className={`text-sm font-medium text-center ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                MedSpace
              </span>
            </div>
          ) : (
            <div className={`p-3 rounded-lg border transition-all h-full flex flex-col justify-between ${
              theme === 'dark' 
                ? 'bg-[#1e1e1e] border-transparent' 
                : 'bg-gray-100 border-gray-300'
            }`}>
              <input
                type="text"
                value={newMedSpaceName}
                onChange={(e) => setNewMedSpaceName(e.target.value)}
                className={`block w-full p-1.5 rounded-md text-sm mb-1 ${
                  theme === 'dark' 
                    ? 'bg-neutral-800 border-neutral-700 text-neutral-200 placeholder-neutral-400 focus:border-neutral-500' 
                    : 'bg-white border-neutral-300 text-neutral-900 placeholder-neutral-500 focus:border-neutral-400'
                } border focus:ring-0 focus:outline-none`}
                placeholder="MedSpace name"
                autoFocus
              />
              <textarea
                value={newMedSpaceDescription}
                onChange={(e) => setNewMedSpaceDescription(e.target.value)}
                className={`block w-full p-1.5 rounded-md text-sm ${
                  theme === 'dark' 
                    ? 'bg-neutral-800 border-neutral-700 text-neutral-200 placeholder-neutral-400 focus:border-neutral-500' 
                    : 'bg-white border-neutral-300 text-neutral-900 placeholder-neutral-500 focus:border-neutral-400'
                } border focus:ring-0 focus:outline-none flex-grow resize-none`}
                placeholder="Description (opt.)"
                rows={1}
              />
              <div className="flex justify-between space-x-1 mt-1">
                <button
                  onClick={() => {
                    setIsCreatingMedSpace(false);
                    setNewMedSpaceName('');
                    setNewMedSpaceDescription('');
                  }}
                  className={`px-2 py-1 rounded text-xs ${
                    theme === 'dark' 
                      ? 'bg-neutral-700 hover:bg-neutral-600 text-neutral-200' 
                      : 'bg-neutral-200 hover:bg-neutral-300 text-neutral-700'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={createMedSpace}
                  disabled={!newMedSpaceName.trim() || loading}
                  className={`px-2 py-1 rounded text-xs ${
                    theme === 'dark' 
                      ? 'bg-neutral-600 hover:bg-neutral-500 text-neutral-100 disabled:bg-neutral-700 disabled:text-neutral-400'
                      : 'bg-neutral-500 hover:bg-neutral-600 text-white disabled:bg-neutral-300 disabled:text-neutral-500'
                  }`}
                >
                  {loading ? (
                    <span className="flex items-center">
                      <Loader2 size={12} className="animate-spin mr-1" />
                      Creating...
                    </span>
                  ) : 'Create'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MedSpacesSection; 