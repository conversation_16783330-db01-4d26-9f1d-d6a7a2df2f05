# subscriptions/revenue_cat/RevenueCat_views.py

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ..models import UserSubscription
from ..middleware import AuthenticationMiddleware
import requests
import os

class RevenueCatSubscriptionStatus(APIView):
    def get(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, 
                          status=status.HTTP_401_UNAUTHORIZED)

        try:
            # Extract and verify token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = middleware.verify_token_sync(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')

            if not user_id:
                return Response({'error': 'Invalid token'}, 
                              status=status.HTTP_401_UNAUTHORIZED)

            # Get subscription status from database
            subscription = UserSubscription.objects.get(user_id=user_id)
            
            return Response({
                'subscription_type': subscription.subscription_type,
                'status': subscription.subscription_status,
                'will_cancel': subscription.will_cancel,
                'subscription_end_date': subscription.subscription_end_date,
                'provider': subscription.subscription_provider
            })

        except UserSubscription.DoesNotExist:
            return Response({
                'subscription_type': 'free trial',
                'status': 'inactive',
                'will_cancel': False,
                'subscription_end_date': None,
                'provider': 'none'
            })
        except Exception as e:
            return Response({'error': str(e)}, status=400)

class VerifyRevenueCatReceipt(APIView):
    def post(self, request):
        auth_header = request.headers.get('Authorization', '')
        if not auth_header:
            return Response({'error': 'No authorization token provided'}, 
                          status=status.HTTP_401_UNAUTHORIZED)

        try:
            # Extract and verify token
            auth_token = auth_header.replace('Bearer ', '')
            middleware = AuthenticationMiddleware(None)
            claims = middleware.verify_token_sync(auth_token)
            user_id = claims.get('sub') or claims.get('user.id')

            if not user_id:
                return Response({'error': 'Invalid token'}, 
                              status=status.HTTP_401_UNAUTHORIZED)

            # Get receipt data from request
            receipt_data = request.data.get('receipt')
            if not receipt_data:
                return Response({'error': 'No receipt data provided'}, 
                              status=400)

            # Verify receipt with RevenueCat API
            revenuecat_api_key = os.getenv('REVENUECAT_API_KEY')
            headers = {
                'Authorization': f'Bearer {revenuecat_api_key}',
                'Content-Type': 'application/json'
            }
            
            # Make request to RevenueCat API
            response = requests.post(
                'https://api.revenuecat.com/v1/receipts',
                headers=headers,
                json={
                    'app_user_id': user_id,
                    'fetch_token': receipt_data
                }
            )

            if response.status_code == 200:
                return Response({'status': 'success'})
            else:
                return Response({'error': 'Failed to verify receipt'}, 
                              status=400)

        except Exception as e:
            return Response({'error': str(e)}, status=400)