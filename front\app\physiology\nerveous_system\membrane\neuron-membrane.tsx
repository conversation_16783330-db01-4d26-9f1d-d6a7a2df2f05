"use client"

import { motion } from "framer-motion"

interface NeuronMembraneProps {
  progress: number
  onSelectPart?: (part: { name: string; description: string }) => void
}

export default function NeuronMembrane({ progress, onSelectPart }: NeuronMembraneProps) {
  // Calculate the current state based on progress
  const getPhaseState = () => {
    if (progress < 20) {
      return "resting"
    } else if (progress < 40) {
      return "depolarization"
    } else if (progress < 70) {
      return "repolarization"
    } else if (progress < 85) {
      return "hyperpolarization"
    } else {
      return "recovery"
    }
  }

  const currentState = getPhaseState()

  // Calculate sodium channel state
  const sodiumChannelOpen = currentState === "depolarization"

  // Calculate potassium channel state
  const potassiumChannelOpen = currentState === "repolarization" || currentState === "hyperpolarization"

  // Calculate ion positions and animations
  const getSodiumIons = () => {
    const ions = []

    // Outside ions (always present)
    for (let i = 0; i < 12; i++) {
      ions.push({
        id: `na-out-${i}`,
        x: 40 + (i % 6) * 80,
        y: 80 + Math.floor(i / 6) * 40,
        moving: sodiumChannelOpen && i < 6,
      })
    }

    // Inside ions (appear during depolarization)
    if (currentState !== "resting") {
      for (let i = 0; i < 6; i++) {
        ions.push({
          id: `na-in-${i}`,
          x: 40 + i * 80,
          y: 280,
          moving: false,
        })
      }
    }

    return ions
  }

  const getPotassiumIons = () => {
    const ions = []

    // Inside ions (always present)
    for (let i = 0; i < 12; i++) {
      ions.push({
        id: `k-in-${i}`,
        x: 80 + (i % 6) * 80,
        y: 320 - Math.floor(i / 6) * 40,
        moving: potassiumChannelOpen && i < 6,
      })
    }

    // Outside ions (appear during repolarization)
    if (currentState === "repolarization" || currentState === "hyperpolarization" || currentState === "recovery") {
      for (let i = 0; i < 6; i++) {
        ions.push({
          id: `k-out-${i}`,
          x: 80 + i * 80,
          y: 120,
          moving: false,
        })
      }
    }

    return ions
  }

  const sodiumIons = getSodiumIons()
  const potassiumIons = getPotassiumIons()

  return (
    <div className="bg-[hsl(240_10%_3.9%)] rounded-lg p-4 h-[600px] w-full ">
      <h2 className="text-lg font-medium mb-2 text-white">Neuron Membrane</h2>
      <div className="relative h-[520px] w-full bg-[hsl(240_10%_3.9%)] rounded-lg overflow-hidden">
        {/* Membrane */}
        <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-16 bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 flex items-center justify-around">
          {/* Sodium channels */}
          <motion.div
            className="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center relative cursor-pointer"
            onClick={() => onSelectPart?.({ name: 'Sodium Channel', description: 'Sodium channels allow Na+ ions to enter the neuron, causing depolarization.' })}
            animate={{
              backgroundColor: sodiumChannelOpen ? "#1d4ed8" : "#1e3a8a",
            }}
          >
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-blue-500"
              animate={{
                scale: sodiumChannelOpen ? [1, 1.2, 1] : 1,
                opacity: sodiumChannelOpen ? [1, 0.7, 1] : 1,
              }}
              transition={{
                duration: 1,
                repeat: sodiumChannelOpen ? Number.POSITIVE_INFINITY : 0,
                repeatType: "loop",
              }}
            />
            <span className="text-blue-300 font-bold text-sm">Na+</span>
          </motion.div>

          {/* Potassium channels */}
          <motion.div
            className="w-12 h-12 bg-purple-900 rounded-full flex items-center justify-center relative cursor-pointer"
            onClick={() => onSelectPart?.({ name: 'Potassium Channel', description: 'Potassium channels allow K+ ions to exit the neuron, aiding in repolarization.' })}
            animate={{
              backgroundColor: potassiumChannelOpen ? "#7e22ce" : "#581c87",
            }}
          >
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-purple-500"
              animate={{
                scale: potassiumChannelOpen ? [1, 1.2, 1] : 1,
                opacity: potassiumChannelOpen ? [1, 0.7, 1] : 1,
              }}
              transition={{
                duration: 1,
                repeat: potassiumChannelOpen ? Number.POSITIVE_INFINITY : 0,
                repeatType: "loop",
              }}
            />
            <span className="text-purple-300 font-bold text-sm">K+</span>
          </motion.div>

          {/* Sodium-Potassium pump */}
          <motion.div
            className="w-16 h-16 bg-green-900 rounded-full flex items-center justify-center cursor-pointer"
            onClick={() => onSelectPart?.({ name: 'Sodium-Potassium Pump', description: 'The Na+/K+ pump actively transports 3 Na+ ions out and 2 K+ ions in, restoring resting potential.' })}
            animate={{
              rotate: currentState === "recovery" ? 360 : 0,
            }}
            transition={{
              duration: 2,
              repeat: currentState === "recovery" ? Number.POSITIVE_INFINITY : 0,
              repeatType: "loop",
            }}
          >
            <span className="text-green-300 font-bold text-xs text-center">
              Na+/K+
              <br />
              Pump
            </span>
          </motion.div>
        </div>

        {/* Extracellular label */}
        <div className="absolute top-4 left-4 text-sm font-medium text-gray-300">Extracellular Fluid</div>

        {/* Intracellular label */}
        <div className="absolute bottom-4 left-4 text-sm font-medium text-gray-300">Intracellular Fluid</div>

        {/* Sodium ions */}
        {sodiumIons.map((ion) => (
          <motion.div
            key={ion.id}
            className="absolute w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-xs"
            initial={{ x: ion.x, y: ion.y }}
            animate={
              ion.moving
                ? {
                    y: [ion.y, 200, 280],
                    x: ion.x,
                    opacity: 1,
                  }
                : {
                    x: ion.x,
                    y: ion.y,
                    opacity: 1,
                  }
            }
            transition={
              ion.moving
                ? {
                    duration: 1,
                    times: [0, 0.5, 1],
                    ease: "easeInOut",
                  }
                : {}
            }
          >
            Na+
          </motion.div>
        ))}

        {/* Potassium ions */}
        {potassiumIons.map((ion) => (
          <motion.div
            key={ion.id}
            className="absolute w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold text-xs"
            initial={{ x: ion.x, y: ion.y }}
            animate={
              ion.moving
                ? {
                    y: [ion.y, 200, 120],
                    x: ion.x,
                    opacity: 1,
                  }
                : {
                    x: ion.x,
                    y: ion.y,
                    opacity: 1,
                  }
            }
            transition={
              ion.moving
                ? {
                    duration: 1,
                    times: [0, 0.5, 1],
                    ease: "easeInOut",
                  }
                : {}
            }
          >
            K+
          </motion.div>
        ))}

        {/* Legend */}
        <div className="absolute bottom-4 right-4 bg-gray-800 bg-opacity-80 p-2 rounded-md text-xs text-gray-300">
          <div className="flex items-center mb-1">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
            <span>Sodium (Na+)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-purple-500 mr-1"></div>
            <span>Potassium (K+)</span>
          </div>
        </div>
      </div>
      {/* Side-panel handled by parent via onSelectPart */}
    </div>
  )
}
