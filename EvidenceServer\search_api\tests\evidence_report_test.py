import unittest
from unittest.mock import patch, AsyncMock
import asyncio
import os
from dotenv import load_dotenv
from search_api.ai_models.evidence_report import generate_evidence_report, client

class TestEvidenceReport(unittest.TestCase):
    def setUp(self):
        # Load environment variables
        load_dotenv()
        
        # Test data with a more realistic medical abstract
        self.test_articles = [
            {
                'title': 'Effect of Vitamin D on COVID-19 Outcomes',
                'abstract': '''
                Background: This randomized controlled trial investigated the effect of vitamin D 
                supplementation on COVID-19 severity. Methods: 200 COVID-19 patients were randomly 
                assigned to receive either vitamin D (n=100) or placebo (n=100) for 14 days. 
                Primary outcomes included hospitalization duration and disease severity scores. 
                Results: Vitamin D group showed significantly reduced hospitalization time 
                (p<0.001) and lower severity scores. No adverse effects were reported. 
                Conclusion: Vitamin D supplementation may be beneficial in COVID-19 treatment.
                '''
            }
        ]

        self.mock_response_content = """| Parameter | Assessment |
|-----------|------------|
| 🔬 Study Design | Randomized Controlled Trial with double-blind methodology |
| ⚖️ Risk of Bias | Low risk of bias with proper randomization methods |

Parameters assessed: 2 out of 9."""

    @patch('search_api.ai_models.evidence_report.client')
    def test_generate_evidence_report_mock(self, mock_client):
        # Setup mock
        mock_completion = AsyncMock()
        mock_completion.choices = [
            AsyncMock(message=AsyncMock(content=self.mock_response_content))
        ]
        mock_client.chat.completions.create = AsyncMock(return_value=mock_completion)

        # Run test
        result = asyncio.run(generate_evidence_report(self.test_articles))

        # Assertions
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['title'], 'Effect of Vitamin D on COVID-19 Outcomes')
        self.assertIn('Parameter', result[0]['report'])
        self.assertIn('Study Design', result[0]['report'])
        
        # Verify mock was called correctly
        mock_client.chat.completions.create.assert_called()

    def test_generate_evidence_report_integration(self):
        # Verify API key is available
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            self.skipTest("OpenAI API key not found in environment variables")

        # Real API call test
        result = asyncio.run(generate_evidence_report([self.test_articles[0]]))
        
        # Print the actual response
        print("\n=== OpenAI API Response ===")
        print(f"Title: {result[0]['title']}")
        print("Evidence Report:")
        print(result[0]['report'])
        print("=========================\n")
        
        # Basic assertions
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['title'], 'Effect of Vitamin D on COVID-19 Outcomes')
        self.assertIn('Parameter', result[0]['report'])
        self.assertIn('Assessment', result[0]['report'])

if __name__ == '__main__':
    unittest.main()