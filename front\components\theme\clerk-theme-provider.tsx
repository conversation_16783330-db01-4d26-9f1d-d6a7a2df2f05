"use client"
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { useTheme } from "@/components/theme/theme-provider";
import { dark } from '@clerk/themes';

export default function ClerkThemeProvider({ children }: { children: React.ReactNode }) {
  const { theme } = useTheme();
  
  return (
    <ClerkProvider
      appearance={{
        baseTheme: theme === "dark" ? dark : undefined,
        variables: theme === "dark" ? {
          colorBackground: '#111111'  // bg-gray-900
        } : undefined
      }}
    >
      {children}
    </ClerkProvider>
  );
}