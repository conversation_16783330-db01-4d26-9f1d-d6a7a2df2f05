package database

import (
	"decodemed/models"
	"log"

	"gorm.io/gorm"
)

// runMigrations handles all database migrations
func runMigrations(db *gorm.DB) error {
	log.Println("Running database migrations...")

	// First, auto-migrate all models to create the tables
	if err := db.AutoMigrate(
		&models.User{},
		&models.MedSpace{},
		&models.Project{},
		&models.Unit{},
		&models.Mindmap{},
		&models.Quiz{},
		&models.Flashcard{},
		&models.FlashcardReview{},
		// Stripe-related models - now using combined model
		&models.StripeCustomerSubscription{},
		&models.SubscriptionUsage{},
	); err != nil {
		return err
	}

	// After tables exist, check and add any missing columns
	if !db.Migrator().HasColumn(&models.Project{}, "youtube_units_generated") {
		log.Println("Adding youtube_units_generated column to projects table...")
		if err := db.Exec("ALTER TABLE projects ADD COLUMN IF NOT EXISTS youtube_units_generated BOOLEAN DEFAULT false").Error; err != nil {
			log.Printf("Failed to add youtube_units_generated column: %v", err)
			return err
		}
		log.Println("Successfully added youtube_units_generated column")
	}

	// Check and add fileUrl column to units table if it doesn't exist
	if !db.Migrator().HasColumn(&models.Unit{}, "file_url") {
		if err := db.Migrator().AddColumn(&models.Unit{}, "file_url"); err != nil {
			log.Printf("Failed to add file_url column: %v", err)
		} else {
			log.Println("Added file_url column to units table")
		}
	}

	return nil
}

// initDB initializes the database with test data
func initDB(db *gorm.DB) error {
	log.Println("Initializing database...")

	// Handle column renaming from free_usage_count to free_projects_count
	if db.Migrator().HasColumn(&models.User{}, "free_usage_count") && !db.Migrator().HasColumn(&models.User{}, "free_projects_count") {
		log.Println("Renaming free_usage_count column to free_projects_count...")
		// PostgreSQL doesn't support direct column renaming in migrations with GORM, so we need to:
		// 1. Add the new column
		if err := db.Migrator().AddColumn(&models.User{}, "free_projects_count"); err != nil {
			log.Printf("Error adding free_projects_count column: %v", err)
			return err
		}

		// 2. Copy data from old column to new
		if err := db.Exec("UPDATE users SET free_projects_count = free_usage_count").Error; err != nil {
			log.Printf("Error copying data to free_projects_count: %v", err)
			return err
		}

		// We'll keep the old column for now to avoid breaking existing code
		// Later we can remove it in a separate migration
	} else if !db.Migrator().HasColumn(&models.User{}, "free_projects_count") {
		// If neither column exists, add the new one
		log.Println("Adding free_projects_count column to users table...")
		if err := db.Migrator().AddColumn(&models.User{}, "free_projects_count"); err != nil {
			log.Printf("Error adding free_projects_count column: %v", err)
			return err
		}
		// Set default value for existing users
		if err := db.Model(&models.User{}).Where("free_projects_count IS NULL").Update("free_projects_count", 0).Error; err != nil {
			log.Printf("Error setting default free_projects_count: %v", err)
			return err
		}
	}

	// Sync YouTube projects with existing units
	updateYouTubeProjectFlags(db)

	// Check if we have a test user
	var count int64
	db.Model(&models.User{}).Count(&count)
	if count == 0 {
		log.Println("Creating test user...")
		testUser := models.User{
			ClerkID: "test_clerk_id",
			Email:   "<EMAIL>",
		}
		if err := db.Create(&testUser).Error; err != nil {
			log.Printf("Error creating test user: %v", err)
			return err
		}
		log.Printf("Test user created with ID: %d", testUser.ID)
	}

	// Check if we have a debug user with ID 1
	var debugUser models.User
	result := db.First(&debugUser, 1)
	if result.Error != nil {
		log.Println("Creating debug user with ID 1...")
		debugUser := models.User{
			Model: gorm.Model{
				ID: 1,
			},
			ClerkID: "debug_user_id",
			Email:   "<EMAIL>",
		}
		if err := db.Create(&debugUser).Error; err != nil {
			log.Printf("Error creating debug user: %v", err)
			return err
		}
		log.Printf("Debug user created with ID: %d", debugUser.ID)
	}

	return nil
}

// updateYouTubeProjectFlags updates the youtube_units_generated flag for projects that have units
func updateYouTubeProjectFlags(db *gorm.DB) {
	log.Println("Updating YouTube project flags based on existing units...")

	var youtubeProjects []models.Project
	if err := db.Where("type = ?", models.TypeYouTube).Find(&youtubeProjects).Error; err != nil {
		log.Printf("Error fetching YouTube projects: %v", err)
		return
	}

	log.Printf("Found %d YouTube projects to check", len(youtubeProjects))

	for _, project := range youtubeProjects {
		var unitCount int64
		if err := db.Model(&models.Unit{}).Where("project_id = ?", project.ID).Count(&unitCount).Error; err != nil {
			log.Printf("Error counting units for project %d: %v", project.ID, err)
			continue
		}

		if unitCount > 0 && !project.YouTubeUnitsGenerated {
			if err := db.Exec("UPDATE projects SET youtube_units_generated = true WHERE id = ?", project.ID).Error; err != nil {
				log.Printf("Error updating YouTubeUnitsGenerated for project %d: %v", project.ID, err)
			} else {
				log.Printf("Successfully updated YouTubeUnitsGenerated for project %d", project.ID)
			}
		}
	}
}
