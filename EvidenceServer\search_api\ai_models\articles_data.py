import aiohttp
from typing import List, Optional
from .question_query import generate_search_query

# Add this global variable at the top of the file
api_request_count = 0

async def get_pubmed_results(query: str, 
                             max_results: int = 100,  # Changed default to 100
                             start_year: Optional[int] = None, 
                             end_year: Optional[int] = None, 
                             cursor: str = '*', 
                             min_citations: Optional[int] = None,  # Removed license_type parameter
                             max_citations: Optional[int] = None):
    global api_request_count
    search_url = "https://www.ebi.ac.uk/europepmc/webservices/rest/search"
    
    # Construct the query with default filters
    full_query = query

    # Default filters for open access and commercial license
    full_query += " AND OPEN_ACCESS:Y"
    full_query += ' AND (LICENSE:"cc by" OR LICENSE:"cc by-nc" OR LICENSE:"cc by-nc-nd" OR LICENSE:"cc by-nc-sa")'
    
    # publication date
    if start_year and end_year:
        full_query += f" AND (FIRST_PDATE:[{start_year}-01-01 TO {end_year}-12-31])"
    elif start_year:
        full_query += f" AND (FIRST_PDATE:[{start_year}-01-01 TO 3000])"
    elif end_year:
        full_query += f" AND (FIRST_PDATE:[1900 TO {end_year}-12-31])"
    
    # citation count filter
    if min_citations is not None or max_citations is not None:
        citation_range = []
        if min_citations is not None:
            citation_range.append(f"[{min_citations} TO")
        else:
            citation_range.append("[0 TO")
        
        if max_citations is not None:
            citation_range.append(f"{max_citations}]")
        else:
            citation_range.append("*]")
        
        full_query += f" AND CITED:{' '.join(citation_range)}"

    # max results 
    # pagination
    search_params = {
        'query': full_query,
        'format': 'json',
        'pageSize': max_results,  
        'resultType': 'core',
        'cursorMark': cursor,
        
    }
    
    
    # search Europe PMC
    async with aiohttp.ClientSession() as session:
        async with session.get(search_url, params=search_params) as response:
            api_request_count += 1  # Increment the counter
            if response.status != 200:
                return None, f'Failed to search Europe PMC: Status {response.status}', None, 0
            data = await response.json()
    
    articles = parse_articles(data)
    total_results = int(data.get('hitCount', 0))
    next_cursor = data.get('nextCursorMark', None)
    
    return articles, None, next_cursor, total_results

# get pdf link
def get_pdf_link(result):
    if 'fullTextUrlList' in result:
        for url in result['fullTextUrlList']['fullTextUrl']:
            if url['documentStyle'] == 'pdf' and url['availability'] == 'Open access':
                return url['url']
    return ''

# parse articles
def parse_articles(data):
    articles = []
    for result in data.get('resultList', {}).get('result', []):
        doi = result.get('doi', '')
        pubmed_id = result.get('pmid', '')
        
        # Extract journal information
        journal_info = result.get('journalInfo', {})
        journal_title = journal_info.get('journal', {}).get('title', 'N/A')

        articles.append({
            'id': result.get('id', ''),
            'title': result.get('title', ''),
            'authors': [author.get('fullName', '') for author in result.get('authorList', {}).get('author', [])],
            'abstract': result.get('abstractText', ''),
            'journal_title': journal_title,
            'publication_date': result.get('firstPublicationDate', '')[:4],
            'doi': doi,
            'doi_link': f"https://doi.org/{doi}" if doi else f"https://pubmed.ncbi.nlm.nih.gov/{pubmed_id}",
            'pdf_link': get_pdf_link(result),
            'is_open_access': result.get('isOpenAccess', 'N') == 'Y',
            'citation_count': result.get('citedByCount', 0),
            'license_type': result.get('license', '')
        })
    return articles







