package handlers

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"decodemed/ai/flashcards"
	"decodemed/ai/mindmap"
	"decodemed/api/handlers/async"
	"decodemed/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// YouTubeAutoGenService handles automation of content generation for YouTube videos
type YouTubeAutoGenService struct {
	DB *gorm.DB
}

// NewYouTubeAutoGenService creates a new instance of the YouTube auto-generation service
func NewYouTubeAutoGenService(db *gorm.DB) *YouTubeAutoGenService {
	return &YouTubeAutoGenService{
		DB: db,
	}
}

// TriggerYouTubeAutoContentGeneration automatically generates all content types for a YouTube project
// This triggers generation of quizzes, mindmaps, and flashcards in parallel
func (s *YouTubeAutoGenService) TriggerYouTubeAutoContentGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering all content generation for YouTube project %d", projectID)

	// Verify project exists - but don't check the YouTubeUnitsGenerated flag
	// since we're only calling this after generating units
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for YouTube auto content generation: %v", err)
		return
	}

	// Force update the units generated flag to ensure it's set
	if !project.YouTubeUnitsGenerated {
		log.Printf("Project %d YouTubeUnitsGenerated flag is false, but we're being called after generation. Forcing update.", projectID)
		if err := s.DB.Model(&models.Project{}).Where("id = ?", projectID).Update("youtube_units_generated", true).Error; err != nil {
			log.Printf("Warning: Failed to update YouTubeUnitsGenerated flag: %v", err)
		}
	}

	// Verify we have actual units to work with
	var unitCount int64
	if err := s.DB.Model(&models.Unit{}).Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Count(&unitCount).Error; err != nil {
		log.Printf("Error checking units count: %v", err)
		return
	}

	if unitCount == 0 {
		log.Printf("No YouTube units found for project %d, cannot auto-generate content", projectID)
		return
	}

	log.Printf("Found %d YouTube units for project %d, proceeding with auto-generation", unitCount, projectID)

	// Start parallel generation of different content types
	var wg sync.WaitGroup

	// Start quiz generation
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.TriggerYouTubeQuizGeneration(projectID, userID)
	}()

	// Start mindmap generation
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.TriggerYouTubeMindmapGeneration(projectID, userID)
	}()

	// Start flashcard generation
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.TriggerYouTubeFlashcardGeneration(projectID, userID)
	}()

	// Optional: Wait for all to complete (if we want to do something after)
	go func() {
		wg.Wait()
		log.Printf("All content generation completed for YouTube project %d", projectID)
	}()
}

// TriggerYouTubeQuizGeneration automatically generates quizzes for a YouTube project
func (s *YouTubeAutoGenService) TriggerYouTubeQuizGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering quiz generation for YouTube project %d", projectID)

	// Check if units exist for this project
	var unitCount int64
	if err := s.DB.Model(&models.Unit{}).Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Count(&unitCount).Error; err != nil {
		log.Printf("Error checking YouTube units for project %d: %v", projectID, err)
		return
	}

	if unitCount == 0 {
		log.Printf("No YouTube units found for project %d, cannot auto-generate quizzes", projectID)
		return
	}

	// Verify project exists and check if quizzes are already generated
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for YouTube auto quiz generation: %v", err)
		return
	}

	log.Printf("Found YouTube project %d for auto-generation. Units count: %d, Quizzes generated: %t",
		projectID, unitCount, project.QuizzesGenerated)

	// Skip if quizzes are already generated
	if project.QuizzesGenerated {
		log.Printf("Quizzes already generated for YouTube project %d, skipping auto-generation", projectID)
		return
	}

	// Create job ID and initial status
	jobID := uuid.New().String()
	jobStatus := &async.JobStatus{
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
	}

	async.JobMutex.Lock()
	async.JobStatuses[jobID] = jobStatus
	async.JobMutex.Unlock()

	// Broadcast initial status via WebSocket
	async.BroadcastQuizUpdate(projectID, "pending", 0)
	log.Printf("Created job %s for auto-generating quizzes for YouTube project %d", jobID, projectID)

	// Start async processing
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in YouTube auto quiz generation: %v", r)
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Internal server error"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastQuizUpdate(projectID, "failed", 0)
			}
		}()

		// Update job status
		async.JobMutex.Lock()
		jobStatus.Status = "processing"
		jobStatus.Progress = 5
		async.JobMutex.Unlock()

		// Broadcast progress via WebSocket
		async.BroadcastQuizUpdate(projectID, "processing", 5)

		// Query to get all units for the project
		var units []models.Unit
		if err := s.DB.Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Find(&units).Error; err != nil {
			log.Printf("Error fetching YouTube units for project %d: %v", projectID, err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to fetch YouTube units"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastQuizUpdate(projectID, "failed", 0)
			return
		}

		// Update progress based on number of units
		totalUnits := len(units)
		if totalUnits == 0 {
			log.Printf("No YouTube units found for project %d", projectID)
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Error = "No YouTube units found"
			async.JobMutex.Unlock()

			// Broadcast completion via WebSocket
			async.BroadcastQuizUpdate(projectID, "completed", 100)
			return
		}

		log.Printf("Auto-generating quizzes for %d YouTube units in project %d", totalUnits, projectID)

		// Generate quizzes for each unit
		for i, unit := range units {
			log.Printf("Auto-generating quiz for YouTube unit %d (%d/%d)", unit.Model.ID, i+1, totalUnits)

			// Delete existing quiz if it exists
			if err := s.DB.Where("unit_id = ?", unit.Model.ID).Delete(&models.Quiz{}).Error; err != nil {
				log.Printf("Warning: Failed to delete existing quiz for YouTube unit %d: %v", unit.Model.ID, err)
				// Continue with generation even if delete fails
			}

			// Create new quiz using the common GenerateQuizContent function from quizzes.go
			questions := GenerateQuizContent(unit)
			quiz := models.Quiz{
				UnitID:       unit.Model.ID,
				Questions:    questions,
				MaxScore:     100,
				PassingScore: 70,
			}

			log.Printf("Generated quiz questions for YouTube unit %d (JSON length: %d)",
				unit.Model.ID, len(quiz.Questions))

			// Save quiz to database
			if err := s.DB.Create(&quiz).Error; err != nil {
				log.Printf("Failed to save quiz for YouTube unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Update progress
			progress := int((float64(i+1) / float64(totalUnits)) * 100)
			async.JobMutex.Lock()
			jobStatus.Progress = progress
			jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket every 10% or for the last unit
			if (i+1)%maxInt(1, totalUnits/10) == 0 || i == totalUnits-1 {
				async.BroadcastQuizUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
			}
		}

		// Update job status to complete
		async.JobMutex.Lock()
		jobStatus.Status = "completed"
		jobStatus.Progress = 100
		async.JobMutex.Unlock()

		// Update project to mark quizzes as generated
		if err := s.DB.Model(&models.Project{}).Where("id = ?", projectID).Update("quizzes_generated", true).Error; err != nil {
			log.Printf("Failed to update project quizzes_generated status: %v", err)
		} else {
			log.Printf("Successfully marked YouTube project %d as having quizzes generated", projectID)
		}

		// Broadcast completion via WebSocket
		async.BroadcastQuizUpdate(projectID, "completed", 100)
		log.Printf("Auto-generation of quizzes for YouTube project %d completed successfully", projectID)
	}()
}

// TriggerYouTubeMindmapGeneration automatically generates mindmaps for a YouTube project
func (s *YouTubeAutoGenService) TriggerYouTubeMindmapGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering mindmap generation for YouTube project %d", projectID)

	// Check if units exist for this project
	var unitCount int64
	if err := s.DB.Model(&models.Unit{}).Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Count(&unitCount).Error; err != nil {
		log.Printf("Error checking YouTube units for project %d: %v", projectID, err)
		return
	}

	if unitCount == 0 {
		log.Printf("No YouTube units found for project %d, cannot auto-generate mindmaps", projectID)
		return
	}

	// Verify project exists
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for YouTube auto mindmap generation: %v", err)
		return
	}

	log.Printf("Found YouTube project %d for auto-generation. Units count: %d", projectID, unitCount)

	// Check if mindmaps are already generated using direct database query
	var mindmapsCount int64
	if err := s.DB.Model(&models.Mindmap{}).
		Joins("JOIN units ON mindmaps.unit_id = units.id").
		Where("units.project_id = ? AND units.type = ?", projectID, models.TypeYouTube).
		Count(&mindmapsCount).Error; err != nil {
		log.Printf("Error checking existing mindmaps: %v", err)
	} else if mindmapsCount > 0 {
		log.Printf("Mindmaps already exist for YouTube project %d (found %d), skipping auto-generation", projectID, mindmapsCount)
		return
	}

	// Create job ID and initial status
	jobID := uuid.New().String()
	jobStatus := &async.JobStatus{
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
	}

	async.JobMutex.Lock()
	async.JobStatuses[jobID] = jobStatus
	async.JobMutex.Unlock()

	// Broadcast initial status via WebSocket
	async.BroadcastMindmapUpdate(projectID, "pending", 0)
	log.Printf("Created job %s for auto-generating mindmaps for YouTube project %d", jobID, projectID)

	// Start async processing
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in YouTube auto mindmap generation: %v", r)
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Internal server error"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastMindmapUpdate(projectID, "failed", 0)
			}
		}()

		// Update job status
		async.JobMutex.Lock()
		jobStatus.Status = "processing"
		jobStatus.Progress = 5
		async.JobMutex.Unlock()

		// Broadcast progress via WebSocket
		async.BroadcastMindmapUpdate(projectID, "processing", 5)

		// Query to get all units for the project
		var units []models.Unit
		if err := s.DB.Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Find(&units).Error; err != nil {
			log.Printf("Error fetching YouTube units for project %d: %v", projectID, err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to fetch YouTube units"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastMindmapUpdate(projectID, "failed", 0)
			return
		}

		// Update progress based on number of units
		totalUnits := len(units)
		if totalUnits == 0 {
			log.Printf("No YouTube units found for project %d", projectID)
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Error = "No YouTube units found"
			async.JobMutex.Unlock()

			// Broadcast completion via WebSocket
			async.BroadcastMindmapUpdate(projectID, "completed", 100)
			return
		}

		log.Printf("Auto-generating mindmaps for %d YouTube units in project %d", totalUnits, projectID)

		// Initialize mindmap generator
		generator, err := mindmap.NewGenerator()
		if err != nil {
			log.Printf("Failed to create mindmap generator: %v", err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to initialize mindmap generator"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastMindmapUpdate(projectID, "failed", 0)
			return
		}

		// Generate mindmaps for each unit
		for i, unit := range units {
			log.Printf("Auto-generating mindmap for YouTube unit %d (%d/%d)", unit.Model.ID, i+1, totalUnits)

			// Delete existing mindmap if it exists
			if err := s.DB.Where("unit_id = ?", unit.Model.ID).Delete(&models.Mindmap{}).Error; err != nil {
				log.Printf("Warning: Failed to delete existing mindmap for YouTube unit %d: %v", unit.Model.ID, err)
				// Continue with generation even if delete fails
			}

			// For YouTube units, use the YouTube summary
			content := unit.YoutubeSummary
			if content == "" {
				content = unit.Content // Fallback to content if YouTube summary is empty
			}

			// Generate mindmap content
			ctx := context.Background()
			mindmapData, err := generator.GenerateFromContent(ctx, content, "")
			if err != nil {
				log.Printf("Failed to generate mindmap for YouTube unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Create new mindmap
			mindmapModel := models.Mindmap{
				UnitID: unit.Model.ID,
				Data:   mindmapData,
			}

			// Save mindmap to database
			if err := s.DB.Create(&mindmapModel).Error; err != nil {
				log.Printf("Failed to save mindmap for YouTube unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Update progress
			progress := int((float64(i+1) / float64(totalUnits)) * 100)
			async.JobMutex.Lock()
			jobStatus.Progress = progress
			jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket every 10% or for the last unit
			if (i+1)%maxInt(1, totalUnits/10) == 0 || i == totalUnits-1 {
				async.BroadcastMindmapUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
			}
		}

		// Update job status to complete
		async.JobMutex.Lock()
		jobStatus.Status = "completed"
		jobStatus.Progress = 100
		async.JobMutex.Unlock()

		// Broadcast completion via WebSocket
		async.BroadcastMindmapUpdate(projectID, "completed", 100)

		// Log completion
		log.Printf("Auto-generation of mindmaps for YouTube project %d completed successfully", projectID)
	}()
}

// TriggerYouTubeFlashcardGeneration automatically generates flashcards for a YouTube project
func (s *YouTubeAutoGenService) TriggerYouTubeFlashcardGeneration(projectID uint, userID interface{}) {
	log.Printf("Auto-triggering flashcard generation for YouTube project %d", projectID)

	// Check if units exist for this project
	var unitCount int64
	if err := s.DB.Model(&models.Unit{}).Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Count(&unitCount).Error; err != nil {
		log.Printf("Error checking YouTube units for project %d: %v", projectID, err)
		return
	}

	if unitCount == 0 {
		log.Printf("No YouTube units found for project %d, cannot auto-generate flashcards", projectID)
		return
	}

	// Verify project exists
	var project models.Project
	if err := s.DB.Where("id = ?", projectID).First(&project).Error; err != nil {
		log.Printf("Error fetching project for YouTube auto flashcard generation: %v", err)
		return
	}

	log.Printf("Found YouTube project %d for auto-generation. Units count: %d", projectID, unitCount)

	// Check if flashcards are already generated using direct database query
	var flashcardsCount int64
	if err := s.DB.Model(&models.Flashcard{}).
		Joins("JOIN units ON flashcards.unit_id = units.id").
		Where("units.project_id = ? AND units.type = ?", projectID, models.TypeYouTube).
		Count(&flashcardsCount).Error; err != nil {
		log.Printf("Error checking existing flashcards: %v", err)
	} else if flashcardsCount > 0 {
		log.Printf("Flashcards already exist for YouTube project %d (found %d), skipping auto-generation", projectID, flashcardsCount)
		return
	}

	// Create job ID and initial status
	jobID := uuid.New().String()
	jobStatus := &async.JobStatus{
		Status:    "pending",
		Progress:  0,
		CreatedAt: time.Now(),
	}

	async.JobMutex.Lock()
	async.JobStatuses[jobID] = jobStatus
	async.JobMutex.Unlock()

	// Broadcast initial status via WebSocket
	async.BroadcastFlashcardUpdate(projectID, "pending", 0)
	log.Printf("Created job %s for auto-generating flashcards for YouTube project %d", jobID, projectID)

	// Start async processing
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in YouTube auto flashcard generation: %v", r)
				async.JobMutex.Lock()
				jobStatus.Status = "failed"
				jobStatus.Error = "Internal server error"
				async.JobMutex.Unlock()

				// Broadcast failure via WebSocket
				async.BroadcastFlashcardUpdate(projectID, "failed", 0)
			}
		}()

		// Update job status
		async.JobMutex.Lock()
		jobStatus.Status = "processing"
		jobStatus.Progress = 5
		async.JobMutex.Unlock()

		// Broadcast progress via WebSocket
		async.BroadcastFlashcardUpdate(projectID, "processing", 5)

		// Query to get all units for the project
		var units []models.Unit
		if err := s.DB.Where("project_id = ? AND type = ?", projectID, models.TypeYouTube).Find(&units).Error; err != nil {
			log.Printf("Error fetching YouTube units for project %d: %v", projectID, err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to fetch YouTube units"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastFlashcardUpdate(projectID, "failed", 0)
			return
		}

		// Update progress based on number of units
		totalUnits := len(units)
		if totalUnits == 0 {
			log.Printf("No YouTube units found for project %d", projectID)
			async.JobMutex.Lock()
			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Error = "No YouTube units found"
			async.JobMutex.Unlock()

			// Broadcast completion via WebSocket
			async.BroadcastFlashcardUpdate(projectID, "completed", 100)
			return
		}

		log.Printf("Auto-generating flashcards for %d YouTube units in project %d", totalUnits, projectID)

		// Initialize flashcard generator
		generator, err := flashcards.NewFlashcardGenerator()
		if err != nil {
			log.Printf("Failed to create flashcard generator: %v", err)
			async.JobMutex.Lock()
			jobStatus.Status = "failed"
			jobStatus.Error = "Failed to initialize flashcard generator"
			async.JobMutex.Unlock()

			// Broadcast failure via WebSocket
			async.BroadcastFlashcardUpdate(projectID, "failed", 0)
			return
		}

		// Generate flashcards for each unit
		for i, unit := range units {
			log.Printf("Auto-generating flashcards for YouTube unit %d (%d/%d)", unit.Model.ID, i+1, totalUnits)

			// Delete existing flashcards if they exist
			if err := s.DB.Where("unit_id = ?", unit.Model.ID).Delete(&models.Flashcard{}).Error; err != nil {
				log.Printf("Warning: Failed to delete existing flashcards for YouTube unit %d: %v", unit.Model.ID, err)
				// Continue with generation even if delete fails
			}

			// Use YouTube summary if available
			content := unit.YoutubeSummary
			if content == "" {
				content = unit.Content // Fallback to content if YouTube summary is empty
			}

			// Generate flashcard content
			ctx := context.Background()
			flashcardItems, err := generator.GenerateFlashcardsFromContent(ctx, unit.Title, content, "video")
			if err != nil {
				log.Printf("Failed to generate flashcards for YouTube unit %d: %v", unit.Model.ID, err)
				continue
			}

			// Save each flashcard to database
			for _, item := range flashcardItems {
				flashcard := models.Flashcard{
					UnitID:         unit.Model.ID,
					Question:       item.Question,
					Answer:         item.Answer,
					DifficultyRank: item.DifficultyRank,
				}

				if err := s.DB.Create(&flashcard).Error; err != nil {
					log.Printf("Failed to save flashcard for YouTube unit %d: %v", unit.Model.ID, err)
					continue
				}
			}

			// Update progress
			progress := int((float64(i+1) / float64(totalUnits)) * 100)
			async.JobMutex.Lock()
			jobStatus.Progress = progress
			jobStatus.Status = fmt.Sprintf("processing: %d/%d units", i+1, totalUnits)
			async.JobMutex.Unlock()

			// Broadcast progress via WebSocket every 10% or for the last unit
			if (i+1)%maxInt(1, totalUnits/10) == 0 || i == totalUnits-1 {
				async.BroadcastFlashcardUpdate(projectID, fmt.Sprintf("processing: %d/%d units", i+1, totalUnits), progress)
			}
		}

		// Update job status to complete
		async.JobMutex.Lock()
		jobStatus.Status = "completed"
		jobStatus.Progress = 100
		async.JobMutex.Unlock()

		// Broadcast completion via WebSocket
		async.BroadcastFlashcardUpdate(projectID, "completed", 100)

		// Log completion
		log.Printf("Auto-generation of flashcards for YouTube project %d completed successfully", projectID)
	}()
}

// Define global service instance for access from other files
var youtubeAutoGenServiceInstance *YouTubeAutoGenService

// InitYouTubeAutoGenService initializes the global service instance
func InitYouTubeAutoGenService(db *gorm.DB) {
	youtubeAutoGenServiceInstance = NewYouTubeAutoGenService(db)
	log.Printf("YouTubeAutoGenService initialized with database: %v", db != nil)
}

// GetYouTubeAutoGenService returns the global service instance
func GetYouTubeAutoGenService() *YouTubeAutoGenService {
	return youtubeAutoGenServiceInstance
}
