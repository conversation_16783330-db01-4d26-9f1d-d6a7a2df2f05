'use client'
import React from 'react'
import { HeroHeader } from '@/containers/main/hero5-header'
import { MainThemeProvider, useMainTheme } from '@/components/theme/main-theme-provider'

const ReturnPolicyContent = () => {
  const { theme } = useMainTheme()
  return (
    <div className={`min-h-screen items-start text-left bg-background ${theme === 'dark' ? 'dark' : ''}`}>
      <HeroHeader />
      {/* Return Policy Section */}
      <section className="pt-40 pb-20 bg-neutral-100 dark:bg-neutral-900">
        <div className="container mx-auto px-6 relative">
          <div className="relative z-10">
            <h1 className="text-4xl md:text-6xl font-bold text-[#091225] dark:text-white text-center">
              Return Policy
            </h1>
            <p className="text-xl text-neutral-500 dark:text-neutral-400 max-w-2xl mx-auto text-center">
              Our commitment to customer satisfaction
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 px-6 bg-background">
        <div className="container mx-auto max-w-6xl">
          <div className="space-y-12">
            {/* Version and Date */}
            <div>
              <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">DecodeMed Return Policy</h2>
              <p className="text-neutral-600 dark:text-neutral-400">Version: 1.0</p>
              <p className="text-neutral-600 dark:text-neutral-400">Last Updated: April 7, 2025</p>
              <p className="text-neutral-700 dark:text-neutral-300 mt-4">
                At DecodeMed, we are committed to ensuring your satisfaction with our services. This Return Policy outlines the terms and conditions for refunds and cancellations.
              </p>
            </div>

            {/* 1. Subscription Refunds */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">1. Subscription Refunds</h2>
              <div className="space-y-4">
                <p className="text-neutral-700 dark:text-neutral-300">
                  We offer a 7-day money-back guarantee for all new subscriptions. If you are not satisfied with our service within 7 days of your initial purchase, you may request a full refund by contacting our support team at <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a>.
                </p>
                <p className="text-neutral-700 dark:text-neutral-300">
                  To be eligible for a refund, your request must be received within 7 days of the initial subscription purchase date.
                </p>
              </div>
            </div>

            {/* 2. Refund Exclusions */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">2. Refund Exclusions</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">The following situations are excluded from our refund policy:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Requests made after the 7-day refund period has expired.</li>
                <li>Subscription renewals (refunds apply only to initial purchases).</li>
                <li>Accounts that have violated our Terms of Service.</li>
                <li>Special promotional or discounted subscriptions marked as non-refundable.</li>
              </ul>
            </div>

            {/* 3. Cancellation Policy */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">3. Cancellation Policy</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">You may cancel your subscription at any time:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Cancellations can be made through your account settings or by contacting support.</li>
                <li>When you cancel, you will retain access to premium features until the end of your current billing period.</li>
                <li>No partial refunds are provided for unused portions of your billing period after the 7-day refund window.</li>
                <li>Cancellation prevents automatic renewal for the next billing cycle.</li>
              </ul>
            </div>

            {/* 4. Refund Process */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">4. Refund Process</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">To request a refund:</p>
              <ol className="list-decimal pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Contact our support team at <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a>.</li>
                <li>Include your account email and purchase date in your request.</li>
                <li>Briefly explain your reason for requesting a refund.</li>
                <li>Once approved, refunds are typically processed within 5-7 business days.</li>
                <li>The refund will be issued to the original payment method used for the purchase.</li>
              </ol>
            </div>

            {/* 5. Changes to This Policy */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">5. Changes to This Policy</h2>
              <p className="text-neutral-700 dark:text-neutral-300">
                We reserve the right to modify this Return Policy at any time. Changes will be effective when posted on this page with a revised date. We encourage you to review this Return Policy periodically for any changes.
              </p>
            </div>

            {/* 6. Contact Us */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">6. Contact Us</h2>
              <p className="text-neutral-700 dark:text-neutral-300">For questions about our Return Policy, please contact us at:</p>
              <p className="text-neutral-700 dark:text-neutral-300 mt-2">Email: <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a></p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

const ReturnPolicyPage = () => {
  return (
    <MainThemeProvider>
      <ReturnPolicyContent />
    </MainThemeProvider>
  )
}

export default ReturnPolicyPage; 