import os
from openai import Async<PERSON>penA<PERSON>
from dotenv import load_dotenv
import asyncio

from .chat_ranking import rank_articles
load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

if not api_key:
    raise ValueError("The OpenAI API key must be set in the environment variables.")

async def chat_response(question, chat_context, articles):
    # Add error handling for None/empty articles
    if not articles:
        # Return an error message that can be handled by the frontend
        error_message = "I apologize, but I couldn't retrieve any relevant research articles at the moment. This could be due to a temporary issue with the research database. Please try again in a few moments."
        yield error_message
        return

    top_articles = articles[:10]
    
    combined_abstracts = "\n\n".join([f"[{i+1}]. {article['title']}: {article['abstract']}" for i, article in enumerate(top_articles)])

    formatted_chat_context = "Previous interactions:\n"
    
    for i, entry in enumerate(chat_context, 1):
        formatted_chat_context += f"{i}.   User: {entry['user']}\n   Assistant: {entry['assistant']}\n\n"
    

    messages = [
        {
            "role": "system",
            "content": """You are a helpful assistant specializing in medical research. 
            Provide clear and concise answers to the user's questions based on the provided research data and previous context.
            Use references when citing information from the articles."""
        },
        {
            "role": "user",
            "content": f"""
            Context: {formatted_chat_context}

            Respond to the question: '{question}', do not include the question in your response.
            with this research data: {combined_abstracts}

            Guidelines:
            - Provide an answer that directly addresses the question. 
            - Use information from the provided research articles.
            - Use markdown formatting for the response.
            - References: Use reference numbers like this: [1][2][3]
            - Use the following structure for the response: 

               Initial response should respond to the question with the information from the research articles.
               Direct response to the question. [1][2][3]
               - Subtitle 1(bold): bullet point 1.  [4][5] 
               - Subtitle 2(bold): bullet point 2.  [6][7]  
               - Subtitles and bullet points as needed [8][9] .....
               Summary of the response. [numbers]....
            
            Use the references in the text, do not return a list of references, just return the references in the text.
            - CONTEXT: Use the context from previous interactions to provide a more comprehensive answer.
            - Ensure continuity with previous responses if the current question is related.
            - Once you got the context or question or query, do not return the question in your response again.
            """
        }
    ]

    response = await client.chat.completions.create(
        model="gpt-4o-mini",  # You can change this to the desired model
        messages=messages,
        temperature=0.2,
        stream=True,
    )
    
    async for chunk in response:
        if chunk.choices[0].delta.content is not None:
            yield chunk.choices[0].delta.content

# TESTING
async def test_chat_response():
    question = "What are the latest treatments for COVID-19?"
    chat_history = [
        {"user": "What is COVID-19?", "assistant": "COVID-19 is a respiratory illness caused by the SARS-CoV-2 virus..."},
        {"user": "What are the symptoms of COVID-19?", "assistant": "Common symptoms of COVID-19 include fever, cough, and fatigue..."}
    ]
    


    print("Parsing articles...")
    articles = await rank_articles(question, chat_history)
    
    print("Generating response...")
    full_response = ""
    async for chunk in chat_response(question, chat_history, articles):
        full_response += chunk
        print(chunk, end='', flush=True)  # Print each chunk as it's received
    


if __name__ == "__main__":
    asyncio.run(test_chat_response())
