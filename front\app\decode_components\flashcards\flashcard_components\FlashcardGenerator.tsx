"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {  fetchWithTokenRetry } from '../flashcards';
import { API_URL } from '@/app/utils/decode_api';

interface MedSpace {
  id: number;
  name: string;
}

// Unit data structure from the API
interface UnitData {
  id?: number;
  name?: string;
  title?: string;
  [key: string]: unknown; // Allow other properties with unknown type
}

interface FlashcardGeneratorProps {
  projectId: string;
}

// API response for generating flashcards
interface FlashcardGenerationResponse {
  count?: number;
  message?: string;
  status?: string;
  flashcards: Array<{ id?: number; question: string; answer: string; difficulty_rank?: number; tags?: string[]; }>;
}

export default function FlashcardGenerator({ projectId }: FlashcardGeneratorProps) {
  const { getToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [units, setUnits] = useState<MedSpace[]>([]);
  const [selectedUnit, setSelectedUnit] = useState<number | null>(null);
  const [generating, setGenerating] = useState(false);

  // API methods specific to FlashcardGenerator
  const checkFlashcardsGeneratedStatus = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Checking flashcards status for project:', projectId);
      
      // Use the correct endpoint from the backend routes
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/flashcards/by-project?project_id=${projectId}`,
        { method: 'GET' },
        getToken
      );

      if (!response.ok) {
        // Log the error response for debugging
        console.log(`Status endpoint returned ${response.status}, assuming no flashcards generated`);
        if (response.status === 500) {
          console.log('Backend error detected. There might be a database issue with the flashcards table.');
        }
        return false;
      }

      const data = await response.json();
      console.log('Flashcard status response:', data);
      
      // If we get a successful response with data, consider flashcards as generated
      // Handle null responses properly
      if (data === null) {
        console.log('Received null response from flashcards endpoint, assuming no flashcards generated');
        return false;
      }
      
      return Array.isArray(data) && data.length > 0;
    } catch (error) {
      console.error('Error in checkFlashcardsGeneratedStatus:', error);
      // Don't throw, just return false to indicate no flashcards
      return false;
    }
  }, [projectId, getToken]);

  const getProjectUnits = useCallback(async (): Promise<MedSpace[]> => {
    try {
      console.log('Fetching units for project:', projectId);
      
      // Use the correct endpoint from backend routes
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/units?project_id=${projectId}`,
        { method: 'GET' },
        getToken
      );

      if (!response.ok) {
        console.log(`Units endpoint returned ${response.status}, using placeholder data`);
        // Return empty array or placeholder data instead of throwing
        return [];
      }

      const data = await response.json();
      console.log('Project units response:', data);
      
      // Handle different response formats
      if (Array.isArray(data)) {
        // Ensure each unit has an id and name
        return data.map((unit: UnitData) => ({
          id: unit.id || 0, // Provide default id to prevent undefined
          name: unit.name || unit.title || 'Unnamed Unit' // Check multiple possible name fields
        }));
      }
      
      if (data && data.units && Array.isArray(data.units)) {
        // Ensure each unit has an id and name
        return data.units.map((unit: UnitData) => ({
          id: unit.id || 0, // Provide default id to prevent undefined
          name: unit.name || unit.title || 'Unnamed Unit' // Check multiple possible name fields
        }));
      }
      
      console.log('No valid units data found in response');
      return [];
    } catch (error) {
      console.error('Error in getProjectUnits:', error);
      // Return empty array instead of throwing
      return [];
    }
  }, [projectId, getToken]);

  const generateFlashcardsForUnit = async (unitId: string): Promise<FlashcardGenerationResponse> => {
    try {
      console.log(`Generating flashcards for unit ${unitId} in project ${projectId}`);
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/flashcards/generate`,
        { 
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            unit_id: unitId,
            project_id: projectId,
            type: 'unit'
          })
        },
        getToken
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error generating flashcards: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Failed to generate flashcards: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Flashcard generation response:', data);
      return data;
    } catch (error) {
      console.error('Error in generateFlashcardsForUnit:', error);
      throw error;
    }
  };

  const generateFlashcardsForAllUnits = async (): Promise<FlashcardGenerationResponse> => {
    try {
      console.log(`Generating flashcards for all units in project ${projectId}`);
      
      // Use the single card generation endpoint with a project_id param
      const response = await fetchWithTokenRetry(
        `${API_URL}/api/decode/flashcards/generate`,
        { 
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            project_id: projectId,
            type: 'project'
          })
        },
        getToken
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error generating flashcards for all units: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Failed to generate flashcards for all units: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Flashcard generation for all units response:', data);
      
      // Default response structure if the API doesn't return the expected format
      if (!data) {
        return {
          message: "Flashcards generation initiated",
          count: 0,
          flashcards: []
        };
      }
      
      return data;
    } catch (error) {
      console.error('Error in generateFlashcardsForAllUnits:', error);
      throw error;
    }
  };

  // Load units for the project
  useEffect(() => {
    const loadUnits = async () => {
      if (!projectId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const unitsData = await getProjectUnits();
        setUnits(unitsData);
        
        // Check if flashcards have already been generated
        const isGenerated = await checkFlashcardsGeneratedStatus();
        if (isGenerated) {
          setMessage('Flashcards have already been generated for this project.');
        }
      } catch (err) {
        console.error('Error loading units:', err);
        setError('Failed to load units. The API endpoint may not be available yet.');
      } finally {
        setLoading(false);
      }
    };
    
    loadUnits();
  }, [projectId, getProjectUnits, checkFlashcardsGeneratedStatus]);

  const handleUnitSelect = (unitId: number) => {
    setSelectedUnit(unitId);
  };

  const handleGenerateForUnit = async () => {
    if (!selectedUnit) {
      setError('Please select a unit first');
      return;
    }
    
    setGenerating(true);
    setError(null);
    setMessage(null);
    
    try {
      const result = await generateFlashcardsForUnit(selectedUnit.toString());
      setMessage(`Successfully generated ${result.count || 0} flashcards for the selected unit.`);
    } catch (err) {
      console.error('Error generating flashcards:', err);
      setError('Failed to generate flashcards. The API endpoint may not be available yet.');
    } finally {
      setGenerating(false);
    }
  };

  const handleGenerateForAll = async () => {
    setGenerating(true);
    setError(null);
    setMessage(null);
    
    try {
      const result = await generateFlashcardsForAllUnits();
      setMessage(`Successfully initiated flashcard generation for all units. ${result.message || ''}`);
    } catch (err) {
      console.error('Error generating flashcards for all units:', err);
      setError('Failed to generate flashcards. The API endpoint may not be available yet.');
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
        <p className="text-muted-foreground">Loading units...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Generate Flashcards</h3>
        </CardHeader>
        <CardContent>
          {message && (
            <div className="mb-4 p-2 bg-green-100 text-green-800 rounded">
              {message}
            </div>
          )}
          
          {error && (
            <div className="mb-4 p-2 bg-red-100 text-red-800 rounded">
              {error}
            </div>
          )}
          
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Generate for All Units</h4>
              <Button 
                onClick={handleGenerateForAll} 
                disabled={generating}
                className="w-full"
              >
                {generating ? 'Generating...' : 'Generate for All Units'}
              </Button>
            </div>
            
            <div className="border-t pt-4">
              <h4 className="text-sm font-medium mb-2">Generate for Specific Unit</h4>
              
              {units.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    {units.map((unit) => (
                      <Button
                        key={`unit-${unit.id || Math.random().toString(36).substr(2, 9)}`}
                        variant={selectedUnit === unit.id ? "default" : "outline"}
                        onClick={() => handleUnitSelect(unit.id)}
                        className="text-left"
                      >
                        {unit.name || 'Unnamed Unit'}
                      </Button>
                    ))}
                  </div>
                  
                  <Button 
                    onClick={handleGenerateForUnit} 
                    disabled={!selectedUnit || generating}
                    className="w-full"
                  >
                    {generating ? 'Generating...' : 'Generate for Selected Unit'}
                  </Button>
                </div>
              ) : (
                <p className="text-gray-500">No units found for this project.</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {error && error.includes('database') && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive font-medium">Backend Database Error</p>
            <p className="text-sm text-muted-foreground mt-2">
              The backend is encountering a database error. The table &ldquo;flashcard_with_tags&rdquo; might be missing.
              Please contact your backend developer to check the database schema.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 