[{"id": "61f7c496-bcea-4ba2-92d8-84691d21595b", "title": {"en": "Myocardial Infarction (MI): Clinical Notes", "es": "<PERSON>far<PERSON>"}, "overview": {"code": {"en": "\nconst TitleSection = () => {\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-background rounded-lg shadow-md text-foreground\">\n      <h1 className=\"text-3xl font-bold text-red-700 dark:text-red-500 mb-6 border-b pb-2\">Myocardial Infarction (MI): Clinical Notes</h1>\n      \n      <div className=\"bg-red-100 dark:bg-red-900/30 p-4 border-l-4 border-red-700 dark:border-red-500 mb-6 flex items-start\">\n        <AlertTriangle className=\"text-red-700 dark:text-red-500 mr-2 mt-1 flex-shrink-0\" size={20} />\n        <div>\n          <h3 className=\"font-bold text-red-700 dark:text-red-500\">EMERGENCY CONDITION</h3>\n          <p className=\"text-foreground/90\">Immediate assessment and treatment required. Time is myocardium.</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nrender(<TitleSection />);", "es": ""}}, "system": "cardiovascular", "sections": {"etiology": {"READ": {"title": {"en": "Pathophysiology", "es": ""}, "code": {"en": "\nconst PathophysiologySection = () => {\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-background rounded-lg shadow-md text-foreground\">\n      <section className=\"mb-6\">\n        <h2 className=\"text-xl font-bold text-foreground mb-2\">Pathophysiology</h2>\n        <ul className=\"list-disc pl-6 text-foreground/90 space-y-1\">\n          <li>Usually results from complete thrombotic occlusion of a coronary artery</li>\n          <li>Most commonly caused by rupture of an atherosclerotic plaque</li>\n          <li>Results in ischemia and eventual necrosis of myocardial tissue</li>\n          <li>Infarct evolution occurs over several hours (6-12 hours for complete necrosis)</li>\n          <li>Area at risk may be salvageable with prompt reperfusion therapy</li>\n        </ul>\n      </section>\n    </div>\n  );\n};\n\nrender(<PathophysiologySection />);", "es": ""}}}}}]