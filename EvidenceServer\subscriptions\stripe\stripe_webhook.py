import os
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import stripe
from subscriptions.models import UserSubscription
from datetime import datetime



# WEBHOOKS ------------ CONSTANTLY UPDATING SUBSCRIPTION STATUS 
@csrf_exempt
def stripe_webhook(request):
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    webhook_secret = os.getenv('STRIPE_WEBHOOK_SECRET')


    if not webhook_secret:
        print("Error: STRIPE_WEBHOOK_SECRET not configured")
        return JsonResponse({'error': 'Webhook secret not configured'}, status=500)
    
    if not sig_header:
        print("Error: No Stripe signature header found")
        return JsonResponse({'error': 'No Stripe signature found'}, status=400)

    try:
        # Convert payload to string if it's bytes
        if isinstance(payload, bytes):
            payload = payload.decode('utf-8')
            
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
        
      

        # Handle different event types
        if event['type'] == 'checkout.session.completed':
            session = event.data.object
            print(f"Processing checkout session: {session.id}")
            if session.subscription:
                subscription = stripe.Subscription.retrieve(session.subscription)
                handle_subscription_created(subscription)
                
        elif event['type'] == 'customer.subscription.updated':
            subscription = event.data.object
            print(f"Processing subscription update: {subscription.id}")
            handle_subscription_updated(subscription)
            
        elif event['type'] == 'customer.subscription.deleted':
            subscription = event.data.object
            print(f"Processing subscription deletion: {subscription.id}")
            handle_subscription_deleted(subscription)
            
        elif event['type'] == 'invoice.payment_failed':
            invoice = event.data.object
            print(f"Processing failed payment for invoice: {invoice.id}")
            handle_payment_failed(invoice)
            
        elif event['type'] == 'invoice.payment_succeeded':
            invoice = event.data.object
            print(f"Processing successful payment for invoice: {invoice.id}")
            handle_payment_succeeded(invoice)
        
        return JsonResponse({'status': 'success'})
        
    except ValueError as e:
        print(f"Invalid payload: {str(e)}")
        return JsonResponse({'error': 'Invalid payload'}, status=400)
    except stripe.error.SignatureVerificationError as e:
        print(f"Invalid signature: {str(e)}")
        return JsonResponse({'error': 'Invalid signature'}, status=400)
    except Exception as e:
        print(f"Webhook error: {str(e)}")
        return JsonResponse({'error': str(e)}, status=400)


def handle_subscription_created(subscription):
    try:
        # Get user information from metadata
        user_id = subscription.metadata.get('user_id')
        subscription_type = subscription.metadata.get('subscription_type')
        
        # Get subscription end date from Stripe
        subscription_end = datetime.fromtimestamp(subscription.current_period_end)
        
        user_subscription, created = UserSubscription.objects.get_or_create(
            user_id=user_id,
            defaults={
                'subscription_provider': 'stripe',
                'stripe_customer_id': subscription.customer,
                'stripe_subscription_id': subscription.id,
                'subscription_status': 'active',
                'subscription_type': subscription_type,
                'subscription_end_date': subscription_end  # Add end date
            }
        )
        
        if not created:
            # Update existing subscription
            user_subscription.stripe_subscription_id = subscription.id
            user_subscription.subscription_status = 'active'
            user_subscription.subscription_type = subscription_type
            user_subscription.subscription_end_date = subscription_end  # Update end date
            user_subscription.save()
            
        print(f"✅ Successfully processed subscription for user {user_id}")
        print(f"📅 Subscription ends at: {subscription_end}")
    except Exception as e:
        print(f"❌ Error in handle_subscription_created: {str(e)}")

def handle_subscription_updated(subscription):
    user_id = subscription.metadata.get('user_id')
    subscription_type = subscription.metadata.get('subscription_type')
    
    try:
        user_subscription = UserSubscription.objects.get(user_id=user_id)
        user_subscription.subscription_status = subscription.status
        user_subscription.subscription_type = subscription_type
        user_subscription.will_cancel = subscription.cancel_at_period_end
        
        # Update end date based on cancellation status
        if subscription.cancel_at_period_end:
            user_subscription.subscription_end_date = datetime.fromtimestamp(subscription.cancel_at)
        else:
            user_subscription.subscription_end_date = datetime.fromtimestamp(subscription.current_period_end)
        
        user_subscription.save()
        print(f"✅ Successfully updated subscription for user {user_id}")
        print(f"📅 New end date: {user_subscription.subscription_end_date}")
    except UserSubscription.DoesNotExist:
        print(f"❌ No subscription found for user {user_id}")

def handle_subscription_deleted(subscription):
    user_id = subscription.metadata.get('user_id')
    
    try:
        user_subscription = UserSubscription.objects.get(user_id=user_id)
        user_subscription.subscription_status = 'inactive'
        user_subscription.subscription_end_date = datetime.now()  # Set end date to now
        user_subscription.save()
        print(f"✅ Successfully marked subscription as inactive for user {user_id}")
    except UserSubscription.DoesNotExist:
        print(f"❌ No subscription found for user {user_id}")

def handle_payment_failed(invoice):
    subscription_id = invoice.subscription
    if subscription_id:
        try:
            subscription = stripe.Subscription.retrieve(subscription_id)
            user_id = subscription.metadata.get('user_id')
            
            user_subscription = UserSubscription.objects.get(user_id=user_id)
            user_subscription.subscription_status = 'past_due'
            # Keep the existing end date
            user_subscription.save()
            print(f"⚠️ Payment failed for user {user_id}")
        except Exception as e:
            print(f"❌ Error in handle_payment_failed: {str(e)}")

def handle_payment_succeeded(invoice):
    subscription_id = invoice.subscription
    if subscription_id:
        try:
            subscription = stripe.Subscription.retrieve(subscription_id)
            user_id = subscription.metadata.get('user_id')
            
            user_subscription = UserSubscription.objects.get(user_id=user_id)
            user_subscription.subscription_status = 'active'
            # Update end date after successful payment
            user_subscription.subscription_end_date = datetime.fromtimestamp(subscription.current_period_end)
            user_subscription.save()
            print(f"✅ Payment succeeded for user {user_id}")
            print(f"📅 Next billing date: {user_subscription.subscription_end_date}")
        except Exception as e:
            print(f"❌ Error in handle_payment_succeeded: {str(e)}")