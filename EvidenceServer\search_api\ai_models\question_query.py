import os
from openai import Async<PERSON>penA<PERSON>
from dotenv import load_dotenv
load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

if not api_key:
    raise ValueError("The OpenAI API key must be set in the environment variables.")

async def generate_search_query(question, publication_types):
    # Add input validation
    if question is None or not question.strip():
        raise ValueError("Question cannot be empty or None")

    # Create publication types string in a more readable way
    if publication_types:
        types_quoted = [f'"{pt}"' for pt in publication_types]
        publication_types_str = f'AND ({" OR ".join(types_quoted)})'
    else:
        publication_types_str = ''
    
    response = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": f"""You are an assistant that generates effective Europe PMC search queries. Follow these guidelines:
                MEDICAL TERMINOLOGY:
                - Only use medical terminology, if the question does not contain medical terminology, always use medical terminology synonyms.
                - Use medical terms for PubMed search queries.
                KEYWORDS STRUCTURE:
                - Do not use abbreviations.
                - Return the keywords relevant to the disease and the topic.
                - Return up to 6 keywords, use AND, OR if necessary.
                - Use this structure as an example, do not include keywords that will not answer the question.
                  keyword1 OR keyword2 AND keyword3 OR keyword4 AND keyword5 OR keyword6  {publication_types_str}
                - Use lowercase.
                PUBLICATION TYPE:
                - If only keywords are provided, return a search query that includes only uses the keywords and the publication type: {publication_types_str} if it is not empty, if it is empty, return a search query that does not contain any publication type.
                - Include the following publication type: {publication_types_str}, if empty, return a search query that does not contain any publication type.
                
                
                """
            },
            {
                "role": "user",
                "content": f"Generate a query for this biomedical research question: {question}"
            }
        ],
        temperature=0.1,
    )
    
    query = response.choices[0].message.content.strip()
    return query


