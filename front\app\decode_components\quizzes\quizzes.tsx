"use client";

import React, { useState, useEffect, useCallback } from "react";
import { CheckCircle, XCircle, Loader2, RefreshCw,  ChevronLeft, ChevronRight } from "lucide-react";
import { useTheme } from "@/components/theme/theme-provider";
import { API_URL } from "@/app/utils/decode_api";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";

interface QuizzesProps {
  projectId?: string;
}

interface Project {
  id: number;
  name: string;
  description: string;
  created_at: string;
  unit_count?: number;
}

interface Unit {
  id: string;
  title: string;
  content: string;
  project_id: number;
}

interface QuizQuestion {
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  answer: number | boolean | string;
  explanation: string;
}

interface Quiz {
  id: number;
  unit_id: number;
  questions: QuizQuestion[];
  created_at: string;
  updated_at: string;
  user_progress?: UserProgress;
  max_score?: number;
  passing_score?: number;
}

interface UserProgress {
  answered_questions: number[];
  correct_answers: number[];
  current_score: number;
  completed: boolean;
}

interface ProjectData {
  ID?: number;
  id?: number;
  name?: string;
  Name?: string;
  description?: string;
  Description?: string;
  CreatedAt?: string;
  createdAt?: string;
  created_at?: string;
  unit_count?: number;
}

interface UnitData {
  ID?: string | number;
  id?: string | number;
  title?: string;
  Title?: string;
  content?: string;
  Content?: string;
  project_id?: number;
  ProjectID?: number;
}

export default function Quizzes({ projectId }: QuizzesProps) {
  return <QuizzesApp projectId={projectId} />;
}

function QuizzesApp({ projectId }: QuizzesProps) {
  const { getToken } = useAuth();
  const { theme } = useTheme();
  const router = useRouter();
  
  // Project related state (keeping only what's needed)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  
  // Unit related states
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedUnit, setSelectedUnit] = useState<Unit | null>(null);
  const [carouselIndex, setCarouselIndex] = useState(0);
  
  // Quiz related states
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatingQuiz, setGeneratingQuiz] = useState(false);
  const [jobStatus, setJobStatus] = useState<string | null>(null);
  const [pollInterval, setPollInterval] = useState<NodeJS.Timeout | null>(null);
  
  // Quiz interaction states
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState<(number | boolean | string)[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [quizMode, setQuizMode] = useState<'quiz'>('quiz');
  
  // New state variables for tracking quiz progress
  const [answerFeedback, setAnswerFeedback] = useState<boolean[]>([]);
  const [showFeedback, setShowFeedback] = useState(false);
  const [pointsEarned, setPointsEarned] = useState(0);
  const [totalPoints, setTotalPoints] = useState(0);
  const [questionPoints, setQuestionPoints] = useState<number[]>([]);
  
  // Define all callback functions before useEffects
  
  // Fetch projects with useCallback
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      const token = await getToken();
      
      const response = await fetch(`${API_URL}/api/decode/projects`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (!response.ok) throw new Error('Failed to fetch projects');
      const data = await response.json();
      
      let projectsData = [];
      
      if (Array.isArray(data)) {
        projectsData = data;
      } else if (data && Array.isArray(data.projects)) {
        projectsData = data.projects;
      } else if (data && data.project) {
        projectsData = [data.project];
      }
      
      const normalizedProjects = projectsData.map((project: ProjectData) => {
        if (!project) return null;
        return {
          id: project.ID || project.id,
          name: project.name || project.Name,
          description: project.description || project.Description,
          created_at: project.CreatedAt || project.createdAt || project.created_at,
          unit_count: project.unit_count || 0
        };
      }).filter(Boolean);
      
      // Select the first project automatically
      if (normalizedProjects.length > 0) {
        setSelectedProject(normalizedProjects[0]);
        // Let the useEffect for selectedProject handle fetching units rather than calling directly
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      setError('Failed to fetch projects');
    } finally {
      setLoading(false);
    }
  }, [getToken, setLoading, setSelectedProject, setError]);
  
  // Fetch units for a project with useCallback
  const fetchUnitsForProject = useCallback(async (projectId: number | string) => {
    try {
      // Convert to number if it's a string
      const numericProjectId = typeof projectId === 'string' ? parseInt(projectId, 10) : projectId;
      
      // Guard against undefined or invalid projectId
      if (!numericProjectId || isNaN(numericProjectId)) {
        console.error('Invalid project ID provided to fetchUnitsForProject:', projectId);
        return;
      }
      
      console.log('Fetching units for project ID:', numericProjectId);
      setLoading(true);
      const token = await getToken();
      
      const response = await fetch(`${API_URL}/api/decode/units/all?project_id=${numericProjectId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 402) {
        // Redirect to upgrade page for subscription required
        router.push('/subscriptions/pricing');
        throw new Error('Subscription required to access this content');
      }

      if (!response.ok) throw new Error('Failed to fetch units');
      const data = await response.json();
      
      let unitsData = [];
      
      // Handle different response formats
      if (Array.isArray(data)) {
        unitsData = data;
      } else if (data && Array.isArray(data.units)) {
        unitsData = data.units;
      }
      
      // Normalize units - Add proper console debug output
      console.log("Units data received:", unitsData);
      
      const normalizedUnits = unitsData.map((unit: UnitData) => {
        if (!unit) return null;
        const normalized = {
          id: unit.ID || unit.id,
          title: unit.title || unit.Title,
          content: unit.content || unit.Content,
          project_id: unit.project_id || unit.ProjectID
        };
        console.log("Normalized unit:", normalized);
        return normalized;
      }).filter(Boolean);
      
      setUnits(normalizedUnits);
      setSelectedUnit(null);
      setQuiz(null);
    } catch (error) {
      console.error('Error fetching units:', error);
      setError('Failed to fetch units');
    } finally {
      setLoading(false);
    }
  }, [getToken, setLoading, setUnits, setSelectedUnit, setQuiz, setError, router]);
  
  // Fetch quiz for a unit with useCallback
  const fetchQuizForUnit = useCallback(async (unitId: string | number) => {
    // Guard against undefined or invalid unitId
    if (!unitId) {
      console.error('Invalid unit ID provided to fetchQuizForUnit:', unitId);
      return;
    }
    
    if (typeof unitId === 'string' && unitId.trim() === '') {
      console.error('Empty unit ID provided to fetchQuizForUnit');
      return;
    }
    
    setLoading(true);
    
    try {
      const token = await getToken();
      console.log('Fetching quiz for unit ID:', unitId);
      
      const response = await fetch(`${API_URL}/api/decode/quiz/${unitId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
     
        
        // Parse questions from JSON string if necessary
        if (typeof data.questions === 'string') {
          try {
            data.questions = JSON.parse(data.questions);
          } catch (e) {
            console.error('Error parsing questions JSON:', e);
            data.questions = [];
          }
        }
        
        // Parse user progress if available
        if (data.user_progress && typeof data.user_progress === 'string') {
          try {
            data.user_progress = JSON.parse(data.user_progress);
          } catch (e) {
            console.error('Error parsing user progress:', e);
            data.user_progress = null;
          }
        }

        // Ensure questions is an array
        if (!Array.isArray(data.questions)) {
          console.error('Questions is not an array:', data.questions);
          data.questions = [];
        }
        
        setQuiz(data);
        
        // Initialize quiz state
        setCurrentQuestionIndex(0);
        setUserAnswers(new Array(data.questions.length).fill(null));
        setAnswerFeedback(new Array(data.questions.length).fill(false));
        setShowResults(false);
        setShowFeedback(false);
        
        // Set points for each question (default 1 point per question)
        const points = data.questions.map(() => 1);
        setQuestionPoints(points);
        setTotalPoints(points.reduce((sum: number, val: number) => sum + val, 0));
        setPointsEarned(0);
        
        // If there's saved progress, restore it
        if (data.user_progress) {
          const progress = data.user_progress;
          
          // If the quiz was completed, show results
          if (progress.completed) {
            setShowResults(true);
          }
          
          // Restore correct answers
          if (progress.correct_answers && progress.correct_answers.length > 0) {
            const newFeedback = [...answerFeedback];
            progress.correct_answers.forEach((index: number) => {
              if (index !== null && typeof index === 'number') {
                newFeedback[index] = true;
              }
            });
            setAnswerFeedback(newFeedback);
            setPointsEarned(progress.current_score || progress.correct_answers.length);
          }
        }
        
        setLoading(false);
      } else {
        if (response.status === 404) {
          // No quiz exists yet for this unit
          setQuiz(null);
          setError('No quiz found for this unit. Try generating one first.');
        } else {
          setError('Failed to fetch quiz. Please try again.');
        }
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching quiz:', error);
      setError('An error occurred while fetching the quiz.');
      setLoading(false);
    }
  }, [getToken, setLoading, setQuiz, setError, setCurrentQuestionIndex, setUserAnswers, setAnswerFeedback, setShowFeedback, setShowResults]);
  
  // Now all the useEffects with proper dependencies
  
  // Fetch projects on component mount - simplified to just select the first or specified project
  useEffect(() => {
    if (projectId) {
      console.log("Quizzes component: Loading project with ID:", projectId);
      const fetchAndSelectProject = async () => {
        try {
          const token = await getToken();
          if (!token) return;
          
          if (!projectId) {
            console.error("Project ID is undefined");
            return;
          }
          
          console.log("Fetching project with ID:", projectId);
          const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (!response.ok) {
            console.error(`Failed to fetch project: ${response.status}`);
            throw new Error(`Failed to fetch project: ${response.status}`);
          }
          
          const data = await response.json();
          if (data.project) {
            console.log("Project data received:", data.project);
            setSelectedProject(data.project);
            
            const rawProjectId = data.project.id || data.project.ID;
            const projectIdNumber = typeof rawProjectId === 'string' ? parseInt(rawProjectId, 10) : rawProjectId;
            
            if (projectIdNumber && !isNaN(projectIdNumber)) {
              console.log("Using project ID for fetching units:", projectIdNumber);
              fetchUnitsForProject(projectIdNumber);
            } else {
              console.error("Invalid project ID:", rawProjectId);
            }
          } else {
            console.error("Project data missing in response:", data);
          }
        } catch (error) {
          console.error("Error fetching specific project:", error);
        }
      };
      
      fetchAndSelectProject();
    } else {
      // If no specific projectId, fetch all projects and select the first one
      fetchProjects();
    }
  }, [projectId, getToken, fetchUnitsForProject, fetchProjects]);
  
  // Fetch units when a project is selected
  useEffect(() => {
    if (selectedProject) {
      // Extract project ID, handling both uppercase and lowercase property names
      const projectId = selectedProject.id;
      
      if (projectId) {
        // Convert to number if needed
        const projectIdNumber = typeof projectId === 'string' ? parseInt(projectId, 10) : projectId;
        if (projectIdNumber && !isNaN(projectIdNumber)) {
          fetchUnitsForProject(projectIdNumber);
        }
      }
    }
  }, [selectedProject, fetchUnitsForProject]);
  
  // Fetch quiz when a unit is selected
  useEffect(() => {
    if (selectedUnit) {
      // Extract unit ID, handling both uppercase and lowercase property names
      const unitId = selectedUnit.id;
      
      if (unitId) {
        // Convert to number if needed
        const unitIdNumber = typeof unitId === 'string' ? parseInt(unitId, 10) : unitId;
        if (unitIdNumber && !isNaN(unitIdNumber)) {
          // Only fetch if we don't already have a quiz for this unit
          if (!quiz || quiz.unit_id !== unitIdNumber) {
            fetchQuizForUnit(unitIdNumber);
          }
        }
      }
    }
  }, [selectedUnit]); // Remove fetchQuizForUnit from dependencies
  
  // Handle answer selection
  const handleAnswerSelect = (answer: number | boolean | string) => {
    if (showFeedback) return; // Prevent changing answer after feedback is shown
    
    const newAnswers = [...userAnswers];
    newAnswers[currentQuestionIndex] = answer;
    setUserAnswers(newAnswers);
  };
  
  const checkAnswer = () => {
    if (!quiz) return;
    
    const currentQuestion = quiz.questions[currentQuestionIndex];
    const userAnswer = userAnswers[currentQuestionIndex];
    const isCorrect = userAnswer === currentQuestion.answer;
    
    // Update the answer feedback state
    const newFeedback = [...answerFeedback];
    newFeedback[currentQuestionIndex] = isCorrect;
    setAnswerFeedback(newFeedback);
    
    // Set points for the question
    const points = 1; // Default one point per question
    const newPoints = [...questionPoints];
    newPoints[currentQuestionIndex] = points;
    setQuestionPoints(newPoints);
    
    setShowFeedback(true);
  };
  
  // Move to next question
  const nextQuestion = () => {
    if (!quiz) return;
    
    if (!showFeedback) {
      // If feedback not shown yet, check the answer first
      checkAnswer();
      return;
    }
    
    // Reset feedback state for the next question
    setShowFeedback(false);
    
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      // Save progress periodically
      if (currentQuestionIndex % 3 === 0) {
        saveQuizProgress();
      }
    } else {
      setShowResults(true);
      // Save final quiz progress
      saveQuizProgress();
    }
  };
  
  // Move to previous question
  const prevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      // No longer resetting showFeedback when going to previous question
    }
  };
  
  // Updated reset quiz function without quizMode
  const resetQuiz = () => {
    if (quiz) {
      setCurrentQuestionIndex(0);
      setUserAnswers(Array(quiz.questions.length).fill(null));
      setAnswerFeedback(Array(quiz.questions.length).fill(false));
      setShowResults(false);
      setShowFeedback(false);
      setPointsEarned(0);
    }
  };
  
  // Update calculate score function to simplify
  const calculateScore = () => {
    if (!quiz || quiz.questions.length === 0) return;
    
    let earned = 0;
    let total = 0;
    
    quiz.questions.forEach((_, index) => {
      const points = questionPoints[index] || 1;
      total += points;
      
      if (answerFeedback[index]) {
        earned += points;
      }
    });
    
    setPointsEarned(earned);
    setTotalPoints(total);
  };
  
  // Updated saving progress
  const saveQuizProgress = async () => {
    if (!quiz || !selectedUnit) return;
    
    try {
      calculateScore();
      
      const token = await getToken();
      
      // Additional check - pointsEarned and totalPoints should be set by calculateScore
      if (pointsEarned === 0 && totalPoints === 0) {
        // Fallback calculation if needed
        const correct = answerFeedback.filter(Boolean).length;
        const earned = correct;
        const total = quiz.questions.length;
        setPointsEarned(earned);
        setTotalPoints(total);
      }
      
      const percentage = totalPoints > 0 ? Math.round((pointsEarned / totalPoints) * 100) : 0;
      
      // Check if the quiz is passed based on the passing score (default to 70%)
      const isPassed = percentage >= (quiz.passing_score || 70);
      
      const response = await fetch(`${API_URL}/api/decode/quiz/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          quiz_id: quiz.id,
          unit_id: selectedUnit.id,
          answered_questions: userAnswers.map((_, index) => index).filter(i => userAnswers[i] !== null),
          correct_answers: answerFeedback.map((isCorrect, index) => isCorrect ? index : null).filter(i => i !== null),
          current_score: percentage,
          completed: isPassed,
        }),
      });
      
      if (response.ok) {
        setShowResults(true);
      }
    } catch (error) {
      console.error("Error saving progress:", error);
    }
  };
  
  // Connect to WebSocket for real-time quiz updates
  useEffect(() => {
    if (!selectedProject?.id) return;
    
    // Create WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/quiz`;
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('WebSocket connection established');
      
      // Subscribe to updates for this project
      ws.send(JSON.stringify({
        type: 'subscribe_quiz',
        payload: {
          project_id: selectedProject.id
        }
      }));
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'quiz_update' && data.payload) {
          console.log('Received quiz update via WebSocket:', data);
          
          // If we're waiting for quizzes to be generated and we get a completed status
          if (generatingQuiz && data.payload.status === 'completed') {
            setGeneratingQuiz(false);
            setJobStatus('completed');
            
            // Clear any existing poll interval
            if (pollInterval) {
              clearInterval(pollInterval);
              setPollInterval(null);
            }
            
            // Fetch updated units with their quizzes
            if (selectedProject && selectedProject.id) {
              // Ensure we're passing a number using Number() conversion
              fetchUnitsForProject(Number(selectedProject.id));
            }
          }
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    ws.onclose = () => {
      console.log('WebSocket connection closed');
    };
    
    // Clean up on unmount
    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [selectedProject?.id, generatingQuiz, pollInterval]);
  
  // Render the current question
  const renderCurrentQuestion = () => {
    if (!quiz || !quiz.questions || quiz.questions.length === 0) {
      // Show loading animation while quiz is being fetched or generated
      return (
        <div className="flex items-center justify-center h-full w-full py-10">
          <div className="text-center">
            <Loader2 size={36} className={`animate-spin mx-auto mb-3 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} />
            <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-sm`}>
              Loading quiz questions...
            </p>
          </div>
        </div>
      );
    }
    
    // Get current question
    const currentQuestion = quiz.questions[currentQuestionIndex];
    const userAnswer = userAnswers[currentQuestionIndex];
    const isCorrect = showFeedback && answerFeedback[currentQuestionIndex];
    const isAnswered = userAnswer !== null && showFeedback;
    
    return (
      <div className="w-full">
        <div className="mb-4 flex justify-between items-center">
          <h3 className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-800'}`}>
            Question {currentQuestionIndex + 1} of {quiz.questions.length}
          </h3>
        </div>
        <p className={`text-lg ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'} mb-6`}>
          {currentQuestion.question}
        </p>
        
        <div className="space-y-4 mb-6">
          {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (
            currentQuestion.options.map((option, i) => (
              <div
                key={i}
                onClick={() => !showFeedback && handleAnswerSelect(i)}
                className={`p-3 rounded-md cursor-pointer transition-colors ${
                  isAnswered
                    ? (i === currentQuestion.answer 
                        ? (theme === 'dark' ? 'bg-green-600 text-white' : 'bg-green-100 text-green-800')
                        : (userAnswer === i
                            ? (theme === 'dark' ? 'bg-red-600 text-white' : 'bg-red-100 text-red-800')
                            : (theme === 'dark' ? 'bg-[#1e1e1e] hover:bg-[#303030] text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800')))
                    : (userAnswer === i
                        ? (theme === 'dark' ? 'bg-[#303030] text-white' : 'bg-gray-300 text-gray-800')
                        : (theme === 'dark' ? 'bg-[#1e1e1e] hover:bg-[#303030] text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'))
                }`}
              >
                {option}
              </div>
            ))
          )}
          
          {currentQuestion.type === 'true-false' && (
            <div className="flex space-x-4">
              <div
                onClick={() => !showFeedback && handleAnswerSelect(true)}
                className={`flex-1 p-3 rounded-md text-center cursor-pointer transition-colors ${
                  isAnswered
                    ? (currentQuestion.answer === true 
                        ? (theme === 'dark' ? 'bg-green-600 text-white' : 'bg-green-100 text-green-800')
                        : (userAnswer === true
                            ? (theme === 'dark' ? 'bg-red-600 text-white' : 'bg-red-100 text-red-800')
                            : (theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800')))
                    : (userAnswer === true
                        ? (theme === 'dark' ? 'bg-gray-600 text-white' : 'bg-gray-300 text-gray-800')
                        : (theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'))
                }`}
              >
                True
              </div>
              <div
                onClick={() => !showFeedback && handleAnswerSelect(false)}
                className={`flex-1 p-3 rounded-md text-center cursor-pointer transition-colors ${
                  isAnswered
                    ? (currentQuestion.answer === false 
                        ? (theme === 'dark' ? 'bg-green-600 text-white' : 'bg-green-100 text-green-800')
                        : (userAnswer === false
                            ? (theme === 'dark' ? 'bg-red-600 text-white' : 'bg-red-100 text-red-800')
                            : (theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800')))
                    : (userAnswer === false
                        ? (theme === 'dark' ? 'bg-gray-600 text-white' : 'bg-gray-300 text-gray-800')
                        : (theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'))
                }`}
              >
                False
              </div>
            </div>
          )}
          
          {/* Short answer implementation would go here */}
        </div>
        
        {/* Always show explanation after checking answer, even when navigating back */}
        {showFeedback && (
          <div className={`mt-6 p-4 rounded-md ${
            isCorrect 
              ? (theme === 'dark' ? 'bg-green-800/20 border border-green-600' : 'bg-green-50 border border-green-200') 
              : (theme === 'dark' ? 'bg-red-800/20 border border-red-600' : 'bg-red-50 border border-red-200')
          }`}>
            <h4 className={`font-medium mb-2 ${
              isCorrect 
                ? (theme === 'dark' ? 'text-green-400' : 'text-green-700') 
                : (theme === 'dark' ? 'text-red-400' : 'text-red-700')
            }`}>
              {isCorrect ? 'Correct!' : 'Incorrect'}
            </h4>
            <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>
              {currentQuestion.explanation}
            </p>
          </div>
        )}
        
        <div className="mt-6 flex justify-between">
          <button
            onClick={prevQuestion}
            disabled={currentQuestionIndex === 0}
            className={`px-4 py-2 rounded-md ${
              currentQuestionIndex === 0 
                ? (theme === 'dark' ? 'bg-[#1e1e1e] text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-400 cursor-not-allowed') 
                : (theme === 'dark' ? 'bg-[#1e1e1e] text-white hover:bg-[#303030]' : 'bg-gray-200 text-gray-800 hover:bg-gray-300')
            }`}
          >
            Previous
          </button>
          
          <button
            onClick={nextQuestion}
            className={`px-4 py-2 rounded-md ${
              theme === 'dark' 
                ? 'bg-[#1e1e1e] text-white hover:bg-[#303030]' 
                : 'bg-gray-500 text-white hover:bg-gray-600'
            }`}
          >
            {!showFeedback 
              ? 'Check Answer' 
              : (currentQuestionIndex < quiz.questions.length - 1 ? 'Next Question' : 'Finish Quiz')}
          </button>
        </div>
        
        {/* Quiz progress indicator */}
        <div className="mt-14 bg-gray-50 dark:bg-[#1e1e1e] p-6 rounded-2xl">
          <div className="flex justify-between mb-2">
            <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-muted-foreground'}`}>Progress</span>
            <span className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}>
              {Math.round((answerFeedback.filter(Boolean).length / quiz.questions.length * 100))}%
            </span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary rounded-full" 
              style={{ width: `${(answerFeedback.filter(Boolean).length / quiz.questions.length * 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between mt-2 text-xs text-muted-foreground">
            <span>{answerFeedback.filter(Boolean).length} correct</span>
            <span>{quiz.questions.length - currentQuestionIndex - (showFeedback ? 0 : 1)} remaining</span>
            <span>{quiz.questions.length} total</span>
          </div>
        </div>
      </div>
    );
  };
  
  // Updated render quiz results
  const renderQuizResults = () => {
    if (!quiz || !quiz.questions || !userAnswers) return null;
    
    return (
      <div className="w-full">
        <div className="p-6 rounded-lg bg-transparent dark:bg-transparent">
          <div className="text-center p-8 rounded-lg mb-6">
            <h2 className={`text-2xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
              Quiz Results
            </h2>
            
            <div className="mb-4">
              <div className="w-32 h-32 rounded-full flex items-center justify-center">
                <span className={`text-3xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
                  {Math.round((pointsEarned / totalPoints) * 100)}%
                </span>
              </div>
            </div>
            
            <p className={`text-xl ${pointsEarned >= (quiz.passing_score || 70) ? (theme === 'dark' ? 'text-green-400' : 'text-green-600') : (theme === 'dark' ? 'text-red-400' : 'text-red-600')}`}>
              {pointsEarned >= (quiz.passing_score || 70) ? 'Congratulations! You passed the quiz.' : 'You did not pass the quiz.'}
            </p>
            
            <div className={`text-lg mt-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
              <p>You answered {answerFeedback.filter(Boolean).length} out of {quiz.questions.length} questions correctly.</p>
              <p>Points earned: {pointsEarned} / {totalPoints}</p>
              <p>Passing score: {quiz.passing_score || 70}%</p>
            </div>
          </div>
          
         {/* --------------Question Review -----------------*/}

          <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
            Question Review
          </h3>
          <div className="space-y-6">
            {quiz.questions.map((question, index) => {
              const isCorrect = userAnswers[index] === question.answer;
              return (
                <div key={index} className={`p-4 rounded-md ${
                  isCorrect 
                    ? (theme === 'dark' ? 'bg-green-800/10' : 'bg-green-50/50') 
                    : (theme === 'dark' ? 'bg-red-800/10' : 'bg-red-50/50')
                }`}>
                  <p className={`font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
                    Question {index + 1}: {question.question}
                  </p>
                  
                  {question.type === 'multiple-choice' && question.options && (
                    <div className="ml-4 mb-2">
                      {question.options.map((option, optIndex) => (
                        <div key={optIndex} className={`py-1 ${
                          optIndex === question.answer
                            ? (theme === 'dark' ? 'text-green-400 font-medium' : 'text-green-700 font-medium')
                            : (userAnswers[index] === optIndex && userAnswers[index] !== question.answer
                                ? (theme === 'dark' ? 'text-red-400 line-through' : 'text-red-700 line-through')
                                : (theme === 'dark' ? 'text-gray-300' : 'text-gray-600'))
                        }`}>
                          {option} {optIndex === question.answer && '✓'}
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {question.type === 'true-false' && (
                    <div className="ml-4 mb-2">
                      <div className={`py-1 ${
                        question.answer === true
                          ? (theme === 'dark' ? 'text-green-400 font-medium' : 'text-green-700 font-medium')
                          : (userAnswers[index] === true
                              ? (theme === 'dark' ? 'text-red-400 line-through' : 'text-red-700 line-through')
                              : (theme === 'dark' ? 'text-gray-300' : 'text-gray-600'))
                      }`}>
                        True {question.answer === true && '✓'}
                      </div>
                      <div className={`py-1 ${
                        question.answer === false
                          ? (theme === 'dark' ? 'text-green-400 font-medium' : 'text-green-700 font-medium')
                          : (userAnswers[index] === false
                              ? (theme === 'dark' ? 'text-red-400 line-through' : 'text-red-700 line-through')
                              : (theme === 'dark' ? 'text-gray-300' : 'text-gray-600'))
                      }`}>
                        False {question.answer === false && '✓'}
                      </div>
                    </div>
                  )}
                  
                  <div className={`mt-2 ml-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                    <p className="font-medium">Explanation:</p>
                    <p>{question.explanation}</p>
                  </div>
                </div>
              );
            })}
          </div>
          
          <div className="mt-8 flex justify-between">
            <button
              onClick={resetQuiz}
              className={`px-4 py-2 rounded-md ${
                theme === 'dark' 
                  ? 'bg-gray-700 text-white hover:bg-gray-600' 
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              Retake Quiz
            </button>
            
            <button
              onClick={() => setQuizMode('quiz')}
              className={`px-4 py-2 rounded-md ${
                theme === 'dark' 
                  ? 'bg-[#1e1e1e] text-white hover:bg-[#1e1e1e]' 
                  : 'bg-gray-500 text-white hover:bg-gray-600'
              }`}
            >
              Back to Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  // Render quiz list view
  const renderQuizList = () => {
    if (loading) {
      return (
        <div className="flex-1 flex flex-col items-center justify-center">
          <Loader2 className="w-8 h-8 animate-spin mb-2" />
          <p>Loading...</p>
        </div>
      );
    }

    if (generatingQuiz) {
      return (
        <div className="flex-1 flex flex-col items-center justify-center space-y-4">
          <Loader2 className="w-10 h-10 animate-spin mb-2" />
          <p className="text-lg font-medium">Generating quizzes for all units...</p>
          {jobStatus && (
            <div className="w-full max-w-md bg-muted rounded-lg p-4">
              <p className="text-sm mb-2">Status: <span className="font-medium">{jobStatus}</span></p>
              <div className="w-full bg-background rounded-full h-2 mb-1">
                <div 
                  className="bg-gray-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${typeof jobStatus === 'string' && jobStatus.includes('%') 
                    ? parseInt(jobStatus.match(/\d+/)?.[0] || '0') 
                    : jobStatus === 'completed' ? 100 : 10}%` }}
                />
              </div>
              {jobStatus === 'completed' && (
                <p className="text-sm text-success flex items-center">
                  <CheckCircle className="w-4 h-4 mr-1" /> Complete
                </p>
              )}
            </div>
          )}
          <p className="text-sm text-muted-foreground max-w-md text-center">
            Quiz generation can take some time depending on the amount of content. 
            You can continue using other parts of the application while quizzes are being generated.
          </p>
        </div>
      );
    }
    
    if (error) {
      return (
        <div className="flex-1 flex flex-col items-center justify-center text-center">
          <XCircle className="w-8 h-8 text-destructive mb-2" />
          <p className="text-destructive">{error}</p>
          {error.includes('longer than expected') && (
            <div className="mt-4">
              <p className="text-sm text-muted-foreground mb-2">
                Don&apos;t worry! Your quizzes are still being generated in the background.
              </p>
              <button 
                className="px-4 py-2 rounded bg-gray-500 text-white flex items-center justify-center gap-2"
                onClick={() => {
                  if (selectedProject?.id) {
                    fetchUnitsForProject(selectedProject.id);
                    setError('');
                  }
                }}
              >
                <RefreshCw className="w-4 h-4" /> Refresh
              </button>
            </div>
          )}
        </div>
      );
    }

    // If a unit is selected and we're in quiz mode, render the current question
    if (selectedUnit && quizMode === 'quiz') {
      return renderCurrentQuestion();
    }

    // Fallback
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Loader2 size={36} className={`animate-spin mx-auto mb-3 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} />
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-sm`}>
            Loading...
          </p>
        </div>
      </div>
    );
  };

  const SubscriptionRequired = () => {
    const router = useRouter();
    
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Premium Content</h2>
        <p className="text-gray-600 mb-6">
          This content requires an active subscription to access.
        </p>
        <button
          onClick={() => router.push('/subscriptions/pricing')}
          className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
        >
          Upgrade Now
        </button>
      </div>
    );
  };

  // Navigate carousel
  const navigateCarousel = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCarouselIndex(prev => (prev > 0 ? prev - 1 : units.length - 1));
    } else {
      setCarouselIndex(prev => (prev < units.length - 1 ? prev + 1 : 0));
    }
  };

  // When carousel index changes, update selected unit
  useEffect(() => {
    if (units.length > 0 && carouselIndex >= 0 && carouselIndex < units.length) {
      const unit = units[carouselIndex];
      setSelectedUnit(unit);
      
      if (unit && unit.id) {
        fetchQuizForUnit(unit.id);
      }
    }
  }, [carouselIndex, units]);

  // When units are fetched, select the first one
  useEffect(() => {
    if (units.length > 0 && !selectedUnit) {
      setCarouselIndex(0); // This will trigger the useEffect above to set the selected unit
    }
  }, [units, selectedUnit]);

  // Main JSX rendering
  return (
    <div className="w-full h-full flex flex-col bg-white dark:bg-[hsl(0_0%_7.0%)]">
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main content area - simplified for more minimalistic UI */}
        <div className="flex-1 overflow-auto w-full mt-6 px-4">
          {error ? (
            error.includes('Subscription required') ? (
              <SubscriptionRequired />
            ) : (
              <div className={`p-4 m-2 rounded-md ${theme === 'dark' ? 'bg-red-900/20 text-red-200 border border-red-800/30' : 'bg-red-50 text-red-800 border border-red-200'}`}>
                {error}
              </div>
            )
          ) : loading ? (
            <div className="flex items-center justify-center h-full w-full">
              <div className="text-center">
                <Loader2 size={36} className={`animate-spin mx-auto mb-3 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} />
                <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} text-sm`}>
                  Loading quiz...
                </p>
              </div>
            </div>
          ) : quiz ? (
            <>
              {/* Quiz content */}
              <div className="w-full mx-auto">
                {showResults ? renderQuizResults() : renderCurrentQuestion()}
              </div>
            </>
          ) : (
            <div className="w-full mx-auto">
              {renderQuizList()}
            </div>
          )}
        </div>
        
        {/* Units carousel - minimalistic bottom navigation */}
        {selectedProject && units.length > 0 && (
          <div className="w-full flex-shrink-0 py-2 px-3 rounded-2xl border-gray-100 dark:border-gray-800 bg-white dark:bg-[#1e1e1e] sticky bottom-0 z-10 shadow-sm">
            {units.length > 0 && carouselIndex < units.length && (
              <div className="flex items-center justify-between">
                {/* Left navigation button */}
                <button 
                  onClick={() => navigateCarousel('prev')} 
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    theme === 'dark' 
                      ? `text-gray-400 hover:text-gray-200 hover:bg-gray-800 ${loading ? 'opacity-50' : ''}` 
                      : `text-gray-500 hover:text-gray-700 hover:bg-gray-100 ${loading ? 'opacity-50' : ''}`
                  }`}
                  disabled={units.length <= 1 || loading}
                  aria-label="Previous unit"
                >
                  <ChevronLeft size={18} />
                </button>
                
                {/* Center - unit info */}
                <div className="flex flex-col items-center">
                  <div className={`text-sm font-medium truncate max-w-[200px] sm:max-w-[300px] ${
                    loading
                      ? 'text-gray-400 dark:text-gray-500'
                      : 'text-gray-800 dark:text-white'
                  }`}>
                    {loading ? 'Loading...' : units[carouselIndex].title}
                  </div>
                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-0.5">
                    {loading ? '' : `${carouselIndex + 1} / ${units.length}`}
                  </div>
                </div>
                
                <button 
                  onClick={() => navigateCarousel('next')} 
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    theme === 'dark' 
                      ? `text-gray-400 hover:text-gray-200 hover:bg-gray-800 ${loading ? 'opacity-50' : ''}` 
                      : `text-gray-500 hover:text-gray-700 hover:bg-gray-100 ${loading ? 'opacity-50' : ''}`
                  }`}
                  disabled={units.length <= 1 || loading}
                  aria-label="Next unit"
                >
                  <ChevronRight size={18} />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
