package models

import (
	"time"

	"gorm.io/gorm"
)

// Usage limit constants
const (
	FreeUserDailyProjectLimit    = 1   // Free users can create 1 projects per day
	PremiumUserDailyProjectLimit = 100 // Premium users can create 100 projects per day
	FreeUserTotalProjectLimit    = 1   // Free users can have 1 projects total
)

// StripeCustomerSubscription combines customer and subscription data in a single table
type StripeCustomerSubscription struct {
	gorm.Model
	UserID           uint      `gorm:"uniqueIndex" json:"user_id"`
	CustomerID       string    `json:"customer_id"`
	SubscriptionID   string    `json:"subscription_id"`
	Status           string    `json:"status" gorm:"default:'none'"` // "active", "trialing", "past_due", "canceled", "none"
	PlanType         string    `json:"plan_type"`                    // "monthly" or "yearly"
	CurrentPeriodEnd time.Time `json:"current_period_end"`
	CancelAtPeriod   bool      `json:"cancel_at_period"`
}

// SubscriptionUsage tracks user project creation on a daily basis
type SubscriptionUsage struct {
	gorm.Model
	UserID               uint      `json:"user_id" gorm:"index"`
	Date                 time.Time `json:"date" gorm:"index"`
	ProjectsCreatedToday int       `json:"projects_created_today" gorm:"default:0"`
}

// GetSubscriptionStatusForUser returns the current subscription status for a user
func GetSubscriptionStatusForUser(db *gorm.DB, userID uint) (string, error) {
	// Use Find instead of First to avoid logging when record not found
	var subscriptions []StripeCustomerSubscription
	result := db.Where("user_id = ?", userID).Order("created_at DESC").Limit(1).Find(&subscriptions)

	// Check for real errors (not record not found)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		return "", result.Error
	}

	// If no records found, return none status
	if len(subscriptions) == 0 {
		return "none", nil
	}

	// Return the status of the first (and only) subscription
	return subscriptions[0].Status, nil
}

// CreateOrUpdateSubscription creates or updates a customer subscription record
func CreateOrUpdateSubscription(db *gorm.DB, userID uint, customerID, subscriptionID, status, planType string, periodEnd time.Time, cancelAtPeriod bool) error {
	var subscription StripeCustomerSubscription

	// Try to find existing record by user ID
	result := db.Where("user_id = ?", userID).First(&subscription)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// Create new combined record
			subscription = StripeCustomerSubscription{
				UserID:           userID,
				CustomerID:       customerID,
				SubscriptionID:   subscriptionID,
				Status:           status,
				PlanType:         planType,
				CurrentPeriodEnd: periodEnd,
				CancelAtPeriod:   cancelAtPeriod,
			}
			return db.Create(&subscription).Error
		}
		return result.Error
	}

	// Update existing record
	subscription.CustomerID = customerID
	subscription.SubscriptionID = subscriptionID
	subscription.Status = status
	subscription.PlanType = planType
	subscription.CurrentPeriodEnd = periodEnd
	subscription.CancelAtPeriod = cancelAtPeriod

	return db.Save(&subscription).Error
}

// GetCustomerIDForUser retrieves the Stripe customer ID for a user
func GetCustomerIDForUser(db *gorm.DB, userID uint) (string, error) {
	// Use Find instead of First to avoid logging when record not found
	var subscriptions []StripeCustomerSubscription
	result := db.Where("user_id = ?", userID).Order("created_at DESC").Limit(1).Find(&subscriptions)

	// Check for real errors
	if result.Error != nil {
		return "", result.Error
	}

	// If no records found, return empty string
	if len(subscriptions) == 0 {
		return "", nil
	}

	// Return the customer ID of the first subscription
	return subscriptions[0].CustomerID, nil
}

// GetUserDailyProjectLimit returns the maximum number of projects a user can create per day
// based on their subscription status
func GetUserDailyProjectLimit(db *gorm.DB, userID uint) (int, error) {
	// Get user's subscription status
	status, err := GetSubscriptionStatusForUser(db, userID)
	if err != nil {
		return 0, err
	}

	// Active subscribers get premium limit
	if status == "active" || status == "trialing" {
		return PremiumUserDailyProjectLimit, nil
	}

	// Free users get the free limit
	return FreeUserDailyProjectLimit, nil
}

// GetTodayUsage retrieves or creates today's usage record for a user
func GetTodayUsage(db *gorm.DB, userID uint) (*SubscriptionUsage, error) {
	today := time.Now().UTC().Truncate(24 * time.Hour) // Start of the day in UTC

	var usage SubscriptionUsage
	result := db.Where("user_id = ? AND date = ?", userID, today).First(&usage)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// Create a new usage record for today
			usage = SubscriptionUsage{
				UserID:               userID,
				Date:                 today,
				ProjectsCreatedToday: 0,
			}
			if err := db.Create(&usage).Error; err != nil {
				return nil, err
			}
			return &usage, nil
		}
		return nil, result.Error
	}

	return &usage, nil
}

// CanCreateProject checks if a user can create a new project based on their usage and limits
func CanCreateProject(db *gorm.DB, userID uint) (bool, error) {
	// Get the user's daily limit
	limit, err := GetUserDailyProjectLimit(db, userID)
	if err != nil {
		return false, err
	}

	// Get today's usage
	usage, err := GetTodayUsage(db, userID)
	if err != nil {
		return false, err
	}

	// Check if user has reached their daily limit
	return usage.ProjectsCreatedToday < limit, nil
}

// IncrementProjectUsage increments the project creation count for today
func IncrementProjectUsage(db *gorm.DB, userID uint) error {
	usage, err := GetTodayUsage(db, userID)
	if err != nil {
		return err
	}

	// Increment the count
	usage.ProjectsCreatedToday++

	// Save back to the database
	return db.Save(usage).Error
}

// GetRemainingDailyProjects returns the number of projects a user can still create today
func GetRemainingDailyProjects(db *gorm.DB, userID uint) (int, error) {
	// Get the user's daily limit
	limit, err := GetUserDailyProjectLimit(db, userID)
	if err != nil {
		return 0, err
	}

	// Get today's usage
	usage, err := GetTodayUsage(db, userID)
	if err != nil {
		return 0, err
	}

	// Calculate remaining projects
	remaining := limit - usage.ProjectsCreatedToday
	if remaining < 0 {
		remaining = 0
	}

	return remaining, nil
}
