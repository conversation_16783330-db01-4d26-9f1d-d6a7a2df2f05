package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Multilingual text
// e.g. {"en": "...", "es": "..."}
type LangText map[string]string

// Value implements the driver.Valuer interface for LangText
func (lt LangText) Value() (driver.Value, error) {
	return json.Marshal(lt)
}

// <PERSON><PERSON> implements the sql.Scanner interface for LangText
func (lt *LangText) Scan(value interface{}) error {
	if value == nil {
		*lt = LangText{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal LangText value: %v", value)
	}

	return json.Unmarshal(bytes, lt)
}

// Media content for a section
// e.g. simulate, watch, visualize, listen
// Visualize can be a string or a struct with more fields

type VisualizeContent struct {
	URL            string `json:"url,omitempty" gorm:"column:url"`
	Histopathology string `json:"histopathology,omitempty" gorm:"column:histopathology"`
	MRI            string `json:"mri,omitempty" gorm:"column:mri"`
	Credit         string `json:"credit,omitempty" gorm:"column:credit"`
	Segmentations  string `json:"segmentations,omitempty" gorm:"column:segmentations"`
}

// Value implements the driver.Valuer interface for VisualizeContent
func (vc VisualizeContent) Value() (driver.Value, error) {
	return json.Marshal(vc)
}

// Scan implements the sql.Scanner interface for VisualizeContent
func (vc *VisualizeContent) Scan(value interface{}) error {
	if value == nil {
		*vc = VisualizeContent{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal VisualizeContent value: %v", value)
	}

	return json.Unmarshal(bytes, vc)
}

type MediaContent struct {
	Simulate  *SimulateContent  `json:"simulate,omitempty" gorm:"column:simulate;type:jsonb"`
	Watch     string            `json:"watch,omitempty" gorm:"column:watch"`
	Visualize *VisualizeContent `json:"visualize,omitempty" gorm:"column:visualize;type:jsonb"`
	Listen    string            `json:"listen,omitempty" gorm:"column:listen"`
}

// Value implements the driver.Valuer interface for MediaContent
func (mc MediaContent) Value() (driver.Value, error) {
	return json.Marshal(mc)
}

// Scan implements the sql.Scanner interface for MediaContent
func (mc *MediaContent) Scan(value interface{}) error {
	if value == nil {
		*mc = MediaContent{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal MediaContent value: %v", value)
	}

	return json.Unmarshal(bytes, mc)
}

type SimulateContent struct {
	URL           string `json:"url" gorm:"column:url"`
	Segmentations string `json:"segmentations" gorm:"column:segmentations"`
}

// Section content for READ, AI_TUTOR, etc.
type SectionContent struct {
	Code LangText `json:"code" gorm:"column:code;type:jsonb"`
}

// Value implements the driver.Valuer interface for SectionContent
func (sc SectionContent) Value() (driver.Value, error) {
	return json.Marshal(sc)
}

// Scan implements the sql.Scanner interface for SectionContent
func (sc *SectionContent) Scan(value interface{}) error {
	if value == nil {
		*sc = SectionContent{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal SectionContent value: %v", value)
	}

	return json.Unmarshal(bytes, sc)
}

type Section struct {
	READ  *SectionContent `json:"READ,omitempty" gorm:"column:read;type:jsonb"`
	MEDIA *MediaContent   `json:"MEDIA,omitempty" gorm:"column:media;type:jsonb"`
}

// Value implements the driver.Valuer interface for Section
func (s Section) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for Section
func (s *Section) Scan(value interface{}) error {
	if value == nil {
		*s = Section{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal Section value: %v", value)
	}

	return json.Unmarshal(bytes, s)
}

// MedicalNote represents the structure of a medical note.
type MedicalNote struct {
	ID            string   `json:"id" gorm:"primaryKey"`
	Title         LangText `json:"title" gorm:"type:jsonb"`
	MedicalSystem string   `json:"medical_system" gorm:"column:medical_system"`
	Overview      struct {
		Code LangText `json:"code" gorm:"type:jsonb"`
	} `json:"overview" gorm:"embedded"`
	Sections map[string]Section `json:"sections" gorm:"type:jsonb"`
}

// TableName specifies the table name for MedicalNote
func (MedicalNote) TableName() string {
	return "medical_notes"
}
