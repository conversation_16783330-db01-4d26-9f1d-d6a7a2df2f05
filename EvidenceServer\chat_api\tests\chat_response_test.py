import unittest
import asyncio
import sys
from io import StringIO
from ..chat_ai.chat_response import chat_response, rank_articles
from ..chat_ai.chat_query import chat_query
class TestChatResponse(unittest.TestCase):
    def setUp(self):
        self.question = "What are the latest treatments for COVID-19?"
        self.chat_history = [
            {"user": "What is COVID-19?", "assistant": "COVID-19 is a respiratory illness caused by the SARS-CoV-2 virus..."},
            {"user": "What are the symptoms of COVID-19?", "assistant": "Common symptoms of COVID-19 include fever, cough, and fatigue..."}
        ]
        # Store original stdout for restoration
        self.original_stdout = sys.stdout

    async def async_test_chat_response(self):
        # Redirect stdout to capture output
        captured_output = StringIO()
        sys.stdout = captured_output

        try:
            # Test chat query generation
            print("\nGenerating search query...", flush=True)
            query = await chat_query(self.question, self.chat_history)
            print(f"Generated query: {query}", flush=True)
            
            # Print to both capture and real stdout
            print(captured_output.getvalue(), file=self.original_stdout, flush=True)
            captured_output.truncate(0)  # Clear the buffer
            
            self.assertIsNotNone(query)
            self.assertIsInstance(query, str)

            # Test ranking articles
            print("\nRanking articles...", flush=True)
            articles = await rank_articles(self.question, self.chat_history)
            print(f"\nNumber of ranked articles: {len(articles)}", flush=True)
            print(f"Top 3 article titles:", flush=True)
            for i, article in enumerate(articles[:3], 1):
                print(f"{i}. {article.get('title', 'No title')}", flush=True)
            
            # Print to both capture and real stdout
            print(captured_output.getvalue(), file=self.original_stdout, flush=True)
            captured_output.truncate(0)  # Clear the buffer
            
            self.assertIsNotNone(articles)
            self.assertIsInstance(articles, list)

            # Test chat response generation
            print("\nGenerating response:", flush=True)
            full_response = ""
            async for chunk in chat_response(self.question, self.chat_history, articles):
                print(chunk, end='', flush=True, file=self.original_stdout)
                full_response += chunk
                self.assertIsInstance(chunk, str)

            return full_response
        finally:
            sys.stdout = self.original_stdout

    def test_chat_response(self):
        # Run the async test using asyncio
        response = asyncio.run(self.async_test_chat_response())
        self.assertIsNotNone(response)

    def tearDown(self):
        # Restore original stdout
        sys.stdout = self.original_stdout

if __name__ == '__main__':
    unittest.main(verbosity=2)
