package subscriptions

import (
	"decodemed/models"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/subscription"
	"github.com/stripe/stripe-go/v76/webhook"
	"gorm.io/gorm"
)

// loadEnv loads environment variables from .env file
func loadEnv() error {
	// Try to find .env file in current directory and parent directories
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	for {
		envFile := filepath.Join(dir, ".env")
		if _, err := os.Stat(envFile); err == nil {
			// Found .env file, load it
			if err := godotenv.Load(envFile); err != nil {
				return fmt.Errorf("error loading .env file: %v", err)
			}
			return nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}

	return fmt.Errorf(".env file not found in current or parent directories")
}

// WebhookEventHandler processes a single Stripe webhook event
type WebhookEventHandler func(db *gorm.DB, event stripe.Event) error

// StripeWebhookHandler processes webhooks from Stripe
func StripeWebhookHandler(db *gorm.DB) gin.HandlerFunc {
	// Load environment variables
	if err := loadEnv(); err != nil {
		log.Printf("Warning: %v", err)
	}

	// Map of event types to their handlers - using string as key
	eventHandlers := map[string]WebhookEventHandler{
		"checkout.session.completed":    handleCheckoutSessionCompletedEvent,
		"customer.subscription.created": handleSubscriptionCreatedEvent,
		"customer.subscription.updated": handleSubscriptionUpdatedEvent,
		"customer.subscription.deleted": handleSubscriptionDeletedEvent,
		"invoice.payment_succeeded":     handleInvoicePaymentSucceededEvent,
		"invoice.payment_failed":        handleInvoicePaymentFailedEvent,
	}

	return func(c *gin.Context) {
		const MaxBodySize = int64(65536)

		// Read the request body
		body, err := io.ReadAll(io.LimitReader(c.Request.Body, MaxBodySize))
		if err != nil {
			log.Printf("Error reading request body: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
			return
		}

		// Get the signature header
		stripeSignature := c.GetHeader("Stripe-Signature")
		if stripeSignature == "" {
			log.Printf("No Stripe-Signature header received")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Missing Stripe-Signature header"})
			return
		}

		// Get webhook secret from environment
		endpointSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
		if endpointSecret == "" {
			log.Printf("Warning: STRIPE_WEBHOOK_SECRET not set")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Webhook secret not configured"})
			return
		}

		// Verify webhook signature with API version mismatch handling
		event, err := webhook.ConstructEventWithOptions(body, stripeSignature, endpointSecret, webhook.ConstructEventOptions{
			IgnoreAPIVersionMismatch: true,
		})
		if err != nil {
			log.Printf("Error verifying webhook signature: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to verify webhook signature"})
			return
		}

		// Get the event handler - Convert EventType to string for map lookup
		handler, exists := eventHandlers[string(event.Type)]

		// Log the event type
		log.Printf("Received webhook event of type: %s with ID: %s", event.Type, event.ID)

		if !exists {
			// No handler for this event type, but we still acknowledge it to Stripe
			log.Printf("No handler registered for event type: %s", event.Type)
			c.JSON(http.StatusOK, gin.H{"received": true, "status": "no_handler"})
			return
		}

		// Handle the event
		err = handler(db, event)
		if err != nil {
			log.Printf("Error handling webhook event: %v", err)
			// Still return 200 to acknowledge receipt to Stripe, but indicate processing error
			c.JSON(http.StatusOK, gin.H{
				"received": true,
				"status":   "processing_error",
				"error":    err.Error(),
			})
			return
		}

		// Return a success response to Stripe
		c.JSON(http.StatusOK, gin.H{"received": true, "status": "processed"})
	}
}

// handleCheckoutSessionCompletedEvent processes a checkout.session.completed event
func handleCheckoutSessionCompletedEvent(db *gorm.DB, event stripe.Event) error {
	var session stripe.CheckoutSession
	if err := json.Unmarshal(event.Data.Raw, &session); err != nil {
		return fmt.Errorf("error parsing checkout session: %v", err)
	}

	return handleCheckoutSessionCompleted(db, session)
}

// handleSubscriptionCreatedEvent processes a subscription created event
func handleSubscriptionCreatedEvent(db *gorm.DB, event stripe.Event) error {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		return fmt.Errorf("error parsing subscription: %v", err)
	}

	return handleSubscriptionCreated(db, subscription)
}

// handleSubscriptionUpdatedEvent processes subscription created or updated events
func handleSubscriptionUpdatedEvent(db *gorm.DB, event stripe.Event) error {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		return fmt.Errorf("error parsing subscription: %v", err)
	}

	return handleSubscriptionUpdated(db, subscription)
}

// handleSubscriptionDeletedEvent processes a subscription deleted event
func handleSubscriptionDeletedEvent(db *gorm.DB, event stripe.Event) error {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		return fmt.Errorf("error parsing subscription: %v", err)
	}

	return handleSubscriptionDeleted(db, subscription)
}

// handlePaymentIntentSucceededEvent processes a payment_intent.succeeded event
func handlePaymentIntentSucceededEvent(db *gorm.DB, event stripe.Event) error {
	// Just log for now, expand implementation if needed
	log.Printf("Payment intent succeeded: %s", event.ID)
	return nil
}

// handlePaymentIntentFailedEvent processes a payment_intent.payment_failed event
func handlePaymentIntentFailedEvent(db *gorm.DB, event stripe.Event) error {
	// Just log for now, expand implementation if needed
	log.Printf("Payment intent failed: %s", event.ID)
	return nil
}

// handleCustomerCreatedEvent processes the customer.created event
func handleCustomerCreatedEvent(db *gorm.DB, event stripe.Event) error {
	var customer stripe.Customer
	err := json.Unmarshal(event.Data.Raw, &customer)
	if err != nil {
		return fmt.Errorf("error parsing customer.created webhook: %v", err)
	}

	// Log the customer creation
	log.Printf("New Stripe customer created with ID: %s", customer.ID)

	// Extract user_id from metadata
	userIDStr, exists := customer.Metadata["user_id"]
	if !exists {
		return fmt.Errorf("customer %s has no user_id in metadata", customer.ID)
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid user_id in customer metadata: %v", err)
	}

	// Create or update the customer subscription record
	customerSubscription := models.StripeCustomerSubscription{
		UserID:     uint(userID),
		CustomerID: customer.ID,
		Status:     "none", // No subscription yet
	}

	// Use Upsert to create or update the record
	result := db.Where(models.StripeCustomerSubscription{UserID: uint(userID)}).
		Assign(models.StripeCustomerSubscription{CustomerID: customer.ID}).
		FirstOrCreate(&customerSubscription)

	if result.Error != nil {
		return fmt.Errorf("failed to store customer ID in database: %v", result.Error)
	}

	log.Printf("Successfully stored Stripe customer ID %s for user %d", customer.ID, userID)
	return nil
}

// handleInvoicePaymentSucceededEvent processes a invoice.payment_succeeded event
func handleInvoicePaymentSucceededEvent(db *gorm.DB, event stripe.Event) error {
	var invoice stripe.Invoice
	if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
		return fmt.Errorf("error parsing invoice: %v", err)
	}

	log.Printf("Processing invoice payment succeeded for subscription %s", invoice.Subscription.ID)

	// Update subscription with new period end
	if invoice.Subscription != nil {
		var customerSubscription models.StripeCustomerSubscription
		if err := db.Where("subscription_id = ?", invoice.Subscription.ID).First(&customerSubscription).Error; err != nil {
			// If we can't find the subscription, maybe it's a new one
			log.Printf("Could not find subscription %s in database: %v", invoice.Subscription.ID, err)
			// We'll let the subscription.updated event handle this
			return nil
		}

		// Update the subscription period end
		if len(invoice.Lines.Data) > 0 && invoice.Lines.Data[0].Period != nil {
			customerSubscription.CurrentPeriodEnd = time.Unix(invoice.Lines.Data[0].Period.End, 0)
			if err := db.Save(&customerSubscription).Error; err != nil {
				return fmt.Errorf("failed to update subscription period: %v", err)
			}
			log.Printf("Updated subscription period end to %v for subscription %s", customerSubscription.CurrentPeriodEnd, invoice.Subscription.ID)
		}
	}

	return nil
}

// handleInvoicePaymentFailedEvent processes a invoice.payment_failed event
func handleInvoicePaymentFailedEvent(db *gorm.DB, event stripe.Event) error {
	var invoice stripe.Invoice
	if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
		return fmt.Errorf("error parsing invoice: %v", err)
	}

	log.Printf("Processing invoice payment failed for subscription %s", invoice.Subscription.ID)

	// Update subscription status if we have a subscription ID
	if invoice.Subscription != nil {
		var customerSubscription models.StripeCustomerSubscription
		if err := db.Where("subscription_id = ?", invoice.Subscription.ID).First(&customerSubscription).Error; err != nil {
			log.Printf("Could not find subscription %s in database: %v", invoice.Subscription.ID, err)
			return nil
		}

		// Update the subscription status to past_due
		customerSubscription.Status = "past_due"
		if err := db.Save(&customerSubscription).Error; err != nil {
			return fmt.Errorf("failed to update subscription status: %v", err)
		}
		log.Printf("Updated subscription status to past_due for subscription %s", invoice.Subscription.ID)
	}

	return nil
}

// handleSubscriptionUpdated processes subscription updated events
func handleSubscriptionUpdated(db *gorm.DB, sub stripe.Subscription) error {
	// Find the customer associated with this subscription
	customerID := sub.Customer.ID
	if customerID == "" {
		return fmt.Errorf("subscription has no customer ID")
	}

	// Look up the customer mapping in our database
	var customerSubscription models.StripeCustomerSubscription

	// Try looking up by subscription ID first (most reliable)
	if err := db.Where("subscription_id = ?", sub.ID).First(&customerSubscription).Error; err != nil {
		// If not found by subscription ID, try customer ID
		if err := db.Where("customer_id = ?", customerID).First(&customerSubscription).Error; err != nil {
			// Could not find user for this customer
			log.Printf("Could not find customer subscription mapping for subscription %s with customer %s", sub.ID, customerID)

			// Let's see if there's a checkout.session.completed that has already processed this subscription
			// We'll return without an error to avoid retries
			return fmt.Errorf("could not find user for customer ID %s: record not found", customerID)
		}
	}

	// Determine the plan type
	planType := "unknown"
	if len(sub.Items.Data) > 0 {
		priceID := sub.Items.Data[0].Price.ID
		if priceID == os.Getenv("STRIPE_MONTHLY_PRICE_ID") {
			planType = "monthly"
		} else if priceID == os.Getenv("STRIPE_YEARLY_PRICE_ID") {
			planType = "yearly"
		}
	}

	// Create/update subscription record
	if err := models.CreateOrUpdateSubscription(
		db,
		customerSubscription.UserID,
		customerID,
		sub.ID,
		string(sub.Status),
		planType,
		time.Unix(sub.CurrentPeriodEnd, 0),
		sub.CancelAtPeriodEnd,
	); err != nil {
		return fmt.Errorf("failed to update subscription record: %v", err)
	}

	log.Printf("Successfully processed subscription update for user %d: %s (status: %s)", customerSubscription.UserID, sub.ID, sub.Status)
	return nil
}

// handleSubscriptionDeleted processes a subscription deleted event
func handleSubscriptionDeleted(db *gorm.DB, sub stripe.Subscription) error {
	// Find the customer associated with this subscription
	customerID := sub.Customer.ID
	if customerID == "" {
		return fmt.Errorf("subscription has no customer ID")
	}

	// Look up the user via Stripe customer ID
	var customerSubscription models.StripeCustomerSubscription
	if err := db.Where("customer_id = ?", customerID).First(&customerSubscription).Error; err != nil {
		return fmt.Errorf("could not find user for customer ID %s: %v", customerID, err)
	}

	// Mark the subscription as canceled in the database
	if err := models.CreateOrUpdateSubscription(
		db,
		customerSubscription.UserID,
		customerID,
		sub.ID,
		string(sub.Status), // Should be "canceled"
		"",                 // Empty plan type
		time.Unix(sub.CurrentPeriodEnd, 0),
		true, // Canceled
	); err != nil {
		return fmt.Errorf("failed to update subscription record: %v", err)
	}

	log.Printf("Successfully processed subscription deletion for user %d: %s", customerSubscription.UserID, sub.ID)
	return nil
}

// handleSubscriptionCreated processes a new subscription creation
func handleSubscriptionCreated(db *gorm.DB, sub stripe.Subscription) error {
	// Get customer ID
	customerID := sub.Customer.ID
	if customerID == "" {
		return fmt.Errorf("subscription has no customer ID")
	}

	// Look up the user via Stripe customer ID
	var customerSubscription models.StripeCustomerSubscription
	if err := db.Where("customer_id = ?", customerID).First(&customerSubscription).Error; err != nil {
		// If we can't find the customer/user mapping, try to get user_id from metadata
		userID := uint(0)

		// Method 1: Check customer metadata
		if sub.Customer.Metadata != nil {
			userIDStr, ok := sub.Customer.Metadata["user_id"]
			if ok {
				userIDVal, err := strconv.ParseUint(userIDStr, 10, 64)
				if err == nil {
					userID = uint(userIDVal)
					log.Printf("Found user_id %d in customer metadata", userID)
				}
			}
		}

		// Method 2: Check subscription metadata
		if userID == 0 && sub.Metadata != nil {
			userIDStr, ok := sub.Metadata["user_id"]
			if ok {
				userIDVal, err := strconv.ParseUint(userIDStr, 10, 64)
				if err == nil {
					userID = uint(userIDVal)
					log.Printf("Found user_id %d in subscription metadata", userID)
				}
			}
		}

		// Method 3: See if we can find a customer in our database
		if userID == 0 {
			// Try to find customer record by customer ID
			// Look for user ID in checkout sessions via client_reference_id
			// This would require an API call to Stripe which we can't do synchronously
			// But we can handle this in the checkout.session.completed event

			if userID == 0 {
				return fmt.Errorf("could not find user for customer ID %s: no customer mapping", customerID)
			}
		}

		// Create the customer subscription mapping
		customerSubscription = models.StripeCustomerSubscription{
			UserID:     userID,
			CustomerID: customerID,
		}

		if err := db.Create(&customerSubscription).Error; err != nil {
			return fmt.Errorf("failed to create customer mapping: %v", err)
		}

		log.Printf("Created new customer mapping for user %d with customer ID %s", userID, customerID)
	}

	// Determine the plan type
	planType := "unknown"
	if len(sub.Items.Data) > 0 {
		priceID := sub.Items.Data[0].Price.ID
		if priceID == os.Getenv("STRIPE_MONTHLY_PRICE_ID") {
			planType = "monthly"
		} else if priceID == os.Getenv("STRIPE_YEARLY_PRICE_ID") {
			planType = "yearly"
		}
	}

	// Create/update subscription record
	if err := models.CreateOrUpdateSubscription(
		db,
		customerSubscription.UserID,
		customerID,
		sub.ID,
		string(sub.Status),
		planType,
		time.Unix(sub.CurrentPeriodEnd, 0),
		sub.CancelAtPeriodEnd,
	); err != nil {
		return fmt.Errorf("failed to create subscription record: %v", err)
	}

	log.Printf("Successfully processed new subscription for user %d: %s (status: %s)", customerSubscription.UserID, sub.ID, sub.Status)
	return nil
}

// handleCheckoutSessionCompleted processes a successful checkout session
func handleCheckoutSessionCompleted(db *gorm.DB, session stripe.CheckoutSession) error {
	// Only process subscription checkouts
	if session.Mode != stripe.CheckoutSessionModeSubscription || session.Subscription == nil {
		return nil
	}

	// Get user from client reference ID (which we set to user ID when creating session)
	userID, err := strconv.ParseUint(session.ClientReferenceID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid user ID in client reference: %v", err)
	}

	// Ensure customer mapping exists in database (this is critical!)
	if session.Customer != nil && session.Customer.ID != "" {
		var customerSubscription models.StripeCustomerSubscription
		result := db.Where("user_id = ?", userID).First(&customerSubscription)

		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// Create new customer subscription record
				customerSubscription = models.StripeCustomerSubscription{
					UserID:     uint(userID),
					CustomerID: session.Customer.ID,
					Status:     "none", // Will be updated with subscription status
				}

				if err := db.Create(&customerSubscription).Error; err != nil {
					log.Printf("Failed to create customer mapping in checkout session: %v", err)
					// Continue anyway, we'll try to update the subscription below
				} else {
					log.Printf("Created customer mapping for user %d with customer ID %s in checkout session", userID, session.Customer.ID)
				}
			} else {
				log.Printf("Error finding customer subscription: %v", result.Error)
				// Continue anyway
			}
		} else if customerSubscription.CustomerID != session.Customer.ID {
			// Update customer ID if it's different
			customerSubscription.CustomerID = session.Customer.ID
			if err := db.Save(&customerSubscription).Error; err != nil {
				log.Printf("Failed to update customer ID: %v", err)
				// Continue anyway
			} else {
				log.Printf("Updated customer ID to %s for user %d", session.Customer.ID, userID)
			}
		}
	}

	// Now, retrieve the subscription to get current status and details
	sub, err := subscription.Get(session.Subscription.ID, nil)
	if err != nil {
		return fmt.Errorf("failed to retrieve subscription from Stripe: %v", err)
	}

	// Determine the plan type
	planType := "unknown"
	if len(sub.Items.Data) > 0 {
		priceID := sub.Items.Data[0].Price.ID
		if priceID == os.Getenv("STRIPE_MONTHLY_PRICE_ID") {
			planType = "monthly"
		} else if priceID == os.Getenv("STRIPE_YEARLY_PRICE_ID") {
			planType = "yearly"
		}
	}

	// Create/update subscription record with customer ID
	if err := models.CreateOrUpdateSubscription(
		db,
		uint(userID),
		session.Customer.ID,
		sub.ID,
		string(sub.Status),
		planType,
		time.Unix(sub.CurrentPeriodEnd, 0),
		sub.CancelAtPeriodEnd,
	); err != nil {
		return fmt.Errorf("failed to create subscription record: %v", err)
	}

	log.Printf("Successfully processed checkout session %s for user %d with subscription %s", session.ID, userID, sub.ID)
	return nil
}
