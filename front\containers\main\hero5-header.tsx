'use client'
import Link from 'next/link'
import Image from 'next/image'
import { Menu, X } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import React, { useEffect } from 'react'
import { cn } from '@/lib/utils'
import { SignInButton, SignUpButton } from "@clerk/nextjs"
import { useMainTheme } from '@/components/theme/main-theme-provider'
import { MainThemeToggle } from '@/components/theme/main-theme-toggle'
import { LanguageSwitcher } from '@/app/providers/language-switcher'

const menuItems = [
    { name: 'Blog', href: '/blog' },
]

export const HeroHeader = () => {
    const [menuState, setMenuState] = React.useState(false)
    const [isScrolled, setIsScrolled] = React.useState(false)
    const { theme } = useMainTheme()
    const isDarkMode = theme === 'dark'

    // Handle scroll detection
    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 50)
        }
        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [])

    return (
        <header className="dark:bg-[hsl(240_10%_3.9%)]">
            <nav
                data-state={menuState && 'active'}
                className="fixed z-20 w-full px-2">
                <div className={cn(
                    'mx-auto mt-2 max-w-6xl px-4 sm:px-6 transition-all duration-300 lg:px-8 xl:px-12', 
                    isScrolled && 'bg-white/90 dark:bg-[hsl(240_10%_3.9%)]/90 max-w-[90%] sm:max-w-4xl rounded-xl sm:rounded-2xl border backdrop-blur-lg lg:px-5'
                )}>
                    <div className="relative flex flex-wrap items-center justify-between gap-4 py-2 sm:py-3 lg:gap-0 lg:py-4">
                        <div className="flex w-full justify-between lg:w-auto">
                            <Link
                                href="/"
                                aria-label="home"
                                className="flex items-center space-x-2">
                                <Image
                                    src={isDarkMode ? "/decodemed_dark.svg" : "/decodemed.svg"}
                                    alt="DecodeMed Logo"
                                    width={32}
                                    height={32}
                                    priority={true}
                                    className="mt-1"
                                />
                                <Image 
                                    src={isDarkMode ? "/decodemed_text_dark.svg" : "/decodemed_text.svg"}
                                    alt="DecodeMed Text"
                                    width={130}
                                    height={45}
                                    priority={true}
                                    className="mt-2"
                                />
                            </Link>

                            <button
                                onClick={() => setMenuState(!menuState)}
                                aria-label={menuState == true ? 'Close Menu' : 'Open Menu'}
                                className="relative z-20 -m-2.5 -mr-4 block cursor-pointer p-2.5 lg:hidden">
                                <Menu className="in-data-[state=active]:rotate-180 in-data-[state=active]:scale-0 in-data-[state=active]:opacity-0 m-auto size-5 sm:size-6 duration-200" />
                                <X className="in-data-[state=active]:rotate-0 in-data-[state=active]:scale-100 in-data-[state=active]:opacity-100 absolute inset-0 m-auto size-5 sm:size-6 -rotate-180 scale-0 opacity-0 duration-200" />
                            </button>
                        </div>

                        <div className="absolute inset-0 m-auto hidden size-fit lg:block">
                            <ul className="flex gap-6 lg:gap-8 text-sm">
                                {menuItems.map((item, index) => (
                                    <li key={index}>
                                        <Link
                                            href={item.href}
                                            className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100 block duration-150">
                                            <span>{item.name}</span>
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        <div className={cn(
                            "bg-background mb-6 hidden w-full flex-wrap items-center justify-end space-y-8 rounded-3xl border p-6 shadow-2xl shadow-zinc-300/20 md:flex-nowrap lg:m-0 lg:flex lg:w-fit lg:gap-6 lg:space-y-0 lg:border-transparent lg:bg-transparent lg:p-0 lg:shadow-none dark:bg-[hsl(240_10%_3.9%)] dark:shadow-none dark:lg:bg-transparent",
                            menuState && "block lg:flex"
                        )}>
                            <div className="lg:hidden">
                                <ul className="space-y-4 sm:space-y-6 text-sm sm:text-base">
                                    {menuItems.map((item, index) => (
                                        <li key={index}>
                                            <Link
                                                href={item.href}
                                                className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-300 dark:hover:text-neutral-100 block duration-150">
                                                <span>{item.name}</span>
                                            </Link>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <div className="flex flex-col space-y-3 sm:flex-row sm:gap-3 sm:space-y-0 md:w-fit">
                                {/* Only show MainThemeToggle in desktop mode or mobile menu */}
                                <div className={cn("flex items-center gap-2", !menuState && "hidden lg:flex")}>
                                    <MainThemeToggle />
                                    <LanguageSwitcher />
                                </div>
                                <SignInButton>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className={cn(
                                            "text-sm sm:text-base font-semibold transition-all duration-300",
                                            isDarkMode 
                                                ? "bg-transparent hover:bg-neutral-800" 
                                                : "bg-white hover:bg-neutral-100",
                                            isScrolled && 'lg:hidden'
                                        )}>
                                        <span>Sign In</span>
                                    </Button>
                                </SignInButton>
                                <SignUpButton>
                                    <Button
                                        size="sm"
                                        className={cn(
                                            "text-sm sm:text-base font-semibold transition-all duration-300",
                                            isDarkMode 
                                                ? "hover:bg-neutral-800" 
                                                : "hover:bg-neutral-800",
                                            isScrolled && 'lg:hidden'
                                        )}>
                                        <span>Try for free</span>
                                    </Button>
                                </SignUpButton>
                                <SignUpButton>
                                    <Button
                                        size="sm"
                                        className={cn(
                                            "text-sm sm:text-base font-semibold transition-all duration-300",
                                            isDarkMode 
                                                ? "hover:bg-neutral-800" 
                                                : "hover:bg-neutral-800",
                                            isScrolled ? 'lg:inline-flex' : 'hidden'
                                        )}>
                                        <span>Get Started</span>
                                    </Button>
                                </SignUpButton>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </header>
    )
}
