package models

import (
	"strings"
	"time"

	"gorm.io/gorm"
)

// SanitizeString removes null bytes and other problematic characters for database storage
func SanitizeString(input string) string {
	// Remove null bytes that cause PostgreSQL UTF-8 encoding errors
	return strings.ReplaceAll(input, "\x00", "")
}

// -------------------------------USER MODELS--------------------------------
// User represents a user in the system
type User struct {
	gorm.Model
	ClerkID   string     `gorm:"unique;not null" json:"clerk_id"`
	Email     string     `gorm:"unique;not null" json:"email"`
	MedSpaces []MedSpace `json:"med_spaces,omitempty"`
	Projects  []Project  `json:"projects,omitempty"` // Keeping for backward compatibility

	FreeProjectsCount int `gorm:"default:0" json:"free_projects_count"`
}

// -------------------------------MEDSPACE MODELS--------------------------------
// MedSpace represents a collection of related projects
type MedSpace struct {
	gorm.Model
	Name        string    `json:"name"`
	UserID      uint      `json:"user_id"`
	User        User      `json:"-" gorm:"foreignKey:UserID"`
	Projects    []Project `json:"projects,omitempty"`
	Description string    `json:"description,omitempty"`
}

// -------------------------------PROJECT MODELS--------------------------------
// ContentType represents the type of educational content
type ContentType string

const (
	TypeDocument ContentType = "document"
	TypeYouTube  ContentType = "youtube"
)

// Project represents a study project
type Project struct {
	gorm.Model
	Name                  string      `json:"name"`
	UserID                uint        `json:"user_id"`
	MedSpaceID            *uint       `json:"med_space_id"`
	FileURL               string      `json:"file_url"`
	ThumbnailURL          string      `json:"thumbnail_url"`
	Type                  ContentType `json:"type" gorm:"default:'document'"`
	UnitsGenerated        bool        `json:"units_generated" gorm:"default:false"`
	NumUnits              int         `json:"num_units" gorm:"default:6"`
	YouTubeUnitsGenerated bool        `json:"youtube_units_generated" gorm:"default:false"`
	QuizzesGenerated      bool        `json:"quizzes_generated" gorm:"default:false"`
	FlashcardsGenerated   bool        `json:"flashcards_generated" gorm:"default:false"`
	Units                 []Unit      `json:"units,omitempty" gorm:"constraint:OnDelete:CASCADE;"`
}

// -------------------------------UNIT MODELS--------------------------------
// Unit represents any type of study unit within a project
type Unit struct {
	gorm.Model
	Title      string      `json:"title"`
	ProjectID  uint        `json:"project_id"`
	SourceText string      `json:"source_text"`
	Type       ContentType `json:"type"`

	// Content fields
	Content        string `json:"content,omitempty"`         // Used for document-type units
	YoutubeSummary string `json:"youtube_summary,omitempty"` // Used for YouTube-type units

	// Common components
	Mindmap *Mindmap `json:"mindmap,omitempty"`
	Quiz    *Quiz    `json:"quiz,omitempty"`
}

// -------------------------------COMPONENTS MODELS--------------------------------
// Mindmap represents a mindmap for a unit
type Mindmap struct {
	gorm.Model
	UnitID uint   `json:"unit_id"`
	Data   string `json:"data"` // Stores the complete React Flow data structure as a JSON string
}

// Quiz represents a set of questions and answers for a unit
type Quiz struct {
	gorm.Model
	UnitID       uint   `json:"unit_id"`
	Questions    string `json:"questions"`                         // Stores the questions and answers as a JSON string
	UserProgress string `json:"user_progress" gorm:"default:'{}'"` // Stores user's answers and progress as JSON
	MaxScore     int    `json:"max_score" gorm:"default:100"`      // Maximum possible score for the quiz
	PassingScore int    `json:"passing_score" gorm:"default:70"`   // Score needed to pass the quiz (percentage)
}

// -------------------------------FLASHCARD MODELS--------------------------------
// Flashcard represents a study card with spaced repetition parameters
type Flashcard struct {
	gorm.Model
	UnitID         uint       `json:"unit_id" gorm:"index"`
	Unit           Unit       `json:"-" gorm:"foreignKey:UnitID"`
	Question       string     `json:"question" gorm:"type:text"`
	Answer         string     `json:"answer" gorm:"type:text"`
	DifficultyRank int        `json:"difficulty_rank"` // 1-5 scale
	Tags           []string   `json:"tags" gorm:"type:jsonb"`
	LastReviewed   *time.Time `json:"last_reviewed,omitempty"`
	NextReview     *time.Time `json:"next_review,omitempty" gorm:"index"`
	ReviewCount    int        `json:"review_count"`
	Stability      float64    `json:"stability"`      // FSRS parameter
	Difficulty     float64    `json:"difficulty"`     // FSRS parameter
	ElapsedDays    float64    `json:"elapsed_days"`   // FSRS parameter
	ScheduledDays  float64    `json:"scheduled_days"` // FSRS parameter
	Retrievability float64    `json:"retrievability"` // FSRS probability of recall
}

// FlashcardReview represents a review session for a flashcard
type FlashcardReview struct {
	gorm.Model
	FlashcardID    uint      `json:"flashcard_id" gorm:"index"`
	Flashcard      Flashcard `json:"-" gorm:"foreignKey:FlashcardID"`
	Grade          int       `json:"grade"`                    // 0-5 scale
	ElapsedDays    float64   `json:"elapsed_days"`             // Days since last review
	Stability      float64   `json:"stability"`                // Stability at review time
	Difficulty     float64   `json:"difficulty"`               // Difficulty at review time
	Retrievability float64   `json:"retrievability"`           // Probability of recall at review time
	ReviewTime     time.Time `json:"review_time" gorm:"index"` // When the review occurred
	ResponseTime   float64   `json:"response_time"`            // Time taken to respond in seconds
	Correct        bool      `json:"correct"`                  // Whether the answer was correct (grade >= 3)
}
