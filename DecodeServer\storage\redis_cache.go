package storage

import (
	"context"
	"fmt"
	"strings"
	"time"

	"os"

	"github.com/go-redis/redis/v8"
	"github.com/joho/godotenv"
)

// RedisCacheService provides caching functionality using Redis
type RedisCacheService struct {
	client *redis.Client
}

var (
	// Global Redis client instance
	redisClient      *redis.Client
	redisInitialized bool
)

// InitRedisClient initializes the Redis client singleton
func InitRedisClient() error {
	if redisInitialized {
		return nil
	}

	// Load env variables
	if err := godotenv.Load(); err != nil {
		fmt.Printf("Warning: Error loading .env file: %v. Will try to use environment variables directly.\n", err)
	}

	redisURL := os.Getenv("REDIS_URL")
	redisPassword := os.Getenv("REDIS_PASSWORD")

	if redisURL == "" {
		return fmt.Errorf("REDIS_URL environment variable is not set")
	}

	fmt.Printf("Redis config - URL: %s, Password set: %v\n",
		redisURL, redisPassword != "")

	// Parse the Redis URL to extract host and port
	// Format is typically: redis://username:password@host:port
	var addr string

	// Check if the URL has the redis:// prefix
	if strings.HasPrefix(redisURL, "redis://") {
		// Remove the redis:// prefix
		redisURL = strings.TrimPrefix(redisURL, "redis://")

		// Split by @ to separate credentials from host:port
		parts := strings.Split(redisURL, "@")
		if len(parts) > 1 {
			// Use the host:port part
			addr = parts[len(parts)-1]

			// If no password was provided separately, extract it from the URL
			if redisPassword == "" && len(parts) > 1 && strings.Contains(parts[0], ":") {
				credParts := strings.Split(parts[0], ":")
				if len(credParts) > 1 {
					redisPassword = credParts[len(credParts)-1]
					fmt.Println("Using password from Redis URL")
				}
			}
		} else {
			addr = redisURL
		}
	} else {
		// If no redis:// prefix, assume it's just host:port
		addr = redisURL
	}

	fmt.Printf("Connecting to Redis at: %s\n", addr)

	// Create Redis client options with improved timeouts
	opts := &redis.Options{
		Addr:         addr,
		Password:     redisPassword,
		DB:           0, // Default DB
		DialTimeout:  30 * time.Second,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		PoolSize:     10,
		PoolTimeout:  30 * time.Second,
	}

	// Create Redis client
	redisClient = redis.NewClient(opts)

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pong, err := redisClient.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("failed to connect to Redis: %v", err)
	}

	fmt.Printf("Successfully connected to Redis: %s\n", pong)
	redisInitialized = true
	return nil
}

// NewRedisCacheService creates a new Redis cache service
func NewRedisCacheService() (*RedisCacheService, error) {
	if err := InitRedisClient(); err != nil {
		return nil, err
	}

	return &RedisCacheService{
		client: redisClient,
	}, nil
}

// GetRedisClient returns the singleton Redis client
func GetRedisClient() (*redis.Client, error) {
	if err := InitRedisClient(); err != nil {
		return nil, err
	}
	return redisClient, nil
}

// Store stores data in Redis with an optional expiration time
func (r *RedisCacheService) Store(ctx context.Context, key string, data []byte, expiration time.Duration) error {
	// Use a timeout context for the operation
	storeCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	fmt.Printf("Storing %d bytes in Redis with key: %s\n", len(data), key)
	err := r.client.Set(storeCtx, key, data, expiration).Err()
	if err != nil {
		fmt.Printf("ERROR storing data in Redis: %v\n", err)
		return err
	}
	fmt.Printf("Successfully stored data in Redis key: %s\n", key)
	return nil
}

// Retrieve retrieves data from Redis
func (r *RedisCacheService) Retrieve(ctx context.Context, key string) ([]byte, error) {
	val, err := r.client.Get(ctx, key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("key not found: %s", key)
		}
		return nil, err
	}
	return val, nil
}

// Delete removes data from Redis
func (r *RedisCacheService) Delete(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

// Close closes the Redis client connection
func (r *RedisCacheService) Close() error {
	// Don't actually close the global client, just return nil
	return nil
}
