import React, { useState } from 'react';
import axios from 'axios';
import { MDXRemote, MDXRemoteSerializeResult } from 'next-mdx-remote';
import { serialize } from 'next-mdx-remote/serialize';
import remarkGfm from 'remark-gfm';
import remarkEmoji from 'remark-emoji';
import { Sparkles, ChevronUp, ChevronDown } from 'lucide-react';

const API_URL = process.env.NEXT_PUBLIC_API_URL;

interface Article {
  title: string;
  // Add other article properties as needed
}

interface EvidenceReportProps {
  article: Article;
  togglePyramidVisibility: (title: string, force?: boolean) => void;
}

interface EvidenceReport {
  title: string;
  report: string;
  mdxSource: MDXRemoteSerializeResult;
}

export const EvidenceReport: React.FC<EvidenceReportProps> = ({ article, togglePyramidVisibility }) => {
  const [evidenceReports, setEvidenceReports] = useState<EvidenceReport[]>([]);
  const [visibleReports, setVisibleReports] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateEvidenceReport = async () => {
    if (evidenceReports.some(report => report.title === article.title)) {
      toggleReportVisibility(article.title);
    } else {
      setIsLoading(true);
      try {
        const response = await axios.post(`${API_URL}/search-api/evidence-report/`, { article });
        const newEvidenceReport = response.data.evidence_report;
        const serializedMdx = await serialize(newEvidenceReport.report, {
          mdxOptions: { remarkPlugins: [remarkGfm, remarkEmoji] },
        });
        setEvidenceReports(prev => [...prev, { ...newEvidenceReport, mdxSource: serializedMdx }]);
        setVisibleReports(prev => ({ ...prev, [article.title]: true }));
        
        // Close the pyramid when opening the report
        togglePyramidVisibility(article.title, false);
      } catch (err) {
        console.error('Evidence report generation error:', err);
        setError(`An error occurred while generating evidence report: ${(err as Error).message}`);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const toggleReportVisibility = (title: string) => {
    setVisibleReports(prev => {
      const newState = { ...prev, [title]: !prev[title] };
      // Close the pyramid for this article if opening the report
      if (newState[title]) {
        togglePyramidVisibility(title);
      }
      return newState;
    });
  };

  return (
    <div >
      <button
        onClick={handleGenerateEvidenceReport}
        className="text-sm font-semibold py-2 px-1 rounded transition duration-300 flex items-center dark:bg-neutral-800 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:hover:text-white bg-white text-gray-900 hover:bg-gray-700 hover:text-white"
      >
        <Sparkles className="mr-2" size={14} />
        {evidenceReports.some(report => report.title === article.title) ? (
          <>
            {visibleReports[article.title] ? (
              <>Evidence Report <ChevronUp className="ml-2" /></>
            ) : (
              <>Evidence Report <ChevronDown className="ml-2" /></>
            )}
          </>
        ) : (
          <>Evidence Report</>
        )}
      </button>

      {/* Report content in its own container */}
      {(isLoading || error || (evidenceReports.some(report => report.title === article.title) && visibleReports[article.title])) && (
        <div className="mt-6 p-0 dark:bg-neutral-800 bg-gray-100">
         
          {error && <div className="text-red-500">{error}</div>}
          
          {evidenceReports.some(report => report.title === article.title) && visibleReports[article.title] && (
            <MDXRemote
              {...evidenceReports.find(report => report.title === article.title)!.mdxSource}
              components={{
                table: (props) => (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse bg-white text-xs" {...props} />
                  </div>
                ),
                thead: (props) => <thead className="dark:bg-neutral-700 bg-gray-200 border text-center dark:border-neutral-600 border-white" {...props} />,
                th: (props) => <th className="border dark:bg-neutral-800 bg-gray-100 dark:border-neutral-600 border-white p-0.5 text-left font-semibold dark:text-neutral-200 text-gray-900" {...props} />,
                td: (props) => <td className="border dark:bg-neutral-800 bg-gray-100 dark:border-neutral-600 border-white p-0.5 dark:text-neutral-300 text-gray-800" {...props} />,
                tr: (props) => <tr className="dark:hover:bg-neutral-700 hover:bg-gray-200" {...props} />,
                p: (props) => <p className="mt-2 mb-2 text-sm font-semibold dark:text-neutral-300 text-gray-800" {...props} />,
                strong: (props) => <strong className="font-semibold dark:text-blue-400 text-blue-900" {...props} />,
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};
