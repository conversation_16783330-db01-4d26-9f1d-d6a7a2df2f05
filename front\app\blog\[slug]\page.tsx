import { MainThemeProvider } from '@/components/theme/main-theme-provider';
import { HeroHeader } from '@/containers/main/hero5-header';
import { notFound } from 'next/navigation';
import { ClientBlogPost } from './client-components';
import { posts, PostData } from './posts-data';

// Define interface for params
interface BlogParams {
  slug: string;
}

// Generate an array of metadata for all available posts
export function generateStaticParams() {
  return posts.map(post => ({
    slug: post.slug,
  }));
}

// Add generateMetadata function to handle the metadata
export async function generateMetadata({
  params,
}: {
  params: Promise<BlogParams>
}) {
  const { slug } = await params;
  
  // Find the post
  const post = posts.find((p) => p.slug === slug);
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.title,
    description: 'Learn about the latest in medical education and study techniques.',
    openGraph: {
      title: post.title,
      description: 'Explore DecodeMed insights on medical education and studying techniques.',
      url: `https://decodemed.com/blog/${post.slug}`,
      images: [
        {
          url: post.ogImage,
          width: 1200,
          height: 630,
        },
      ],
      siteName: 'DecodeMed',
      locale: 'en_US',
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: 'Explore DecodeMed insights on medical education and studying techniques.',
      images: [post.ogImage],
    },
  };
}

// Main blog post page component
export default async function BlogPostPage({
  params,
}: {
  params: Promise<BlogParams>
}) {
  const { slug } = await params;
  
  // Find the post
  const post: PostData | undefined = posts.find((p) => p.slug === slug);
  
  if (!post) {
    notFound();
  }
  
  return (
    <MainThemeProvider>
      <HeroHeader />
      <ClientBlogPost post={post} />
    </MainThemeProvider>
  );
}