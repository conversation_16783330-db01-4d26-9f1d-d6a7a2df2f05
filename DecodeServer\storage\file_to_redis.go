package storage

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"path/filepath"
	"time"

	"github.com/google/uuid"
)

// FileRedisService handles file content storage in Redis
type FileRedisService struct {
	redisCacheService *RedisCacheService
}

// Maximum file chunk size (5MB)
const maxChunkSize = 5 * 1024 * 1024

// NewFileRedisService creates a new file Redis service
func NewFileRedisService() (*FileRedisService, error) {
	redisCacheService, err := NewRedisCacheService()
	if err != nil {
		return nil, fmt.Errorf("failed to create Redis cache service: %v", err)
	}

	return &FileRedisService{
		redisCacheService: redisCacheService,
	}, nil
}

// StoreFileContent stores file content in Redis
// Returns a unique key that can be used to retrieve the content
func (f *FileRedisService) StoreFileContent(ctx context.Context, fileURL string, content []byte) (string, error) {
	// Generate a unique key using UUID and the file name
	fileName := filepath.Base(fileURL)
	uniqueID := uuid.New().String()
	redisKey := fmt.Sprintf("file:%s:%s", uniqueID, fileName)

	log.Printf("Storing file in Redis: %s (size: %d bytes)", redisKey, len(content))

	// Store the content in Redis with a 24-hour expiration
	storeCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	err := f.redisCacheService.Store(storeCtx, redisKey, content, 24*time.Hour)
	if err != nil {
		log.Printf("Error storing file content in Redis: %v", err)
		return "", fmt.Errorf("failed to store file content in Redis: %v", err)
	}

	log.Printf("Successfully stored file in Redis: %s", redisKey)
	return redisKey, nil
}

// DownloadToRedis downloads a file from storage and stores it in Redis
// Returns a unique key that can be used to retrieve the content
func (f *FileRedisService) DownloadToRedis(ctx context.Context, fileURL string) (string, error) {
	log.Printf("Downloading file to Redis from URL: %s", fileURL)

	// Get storage client
	storageClient, err := GetStorage(ctx)
	if err != nil {
		log.Printf("Error getting storage client: %v", err)
		return "", fmt.Errorf("failed to get storage client: %v", err)
	}
	defer storageClient.Close()

	// Extract object name from URL
	objectName := ExtractObjectNameFromURL(fileURL)
	if objectName == "" {
		log.Printf("Error extracting object name from URL: %s", fileURL)
		return "", fmt.Errorf("failed to extract object name from URL: %s", fileURL)
	}

	log.Printf("Extracted object name: %s", objectName)

	// Get the bucket directly from the Google storage client
	googleStorage, ok := storageClient.(*GoogleCloudStorage)
	if !ok {
		log.Printf("Error: storage client is not a GoogleCloudStorage instance")
		return "", fmt.Errorf("storage client is not a GoogleCloudStorage instance")
	}

	// Get a handle to the object
	object := googleStorage.bucket.Object(objectName)

	// Create a reader for the object with a longer timeout context
	downloadCtx, cancel := context.WithTimeout(ctx, 120*time.Second)
	defer cancel()

	log.Printf("Initiating download from storage: %s", objectName)
	reader, err := object.NewReader(downloadCtx)
	if err != nil {
		log.Printf("Error creating reader for object: %v", err)
		return "", fmt.Errorf("failed to create reader for object: %v", err)
	}
	defer reader.Close()

	// Use a buffer to read data
	var buffer bytes.Buffer

	// Use io.Copy to efficiently transfer data
	log.Printf("Reading object content from storage...")
	n, err := io.Copy(&buffer, reader)
	if err != nil {
		log.Printf("Error reading object content: %v", err)
		return "", fmt.Errorf("failed to read object content: %v", err)
	}

	content := buffer.Bytes()
	log.Printf("Successfully read %d bytes from storage. Starting Redis upload...", n)

	// Store the content in Redis
	redisKey, err := f.StoreFileContent(ctx, fileURL, content)
	if err != nil {
		log.Printf("Error storing content in Redis: %v", err)
		return "", fmt.Errorf("failed to store content in Redis: %v", err)
	}

	log.Printf("Successfully uploaded file to Redis with key: %s", redisKey)
	return redisKey, nil
}

// GetFileContent retrieves file content from Redis
func (f *FileRedisService) GetFileContent(ctx context.Context, redisKey string) ([]byte, error) {
	log.Printf("Retrieving file content from Redis: %s", redisKey)

	// Retrieve the content from Redis with extended timeout
	retrieveCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	content, err := f.redisCacheService.Retrieve(retrieveCtx, redisKey)
	if err != nil {
		log.Printf("Error retrieving file content from Redis: %v", err)
		return nil, fmt.Errorf("failed to retrieve file content from Redis: %v", err)
	}

	log.Printf("Successfully retrieved file content from Redis: %s (size: %d bytes)", redisKey, len(content))
	return content, nil
}

// GetFileName extracts the file name from a Redis key
func (f *FileRedisService) GetFileName(redisKey string) string {
	// Redis key format: "file:<uuid>:<filename>"
	parts := filepath.SplitList(redisKey)
	if len(parts) > 2 {
		return parts[2]
	}
	return ""
}
