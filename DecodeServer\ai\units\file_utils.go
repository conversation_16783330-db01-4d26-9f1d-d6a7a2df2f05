package units

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"decodemed/storage" // Fix import path to match your project structure
)

// DownloadAndExtractContent downloads a file from storage and extracts its content
// Using Redis exclusively for file content storage with minimal temporary files
func DownloadAndExtractContent(ctx context.Context, fileURL string) (content string, title string, redisKey string, err error) {
	// Initialize FileRedisService
	fileRedisService, err := storage.NewFileRedisService()
	if err != nil {
		return "", "", "", fmt.Errorf("failed to initialize Redis service: %w", err)
	}

	// Download file to Redis and get the key
	redisKey, err = fileRedisService.DownloadToRedis(ctx, fileURL)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to download file to Redis: %w", err)
	}

	// Get the file content from Redis
	fileBytes, err := fileRedisService.GetFileContent(ctx, redisKey)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to get file content from Redis: %w", err)
	}

	// Get filename and title from the URL
	fileName := filepath.Base(fileURL)
	title = strings.TrimSuffix(fileName, filepath.Ext(fileName))
	
	// We need to use a temporary file for extraction, but we'll use a mutex to ensure thread safety
	// and clean up immediately after use
	ext := strings.ToLower(filepath.Ext(fileName))
	
	// Use a mutex to ensure thread safety when creating temporary files
	var extractMutex sync.Mutex
	extractMutex.Lock()
	defer extractMutex.Unlock()
	
	// Create a temporary file with a unique name
	tempFile, err := ioutil.TempFile("", "redis-extract-*"+ext)
	if err != nil {
		return "", "", "", fmt.Errorf("failed to create temporary file: %w", err)
	}
	tempFilePath := tempFile.Name()
	tempFile.Close() // Close before writing
	
	// Ensure the temporary file is deleted after use
	defer os.Remove(tempFilePath)
	
	// Write the content to the temporary file
	if err := ioutil.WriteFile(tempFilePath, fileBytes, 0644); err != nil {
		return "", "", "", fmt.Errorf("failed to write to temporary file: %w", err)
	}
	
	// Process the file based on extension
	switch ext {
	case ".pdf":
		content, _, err = ExtractFromPDF(tempFilePath)
	case ".docx", ".doc":
		content, _, err = ExtractFromDOCX(tempFilePath)
	case ".txt", ".text", ".md", ".markdown", ".csv":
		content, _, err = ExtractFromTXT(tempFilePath)
	default:
		// For unknown file types, attempt to treat as text
		if isTextContent(fileBytes) {
			content = string(fileBytes)
		} else {
			err = errors.New("unsupported file type: " + ext)
		}
	}

	// If content is empty or too short, provide a fallback message
	if len(strings.TrimSpace(content)) < 50 {
		content = fmt.Sprintf("This is extracted content from the file: %s. "+
			"The file appears to contain limited text content that can be extracted programmatically. "+
			"The file may consist primarily of images or scanned content.", title)
	}

	return content, title, redisKey, nil
}

// isTextContent does a simple check to determine if byte content is likely text
func isTextContent(content []byte) bool {
	// If content is empty, it's not valid text
	if len(content) == 0 {
		return false
	}

	// Use at most the first 4KB to check
	bufferSize := 4096
	if len(content) < bufferSize {
		bufferSize = len(content)
	}
	buffer := content[:bufferSize]

	// Check if the content contains any null bytes (typical for binary files)
	for _, b := range buffer {
		if b == 0 {
			return false
		}
	}

	// Count printable ASCII characters
	printable := 0
	for _, b := range buffer {
		if (b >= 32 && b <= 126) || b == '\n' || b == '\r' || b == '\t' {
			printable++
		}
	}

	// If more than 90% of characters are printable, it's likely text content
	return float64(printable)/float64(len(buffer)) > 0.9
}

// CleanupRedisKey removes a file from Redis cache
func CleanupRedisKey(redisKey string) {
	if redisKey != "" {
		// Get Redis cache service
		redisCacheService, err := storage.NewRedisCacheService()
		if err != nil {
			return // Just log and return
		}
		
		// Delete the file from Redis
		ctx := context.Background()
		if err := redisCacheService.Delete(ctx, redisKey); err != nil {
			// Just log the error, don't return it
			fmt.Printf("Failed to delete file from Redis cache: %v\n", err)
		}
	}
}
