from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
import asyncio
from .chat_ai.chat_query import chat_query
from .chat_ai.chat_data import fetch_pubmed_articles, parse_articles
from .chat_ai.chat_response import chat_response
from .models import ChatEntry
import uuid
from .serializers import ChatEntrySerializer
from rest_framework.decorators import api_view
from rest_framework.response import Response

# Chat view
@csrf_exempt
@require_http_methods(["POST"])
async def chat_view(request):
    data = json.loads(request.body)
    user_id = data.get('userId')
    
    # Add user ID to request headers for middleware
    request.META['HTTP_X_USER_ID'] = user_id
    
    question = data.get('question')
    conversation_id = data.get('conversationId')
    chat_history = data.get('chat_history', [])

    if not conversation_id:
        return JsonResponse({'error': 'Conversation ID is required'}, status=400)

    # Generate PubMed search query
    query = await chat_query(question, chat_history)

    # Fetch PubMed articles
    fetch_data, error = await fetch_pubmed_articles(query)
    if error:
        return JsonResponse({'error': error}, status=400)

    # Parse articles
    articles = await parse_articles(fetch_data)

    # Generate chat response
    response = ""
    async for chunk in chat_response(question, chat_history, articles):
        response += chunk

    # Create new ChatEntry
    chat_entry = ChatEntry.objects.create(
        user_id=user_id,
        question=question,
        response=response,
        conversation_id=conversation_id,
        sources=articles
    )

    # Update chat history with the new query and response
    chat_history.append({'user': question, 'assistant': response, 'query': query})

    return JsonResponse({
        'response': response,
        'articles': articles,
        'chat_history': chat_history,
        'conversationId': conversation_id
    })

# Get user conversations

@api_view(['GET'])
def get_user_conversations(request):
    user_id = request.query_params.get('userId')
    
    if not user_id:
        return Response({'error': 'User ID is required'}, status=400)

    try:
        # Get unique conversations for the user
        conversations = ChatEntry.objects.filter(user_id=user_id).order_by('-created_at').distinct('conversation_id')
        
        # Serialize the data
        serializer = ChatEntrySerializer(conversations, many=True)
        
        # Extract only the necessary fields
        conversation_list = [
            {
                'conversation_id': entry['conversation_id'],
                'question': entry['question']
            }
            for entry in serializer.data
        ]

        return Response({'conversations': conversation_list})
    except Exception as e:
        return Response({'error': str(e)}, status=500)

# Get chat history
@require_http_methods(["GET"])
def get_chat_history(request):
    conversation_id = request.GET.get('conversationId')
    if not conversation_id:
        return JsonResponse({'error': 'Conversation ID is required'}, status=400)

    chat_entries = ChatEntry.objects.filter(conversation_id=conversation_id).order_by('created_at')
    serializer = ChatEntrySerializer(chat_entries, many=True)
    return JsonResponse({'chatHistory': serializer.data})
