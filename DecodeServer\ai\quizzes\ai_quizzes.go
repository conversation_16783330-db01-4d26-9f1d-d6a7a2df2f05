package quizzes

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/google/generative-ai-go/genai"
	"github.com/joho/godotenv"
	"google.golang.org/api/option"
)

// Generator handles quiz generation using AI
type Generator struct {
	client *genai.Client
	model  *genai.GenerativeModel
	config ModelConfig
}

// ModelConfig represents the configuration for the AI model
type ModelConfig struct {
	Model       string  // Model version to use
	Temperature float32 // Controls randomness (0.0 to 1.0)
	MaxTokens   int32   // Maximum tokens to generate
}

// DefaultConfig returns the default model configuration
func DefaultConfig() ModelConfig {
	return ModelConfig{
		Model:       "gemini-2.5-flash", // Using the pro version for better summaries
		Temperature: 0.2,              // Lower temperature for more deterministic summaries
		MaxTokens:   4000,             // Reasonable limit for summaries
	}
}

// loadEnv loads environment variables from .env file
func loadEnv() error {
	// Try to find .env file in current directory and parent directories
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	for {
		envFile := filepath.Join(dir, ".env")
		if _, err := os.Stat(envFile); err == nil {
			// Found .env file, load it
			if err := godotenv.Load(envFile); err != nil {
				return fmt.Errorf("error loading .env file: %v", err)
			}
			return nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}

	return fmt.Errorf(".env file not found in current or parent directories")
}

// NewGenerator creates a new quiz generator
func NewGenerator() (*Generator, error) {
	// Load environment variables from .env file
	if err := loadEnv(); err != nil {
		fmt.Printf("Warning: %v\n", err)
	}

	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("GEMINI_API_KEY environment variable is not set. Please add it to your .env file")
	}

	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %v", err)
	}

	config := DefaultConfig()
	model := client.GenerativeModel(config.Model)

	// Configure the model
	model.SetTemperature(float32(config.Temperature))
	model.SetMaxOutputTokens(int32(config.MaxTokens))

	return &Generator{
		client: client,
		model:  model,
		config: config,
	}, nil
}

// SetConfig updates the model configuration
func (g *Generator) SetConfig(config ModelConfig) {
	g.config = config
	g.model = g.client.GenerativeModel(config.Model)
	g.model.SetTemperature(float32(config.Temperature))
	g.model.SetMaxOutputTokens(int32(config.MaxTokens))
}

// GenerateQuiz generates quiz questions and answers from the given content
func (g *Generator) GenerateQuiz(ctx context.Context, title string, content string, sourceText string) (string, error) {
	prompt := g.buildPrompt(title, content, sourceText)

	response, err := g.generateContent(ctx, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate quiz: %v", err)
	}

	return response, nil
}

// generateContent sends a prompt to the AI model and returns the response
func (g *Generator) generateContent(ctx context.Context, prompt string) (string, error) {
	resp, err := g.model.GenerateContent(ctx, genai.Text(prompt))
	if err != nil {
		return "", err
	}

	if len(resp.Candidates) == 0 {
		return "", fmt.Errorf("no response generated")
	}

	var result strings.Builder
	for _, candidate := range resp.Candidates {
		if candidate.Content != nil {
			for _, part := range candidate.Content.Parts {
				result.WriteString(fmt.Sprintf("%v", part))
			}
		}
	}

	if result.Len() == 0 {
		return "", fmt.Errorf("empty response generated")
	}

	return result.String(), nil
}

// buildPrompt creates the prompt for the AI
func (g *Generator) buildPrompt(title string, content string, sourceText string) string {
	return fmt.Sprintf(`Generate a comprehensive multiple-choice quiz based on the following medical content titled "%s". 

Content:
%s

Please create 5-10 multiple-choice questions with the following structure:
1. Each question should have exactly 4 options with only one correct answer
2. Include the correct answer index (0-3) for each question
3. Ensure the questions test understanding rather than just memorization
4. Cover the most important concepts from the content
5. Format the output as a JSON array of question objects with the following fields:
   - "type": "multiple-choice" (always)
   - "question": the question text
   - "options": array of exactly 4 options
   - "answer": the index (0-3) of the correct answer
   - "explanation": brief explanation of why the answer is correct
   - "source_validation": a brief note about which part of the source text validates this answer (for internal use only)

Important: Use the following source text to validate the correctness of answers and explanations:
%s

IMPORTANT FORMATTING RULES:
1. The response must be a valid JSON array
2. Do not include any markdown code blocks or backticks
3. Do not include any explanatory text before or after the JSON array
4. Ensure all strings are properly escaped
5. Use double quotes for all strings, not single quotes
6. Do not include any trailing commas
7. The language output should come from the language input


Example format:
[
  {
    "type": "multiple-choice",
    "question": "What is the main function of X?",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "answer": 2,
    "explanation": "Option C is correct because...",
    "source_validation": "This is validated by the source text section discussing X's primary function..."
  }
]`, title, content, sourceText)
}

// cleanMarkdownCodeBlocks cleans up markdown code blocks from AI responses
func cleanMarkdownCodeBlocks(content string) string {
	// Remove markdown code fence markers
	content = strings.TrimPrefix(content, "```json")
	content = strings.TrimPrefix(content, "```")
	content = strings.TrimSuffix(content, "```")

	// Trim whitespace and newlines
	content = strings.TrimSpace(content)

	// Remove any leading/trailing text that might be before/after the JSON array
	start := strings.Index(content, "[")
	end := strings.LastIndex(content, "]")
	if start >= 0 && end >= 0 && end > start {
		content = content[start : end+1]
	}

	// Clean up any escaped newlines in strings
	content = strings.ReplaceAll(content, "\\n", " ")

	// Validate JSON
	var js interface{}
	if err := json.Unmarshal([]byte(content), &js); err != nil {
		log.Printf("Warning: Generated content is not valid JSON after cleaning: %v", err)
		return "[]" // Return empty array if JSON is invalid
	}

	return content
}

// Singleton instance of the generator
var (
	instance *Generator
	initErr  error
)

// GetGenerator returns a singleton instance of the Generator
func GetGenerator() (*Generator, error) {
	if instance == nil && initErr == nil {
		instance, initErr = NewGenerator()
	}
	return instance, initErr
}

// GenerateContentQuiz provides a simplified interface for generating quizzes
func GenerateContentQuiz(ctx context.Context, title string, content string, sourceText string) (string, error) {
	generator, err := GetGenerator()
	if err != nil {
		return "", err
	}

	return generator.GenerateQuiz(ctx, title, content, sourceText)
}
