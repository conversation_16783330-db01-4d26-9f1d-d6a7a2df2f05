package storage

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/joho/godotenv"
)

func init() {
	// Load .env file if it exists
	godotenv.Load()
}

// FileConverter defines methods for converting files to PDF
type FileConverter interface {
	ConvertToPDF(file *multipart.FileHeader) ([]byte, string, error)
	ConvertFileToPDF(filePath string) (string, error)
}

// GotenbergConverter implements FileConverter using Gotenberg API
type GotenbergConverter struct {
	apiURL string
}

// NewGotenbergConverter creates a new Gotenberg converter client
func NewGotenbergConverter() *GotenbergConverter {
	// Get the Gotenberg API URL from environment
	apiURL := os.Getenv("GOTENBERG_API_URL")

	// Check if API URL is set
	if apiURL == "" {
		fmt.Printf("WARNING: GOTENBERG_API_URL environment variable is not set\n")
		// Provide a reasonable default that works in both Docker and non-Docker environments
		apiURL = "http://gotenberg:3000"
	}

	fmt.Printf("Initializing GotenbergConverter\n")

	return &GotenbergConverter{
		apiURL: apiURL,
	}
}

// ConvertToPDF converts a multipart file to PDF using Gotenberg
// Returns the PDF content as bytes, the new filename, and any error
func (g *GotenbergConverter) ConvertToPDF(file *multipart.FileHeader) ([]byte, string, error) {
	// Skip conversion if file is already a PDF
	if isPDF(file.Filename) {
		src, err := file.Open()
		if err != nil {
			return nil, file.Filename, fmt.Errorf("failed to open PDF file: %v", err)
		}
		defer src.Close()

		// Read the PDF content
		pdfContent, err := io.ReadAll(src)
		if err != nil {
			return nil, file.Filename, fmt.Errorf("failed to read PDF content: %v", err)
		}

		return pdfContent, file.Filename, nil
	}

	// Check if file format is supported for conversion
	if !isConvertibleFormat(file.Filename) {
		src, err := file.Open()
		if err != nil {
			return nil, file.Filename, fmt.Errorf("failed to open file: %v", err)
		}
		defer src.Close()

		// Read the content
		content, err := io.ReadAll(src)
		if err != nil {
			return nil, file.Filename, fmt.Errorf("failed to read content: %v", err)
		}

		return content, file.Filename, nil
	}

	// Open the source file
	src, err := file.Open()
	if err != nil {
		return nil, "", fmt.Errorf("failed to open file: %v", err)
	}
	defer src.Close()

	// Create a temporary file to store the original content
	tempFile, err := os.CreateTemp("", "original-*"+filepath.Ext(file.Filename))
	if err != nil {
		return nil, "", fmt.Errorf("failed to create temporary file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// Copy the content to the temporary file
	if _, err = io.Copy(tempFile, src); err != nil {
		return nil, "", fmt.Errorf("failed to copy file content: %v", err)
	}
	tempFile.Close() // Close to ensure all data is written

	// Convert the file using Gotenberg
	pdfBytes, err := g.convertFileUsingGotenberg(tempFile.Name())
	if err != nil {
		return nil, "", fmt.Errorf("conversion failed: %v", err)
	}

	// Generate the new PDF filename
	pdfFilename := strings.TrimSuffix(file.Filename, filepath.Ext(file.Filename)) + ".pdf"

	return pdfBytes, pdfFilename, nil
}

// ConvertFileToPDF converts a file at the given path to PDF and returns the path to the converted file
func (g *GotenbergConverter) ConvertFileToPDF(filePath string) (string, error) {
	// Skip conversion if file is already a PDF
	if isPDF(filePath) {
		return filePath, nil
	}

	// Check if file format is supported for conversion
	if !isConvertibleFormat(filePath) {
		return filePath, nil // Return original path if not convertible
	}

	// Convert the file using Gotenberg
	pdfBytes, err := g.convertFileUsingGotenberg(filePath)
	if err != nil {
		return "", fmt.Errorf("conversion failed: %v", err)
	}

	// Create a temporary file for the PDF output
	pdfPath := strings.TrimSuffix(filePath, filepath.Ext(filePath)) + ".pdf"
	if err := os.WriteFile(pdfPath, pdfBytes, 0644); err != nil {
		return "", fmt.Errorf("failed to write PDF file: %v", err)
	}

	return pdfPath, nil
}

// convertFileUsingGotenberg sends a file to Gotenberg API for conversion
func (g *GotenbergConverter) convertFileUsingGotenberg(filePath string) ([]byte, error) {
	// Create a new multipart writer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Determine conversion endpoint based on file type
	endpoint := g.determineEndpoint(filePath)

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// Create a form file field for the file
	fileField, err := writer.CreateFormFile("files", filepath.Base(filePath))
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %v", err)
	}

	// Copy the file content to the form field
	if _, err = io.Copy(fileField, file); err != nil {
		return nil, fmt.Errorf("failed to copy file to form: %v", err)
	}

	// Close the multipart writer
	writer.Close()

	// Create the request
	req, err := http.NewRequest("POST", g.apiURL+endpoint, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("conversion failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read the response body (PDF content)
	pdfBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	return pdfBytes, nil
}

// determineEndpoint determines the appropriate Gotenberg API endpoint for the file type
func (g *GotenbergConverter) determineEndpoint(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	switch ext {
	case ".docx", ".doc":
		return "/forms/libreoffice/convert"
	case ".pptx", ".ppt":
		return "/forms/libreoffice/convert"
	case ".xlsx", ".xls":
		return "/forms/libreoffice/convert"
	case ".html", ".htm":
		return "/forms/chromium/convert/html"
	case ".md":
		return "/forms/chromium/convert/markdown"
	default:
		return "/forms/libreoffice/convert" // Default to LibreOffice converter
	}
}

// Helper functions

// isPDF checks if a file is already a PDF
func isPDF(filename string) bool {
	return strings.ToLower(filepath.Ext(filename)) == ".pdf"
}

// isConvertibleFormat checks if a file format is supported for conversion
func isConvertibleFormat(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	convertibleFormats := []string{
		".docx", ".doc",
		".pptx", ".ppt",
		".xlsx", ".xls",
		".html", ".htm",
		".md",
		".odt", ".ods", ".odp",
		".txt",
	}

	for _, format := range convertibleFormats {
		if ext == format {
			return true
		}
	}

	return false
}
