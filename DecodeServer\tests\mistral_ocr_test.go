package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"testing"

	"github.com/joho/godotenv"
)

// loadEnv loads environment variables from .env file
func loadEnv() error {
	// Try to find .env file in current directory and parent directories
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	for {
		envFile := filepath.Join(dir, ".env")
		if _, err := os.Stat(envFile); err == nil {
			// Found .env file, load it
			if err := godotenv.Load(envFile); err != nil {
				return fmt.Errorf("error loading .env file: %v", err)
			}
			return nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}

	return fmt.Errorf(".env file not found in current or parent directories")
}

// Helper function to get minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func TestMistralOCRIntegration(t *testing.T) {
	// Load environment variables from .env file
	if err := loadEnv(); err != nil {
		t.Logf("Warning: %v", err)
	}

	// Get API key from environment
	apiKey := os.Getenv("MISTRAL_API_KEY")
	if apiKey == "" {
		t.Skip("MISTRAL_API_KEY environment variable not set. Skipping test.")
	}

	// Define the API endpoint
	url := "https://api.mistral.ai/v1/ocr"

	// Create the request body exactly according to Mistral's documentation
	requestBody := map[string]interface{}{
		"model": "mistral-ocr-latest",
		"document": map[string]interface{}{
			"type":         "document_url",
			"document_url": "https://arxiv.org/pdf/2201.04234",
		},
		"include_image_base64": true,
	}

	// Marshal to JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("Failed to marshal request body: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	// Set headers exactly as in curl example
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// Debug: Print request details
	t.Logf("==== REQUEST DETAILS ====")
	t.Logf("URL: %s", url)
	t.Logf("Authorization: Bearer %s...", apiKey[:min(10, len(apiKey))])
	t.Logf("Request Body: %s", string(jsonBody))
	t.Logf("========================")

	// Make request
	t.Log("Sending request to Mistral API...")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()

	// Print response status for debugging
	t.Logf("Response Status Code: %d", resp.StatusCode)

	// Read the raw response first for debugging
	rawBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}

	// Print raw response for debugging
	t.Logf("Raw Response: %s", string(rawBody))

	// If status code is not 200, we'll still print the response but the test will fail
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code 200, got %d. Response: %s", resp.StatusCode, string(rawBody))
		return
	}

	// Try to parse the response as JSON
	var responseBody map[string]interface{}
	if err := json.Unmarshal(rawBody, &responseBody); err != nil {
		t.Fatalf("Failed to parse response as JSON: %v", err)
	}

	// Print response data
	t.Logf("Parsed Response: %v", responseBody)

	// The test will already have failed if status code wasn't 200, but we'll check for specific fields
	// Note: The actual fields to check may vary based on Mistral's response format
	if pages, ok := responseBody["pages"]; ok {
		t.Logf("Pages found in response: %v", pages)
	} else {
		t.Error("Expected 'pages' field in response but it was not found")
	}
}
