'use client'

import { Activity, DraftingCompass, Book, Zap } from "lucide-react"
import { useLanguage } from '@/app/providers/language-provider'

export default function FeaturesSection() {
  const { t } = useLanguage()
  return (
    <section className="py-12 sm:py-16 md:py-24 lg:py-32 dark:bg-[hsl(240_10%_3.9%)]">
      <div className="mx-auto max-w-6xl px-4 sm:px-6">
        <div className="grid items-center gap-8 sm:gap-10 md:gap-12 md:grid-cols-2 lg:grid-cols-5 lg:gap-16 xl:gap-24">
          <div className="lg:col-span-2">
            <div className="md:pr-4 lg:pr-0">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-gray-900 dark:text-gray-100 lg:text-5xl">{t('features.title')}</h2>
              <p className="mt-4 sm:mt-6 text-sm sm:text-base md:text-lg text-gray-700 dark:text-gray-300">
                {t('features.description')}
              </p>
            </div>
            <ul className="mt-6 sm:mt-8 divide-y border-y text-sm sm:text-base text-gray-800 dark:text-gray-200 *:flex *:items-center *:gap-2 sm:*:gap-3 *:py-2 sm:*:py-3">
              
              <li>
                <Book className="size-4 sm:size-5" />
                {t('features.listItem1')}
              </li>
              <li>
                <DraftingCompass className="size-4 sm:size-5" />
                {t('features.listItem2')}
              </li>
              <li>
                <Zap className="size-4 sm:size-5" />
                {t('features.listItem3')}
              </li>
              <li>
                <Activity className="size-4 sm:size-5" />
                {t('features.listItem4')}
              </li>
            </ul>
          </div>
          <div className="border-border/50 relative rounded-xl sm:rounded-2xl md:rounded-3xl lg:col-span-3 aspect-76/59 mt-6 sm:mt-0 dark:from-zinc-700">
            <video
              src="https://1kl8egk7md.ufs.sh/f/I3tdzkulYJ0GJvhcMg5abeLqrFUinZ07dWQRlxp41hsYkzXE"
              className="rounded-xl sm:rounded-[15px] w-full h-auto"
              controls={false}
              autoPlay
              muted
              loop
            />
          </div>
        </div>
      </div>
    </section>
  )
}
