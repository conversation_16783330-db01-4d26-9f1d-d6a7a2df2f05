import os
from openai import AsyncOpenAI
from dotenv import load_dotenv
from search_api.ai_models.study_design import extract_study_design

load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

async def generate_evidence_report(articles):
    evidence_reports = []
    for article in articles:
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": """You are an expert in research methodology and evidence-based medicine. Analyze the given abstract and provide a concise table and evaluation based on the following parameters:


1. 🔬 Study Design: Select the most appropriate study design from the following options:
                     Clinical Practice Guidelines

                     Systematic Review 
                     Meta-analysis
                     Randomized Controlled Trial (RCT)
                 
                     Cohort Study
                     Case-Control Study
                     Cross-sectional Study

                     Case Series
                     Case Report

                     Narrative Review: Do not confuse with systematic review or meta-analysis.
                     Expert Opinion
                     Editorial opinion

                     In vivo study
                     In vitro study

2. ⚖️ Risk of Bias:
3. 🧮 Sample Size and Power
4. 🔄 Consistency of Results
5. 📈 Statistical Significance
6. 🏥 Clinical Relevance
7. 🔁 Reproducibility
8. 🌍 Applicability 
9. 💼 Conflicts of Interest 

Provide a brief assessment for each parameter, the assessment should be a 10 word sentence.
Format the output as a valid markdown table with two columns: 'Parameter' and 'Assessment'.
Use '|' to separate columns and '-' to create the header row.
Include the icons in the 'Parameter' column.
Ensure the table starts with a proper header row.
Do not include any numbers or unexpected characters at the start of lines.
If information is not available for a parameter, do not include that parameter in the table.
After the table, on a new line, state how many parameters you have assessed out of the total.

Example of correct formatting:

| Parameter | Assessment |
|-----------|------------|
| 🔬 Study Design | Brief 10-word assessment of the study design. |
| ⚖️ Risk of Bias | Brief 10-word assessment of the risk of bias. |
// ... (other parameters) ...

Parameters assessed: X out of 9.
"""
                },

                {
                    "role": "user",
                    "content": f"""Analyze the following abstract:\n\n{article.get('abstract', '')}
                                 If not abstract is provided, return No evidence report available, do not say 'No abstract provided'.
                                   """
                } 
            ],
            temperature=0.4
        )
        report = response.choices[0].message.content.strip()
        evidence_reports.append({
            'title': article.get('title', ''),
            'report': report
        })
    return evidence_reports




