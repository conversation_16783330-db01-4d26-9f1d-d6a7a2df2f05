{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4773513a._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_a23b55e6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|mp4|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|mp4|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NmY4OpvNS/yGwoHKVlaXfBcOit7d+O2u8efRkkTUrPU=", "__NEXT_PREVIEW_MODE_ID": "c017980b9e2099ef7b274a0d549125fa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d99d45b014904cc5c9a9f6b2c2f8f5e0d1915788806370af6a1135332b97d5d5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "088e408d0ceae195742fd1c0f32e161b78524f9a3e7fb54984675c45e7de3fa0"}}}, "sortedMiddleware": ["/"], "functions": {}}