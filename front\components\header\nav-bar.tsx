"use client";

import React from 'react';
import { usePathname } from 'next/navigation';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { useTheme } from '@/components/theme/theme-provider';
import { SignInButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs';
import { Menu } from 'lucide-react';
import { LanguageSwitcher } from '@/app/providers/language-switcher';
import Image from 'next/image';
import Link from 'next/link';

interface NavBarProps {
  handleNewChat?: () => void;
  toggleHistoryVisibility?: () => void;
  toggleFilterVisibility?: () => void;
  isLeftTabVisible?: boolean;
  onShowLeftTab?: () => void;
}

export default function NavBar({ 
  isLeftTabVisible = true,
  onShowLeftTab
}: NavBarProps) {
  const pathname = usePathname();
  const { theme } = useTheme();

  // Profile visibility logic
  const isAuthPage = pathname.startsWith('/sign-in') || pathname.startsWith('/sign-up');
  const isFeaturePage = pathname.startsWith('/home/<USER>');
  const isPricingPage = pathname.startsWith('/home/<USER>');
  const isContactPage = pathname.startsWith('/home/<USER>');
  const isAboutPage = pathname.startsWith('/home/<USER>');
  const isTermsPage = pathname.startsWith('/home/<USER>/terms');
  const isPrivacyPage = pathname.startsWith('/home/<USER>/privacy');

  const shouldShowProfile = !(
    pathname === '/' ||
    pathname === '/home' ||
    isAuthPage ||
    isFeaturePage ||
    isPricingPage ||
    isContactPage ||
    isAboutPage ||
    isTermsPage ||
    isPrivacyPage
  );

  // Get the appropriate logo based on theme
  const getLogoSrc = () => {
    return theme === "dark" ? "/decodemed_text_dark.svg" : "/decodemed_text.svg";
  };

  return (
    <div className={`flex flex-col ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-900'}`}>
      <header className={`${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'} z-10`}>
        <div className="max-w-full px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12">
            {/* Left side: Menu button and logo when LeftTab is hidden */}
            <div className="flex items-center">
              {!isLeftTabVisible && onShowLeftTab && (
                <>
                  <button 
                    onClick={onShowLeftTab}
                    className="mr-4 p-1.5 rounded-md focus:outline-none hover:bg-gray-200 dark:hover:bg-gray-700"
                    aria-label="Show sidebar"
                  >
                    <Menu size={24} />
                  </button>
                  <Link href="/studio" className="ml-2">
                    <Image src={getLogoSrc()} alt="DecodeMed" width={150} height={30} />
                  </Link>
                </>
              )}
            </div>
            
            {/* Center section - empty for now */}
            <div className="flex-1 flex justify-center">
            </div>
            
            {/* Right side: User menu */}
            <div className="flex items-center space-x-2">
              {shouldShowProfile && (
                <div className="ml-auto flex items-center gap-4">
                  <LanguageSwitcher />
                  <ThemeToggle />
                  <SignedIn>
                    <UserButton
                      afterSignOutUrl="/"
                      appearance={{
                        elements: {
                          avatarBox: 'w-9 h-9'
                        }
                      }}
                    />
                  </SignedIn>
                  <SignedOut>
                    <SignInButton mode="modal">
                      <button className="px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors">
                        Sign in
                      </button>
                    </SignInButton>
                  </SignedOut>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>
    </div>
  );
}
