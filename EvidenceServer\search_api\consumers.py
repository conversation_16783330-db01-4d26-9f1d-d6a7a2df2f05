from channels.generic.websocket import AsyncWebsocketConsumer
import json
from .views import ParallelProcessingView
from asgiref.sync import sync_to_async
from .ai_models.articles_summary import generate_summary

class ArticleConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()

    async def disconnect(self, close_code):
        pass

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        question = text_data_json.get('question', '')
        articles = text_data_json.get('articles', [])

        parallel_processing_view = ParallelProcessingView()
        response = await parallel_processing_view.post(self.scope['request'])
        
        async for chunk in response.streaming_content:
            await self.send(text_data=chunk.decode('utf-8'))

class SummaryConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()

    async def disconnect(self, close_code):
        pass

    async def receive(self, text_data):
        data = json.loads(text_data)
        question = data.get('question', '')
        articles = data.get('articles', [])

        async for chunk in generate_summary(question, articles):
            await self.send(text_data=json.dumps({
                'summary': chunk
            }))

