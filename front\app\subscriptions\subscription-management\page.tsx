'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Loader2 } from 'lucide-react';
import NavBar from '@/components/header/nav-bar';
import LeftSidebar from '@/components/left_tab/LeftTab';
import { useTheme } from '@/components/theme/theme-provider';
import SubscriptionManager from '@/app/subscriptions/components/SubscriptionManager';
import { useLanguage } from '@/app/providers/language-provider'

import { getSubscriptionStatus, mapSubscriptionToUsage } from '@/app/utils/subscription-api';
import { UsageStats } from '@/app/utils/stripe';

export default function SubscriptionManagementPage() {
  const { getToken, isLoaded, isSignedIn } = useAuth();
  const { theme } = useTheme();
  const [usage, setUsage] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isLeftTabVisible, setIsLeftTabVisible] = useState(true);
  const { t } = useLanguage()

  // Fetch subscription status function
  const fetchUsage = useCallback(async () => {
    if (!isLoaded || !isSignedIn) {
      setError('Please sign in to view subscription status');
      return;
    }
    setLoading(true);
    try {
      const subscriptionData = await getSubscriptionStatus(getToken);
      
      const usageData = mapSubscriptionToUsage(subscriptionData);
      
      setUsage(usageData);
      setError('');
    } catch (error) {
      console.error('Error fetching usage:', error);
      setError(error instanceof Error ? error.message : 'Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  }, [isLoaded, isSignedIn, getToken]);

  // Handle data fetching
  useEffect(() => {
    if (isSignedIn && isLoaded) {
      fetchUsage(); // Initial fetch
      const interval = setInterval(fetchUsage, 60000); // Polling

      // Add event listener for window focus to re-fetch data
      const handleFocus = () => {
        console.log('Window focused, re-fetching usage data.');
      fetchUsage();
      };
      window.addEventListener('focus', handleFocus);

      return () => {
        clearInterval(interval);
        window.removeEventListener('focus', handleFocus);
      };
    }
  }, [isSignedIn, isLoaded, fetchUsage]);

  if (loading) {
    return (
      <>
        <div className="flex flex-col min-h-screen">
          {isLeftTabVisible && (
            <LeftSidebar 
              onHide={() => setIsLeftTabVisible(false)}
            />
          )}
          <div className="fixed top-0 left-0 right-0 z-10">
            <NavBar 
              isLeftTabVisible={isLeftTabVisible} 
              onShowLeftTab={() => setIsLeftTabVisible(true)} 
            />
          </div>
          <div className={`flex flex-1 pt-12`}>
            <main className={`flex-1 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
              <div className={`min-h-screen py-12 px-4 sm:px-6 lg:px-8 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
                <div className="max-w-7xl mx-auto">
                  <div className="flex items-center justify-center min-h-[400px]">
                    <Loader2 className="h-8 w-8 text-gray-400" />
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <div className="flex flex-col min-h-screen">
          {isLeftTabVisible && (
            <LeftSidebar 
              onHide={() => setIsLeftTabVisible(false)}
            />
          )}
          <div className="fixed top-0 left-0 right-0 z-10">
            <NavBar 
              isLeftTabVisible={isLeftTabVisible} 
              onShowLeftTab={() => setIsLeftTabVisible(true)} 
            />
          </div>
          <div className={`flex flex-1 pt-12`}>
            <main className={`flex-1 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
              <div className={`min-h-screen py-12 px-4 sm:px-6 lg:px-8 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
                <div className="max-w-7xl mx-auto">
                  <div className="text-center text-red-500">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </>
    );
  }

  if (!usage) return null;

  return (
    <>
      <div className="flex flex-col min-h-screen">
        {isLeftTabVisible && (
          <LeftSidebar 
            onHide={() => setIsLeftTabVisible(false)}
          />
        )}
        <div className="fixed top-0 left-0 right-0 z-10">
          <NavBar 
            isLeftTabVisible={isLeftTabVisible} 
            onShowLeftTab={() => setIsLeftTabVisible(true)} 
          />
        </div>
        
        <div className={`flex flex-1 pt-12`}>
          <main className={`flex-1 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
            <div className={`min-h-screen py-12 px-4 sm:px-6 lg:px-8 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
              <div className="max-w-7xl mx-auto">
                <div className="text-center mb-8">
                  <h2 className={`text-3xl font-extrabold sm:text-4xl ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                    {t('subscription_management.title')}
                  </h2>
                  <p className={`mt-4 text-xl ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                    {t('subscription_management.subtitle')}
                  </p>
                </div>

                {/* Subscription Management */}
                <div className="max-w-3xl mx-auto">
                  <div className={`rounded-lg overflow-hidden border ${
                    theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] border-transparent' : 'bg-transparent border-transparent'
                  } shadow-lg p-6`}>
                    {/* Use our SubscriptionManager component */}
                    <SubscriptionManager 
                      usage={usage} 
                      onSubscriptionUpdated={fetchUsage}
                    />
                  </div>
                </div>

                {/* Subscription management section - Premium upgrade section removed */}
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  );
}