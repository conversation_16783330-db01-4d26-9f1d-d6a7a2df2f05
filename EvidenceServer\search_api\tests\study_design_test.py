import unittest
import asyncio
import os
from dotenv import load_dotenv
from ..ai_models.study_design import extract_study_design

class TestStudyDesignIntegration(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        load_dotenv()
        cls.api_key = os.getenv("OPENAI_API_KEY")
        if not cls.api_key:
            raise Exception("OPENAI_API_KEY environment variable not set")

    def setUp(self):
        self.test_articles = [
            {
                'title': 'Effects of tretinoin on acne: A randomized controlled trial',
                'abstract': 'Background: We conducted a randomized controlled trial to evaluate tretinoin efficacy in acne treatment. Methods: 100 patients were randomly assigned to receive either tretinoin or placebo for 12 weeks...'
            },
            {
                'title': 'Systematic review of acne treatments',
                'abstract': 'We conducted a systematic review of all available evidence on acne treatments published between 2000-2023. Database searches identified 500 studies which were screened...'
            }
        ]

    def test_extract_study_design_integration(self):
        async def run_test():
            results = await extract_study_design("What is the study design?", self.test_articles)
            
            self.assertEqual(len(results), 2)
            self.assertTrue(all(isinstance(result, dict) for result in results))
            self.assertTrue(all('title' in result and 'study_design' in result for result in results))
            
            # Check if first article is identified as RCT
            self.assertIn('RCT', results[0]['study_design'].upper())
            
            # Check if second article is identified as Systematic Review
            self.assertIn('SYSTEMATIC REVIEW', results[1]['study_design'].upper())

        asyncio.run(run_test())

    def test_extract_study_design_invalid_abstract(self):
        async def run_test():
            invalid_article = [{
                'title': 'Test article',
                'abstract': 'This is a very short abstract with no clear study design information.'
            }]
            
            results = await extract_study_design("What is the study design?", invalid_article)
            self.assertEqual(len(results), 1)
            self.assertEqual(results[0]['study_design'], 'Unavailable')

        asyncio.run(run_test())

    def test_extract_study_design_error_handling(self):
        async def run_test():
            invalid_articles = [{'title': 'Test', 'abstract': None}]
            
            try:
                await extract_study_design("What is the study design?", invalid_articles)
            except Exception as e:
                self.fail(f"Test failed with exception: {str(e)}")

        asyncio.run(run_test())

if __name__ == '__main__':
    unittest.main()

