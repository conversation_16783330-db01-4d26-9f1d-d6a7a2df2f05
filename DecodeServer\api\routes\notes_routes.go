package routes

import (
	"decodemed/api/handlers/medical_notes/notes_api"
	"decodemed/api/handlers/medical_notes/upload"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupNotesRoutes configures all routes related to medical notes
func SetupNotesRoutes(router *gin.Engine, db *gorm.DB) {
	// Enable CORS for all routes
	router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "http://localhost:3000")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Group all notes-related routes under /api/medical_notes to match frontend
	notes := router.Group("/api/medical_notes")
	{
		// GET /api/medical_notes - Get all medical notes
		notes.GET("", func(c *gin.Context) {
			c.Set("db", db)
			notes_api.GetNotes(c)
		})

		// GET /api/medical_notes/:id - Get a specific medical note
		notes.GET("/:id", func(c *gin.Context) {
			c.Set("db", db)
			notes_api.GetNote(c)
		})

		// DELETE /api/medical_notes/:id - Delete a medical note
		notes.DELETE("/:id", func(c *gin.Context) {
			c.Set("db", db)
			notes_api.DeleteNote(c)
		})

		// POST /api/medical_notes/upload - Upload a new medical note
		notes.POST("/upload", func(c *gin.Context) {
			c.Set("db", db)
			upload.UploadMedicalNoteHandler(c)
		})
	}
}
