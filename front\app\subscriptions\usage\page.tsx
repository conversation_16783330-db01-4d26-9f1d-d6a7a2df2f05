'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Loader2, SearchIcon, XCircle } from 'lucide-react';
import NavBar from '@/components/header/nav-bar';
import LeftSidebar from '@/components/left_tab/LeftTab';
import { useTheme } from '@/components/theme/theme-provider';
import { getSubscriptionStatus, mapSubscriptionToUsage } from '@/app/utils/subscription-api';
import { UsageStats } from '@/app/utils/stripe';
import { useLanguage } from '@/app/providers/language-provider';

export default function UsagePage() {
  const { getToken, isLoaded, isSignedIn } = useAuth();
  const { theme } = useTheme();
  const { t } = useLanguage();
  const [usage, setUsage] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isLeftTabVisible, setIsLeftTabVisible] = useState(true);

  // Fetch subscription status function
  const fetchUsage = useCallback(async () => {
    if (!isLoaded || !isSignedIn) {
      setError(t('usage.please_sign_in'));
      return;
    }
    setLoading(true);
    try {
      const subscriptionData = await getSubscriptionStatus(getToken);
      
      const usageData = mapSubscriptionToUsage(subscriptionData);
      
      setUsage(usageData);
      setError('');
    } catch (error) {
      console.error('Error fetching usage:', error);
      setError(error instanceof Error ? error.message : t('usage.failed_to_load'));
    } finally {
      setLoading(false);
    }
  }, [isLoaded, isSignedIn, getToken, t]);

  // Handle data fetching
  useEffect(() => {
    if (isSignedIn && isLoaded) {
      fetchUsage();
      const interval = setInterval(fetchUsage, 60000);
      return () => clearInterval(interval);
    }
  }, [isSignedIn, isLoaded, fetchUsage]);

  if (loading) {
    return (
      <>
        <div className="flex flex-col min-h-screen">
          {isLeftTabVisible && (
            <LeftSidebar 
              onHide={() => setIsLeftTabVisible(false)}
            />
          )}
          <div className="fixed top-0 left-0 right-0 z-10">
            <NavBar 
              isLeftTabVisible={isLeftTabVisible} 
              onShowLeftTab={() => setIsLeftTabVisible(true)} 
            />
          </div>
          <div className={`flex flex-1 pt-12`}>
            <main className={`flex-1 transition-all duration-300 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
              <div className={`min-h-screen py-12 px-4 sm:px-6 lg:px-8 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
                <div className="max-w-7xl mx-auto">
                  <div className="flex items-center justify-center min-h-[400px]">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <div className="flex flex-col min-h-screen">
          {isLeftTabVisible && (
            <LeftSidebar 
              onHide={() => setIsLeftTabVisible(false)}
            />
          )}
          <div className="fixed top-0 left-0 right-0 z-10">
            <NavBar 
              isLeftTabVisible={isLeftTabVisible} 
              onShowLeftTab={() => setIsLeftTabVisible(true)} 
            />
          </div>
          <div className={`flex flex-1 pt-12`}>
            <main className={`flex-1 transition-all duration-300 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
              <div className={`min-h-screen py-12 px-4 sm:px-6 lg:px-8 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
                <div className="max-w-7xl mx-auto">
                  <div className="text-center text-red-500">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </>
    );
  }

  if (!usage) return null;

  return (
    <>
      <div className="flex flex-col min-h-screen">
        {isLeftTabVisible && (
          <LeftSidebar 
            onHide={() => setIsLeftTabVisible(false)}
          />
        )}
        <div className="fixed top-0 left-0 right-0 z-10">
          <NavBar 
            isLeftTabVisible={isLeftTabVisible} 
            onShowLeftTab={() => setIsLeftTabVisible(true)} 
          />
        </div>
        
        <div className={`flex flex-1 pt-12`}>
          <main className={`flex-1 transition-all duration-300 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>
            <div className={`min-h-screen py-12 px-4 sm:px-6 lg:px-8 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
              <div className="max-w-7xl mx-auto">
                <div className="text-center mb-8">
                  <h2 className={`text-3xl font-extrabold sm:text-4xl ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                    {t('usage.title')}
                  </h2>
                  <p className={`mt-4 text-xl ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                    {t('usage.subtitle')}
                  </p>
                </div>

                {/* Usage Stats Container */}
                <div className="max-w-3xl mx-auto">
                  <div className={`rounded-lg overflow-hidden transition-all transform hover:scale-105 duration-300 border ${
                    theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] border-transparent' : 'bg-white border-transparent'
                  } shadow-lg p-6`}>
                    {/* Daily Project Usage -> Renamed to Total Project Usage */}
                    <div className="space-y-6">
                      <div className={`p-4 rounded-md ${theme === 'dark' ? 'bg-[hsl(0_0%_10%)]' : 'bg-gray-50'}`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <SearchIcon className={`w-5 h-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'}`} />
                            <span className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'} font-medium`}>{t('usage.total_projects')}</span>
                          </div>
                          <p className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'} font-medium`}>
                            {(usage.status === 'active' || usage.status === 'trialing')
                              ? t('usage.unlimited')
                              : `${usage.search.usage}/${usage.search.limit}`}
                          </p>
                        </div>
                        {/* This section is for Free users to show progress bar and limit reached message */}
                        {usage.status !== 'active' && usage.status !== 'trialing' && (
                          <div className="mt-2">
                              <div className={`w-full ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2 mt-2`}>
                                <div 
                                  className="bg-blue-500 h-2 rounded-full" 
                                  style={{
                                    width: `${Math.min(100, (usage.search.usage / Math.max(1, usage.search.limit)) * 100)}%`
                                  }}
                                ></div>
                              </div>
                            {/* Show limit reached message only if limit is not -1 (unlimited) */}
                            {usage.search.limit !== -1 && usage.search.usage >= usage.search.limit && (
                          <div className="mt-2 flex items-center space-x-2">
                            <XCircle className="w-4 h-4 text-red-500" />
                            <p className="text-red-500 text-sm">{t('usage.limit_reached')}</p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Subscription Plan Info */}
                      <div className={`p-4 rounded-md ${theme === 'dark' ? 'bg-[hsl(0_0%_10%)]' : 'bg-gray-50'}`}>
                        <h4 className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} font-medium mb-2`}>{t('usage.current_plan')}</h4>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${usage.status === 'active' || usage.status === 'trialing' ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                          <p className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>
                            {usage.status === 'active' || usage.status === 'trialing'
                              ? t('usage.pro')
                              : t('usage.free_tier')}
                          </p>
                        </div>
                        {(usage.status === 'active' || usage.status === 'trialing') && (
                          <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                            {t('usage.pro_users_message')}
                          </p>
                        )}
                        {usage.status !== 'active' && usage.status !== 'trialing' && (
                          <p className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                            {t('usage.free_users_message', { limit: usage.search.limit })}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  );
} 