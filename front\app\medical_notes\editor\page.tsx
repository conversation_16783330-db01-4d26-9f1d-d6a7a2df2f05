"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>rovider, LiveEditor, LiveError, LivePreview } from "react-live";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Moon, Sun, Code, Eye, ChevronDown, ChevronUp, Trash2 } from "lucide-react";
import { ThemeProvider, useTheme } from "@/components/theme/theme-provider";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface MedicalNote {
  id: string;
  title: {
    [key: string]: string;
  };
  overview: {
    code: {
      [key: string]: string;
    };
  };
  system: string;
  sections: {
    [key: string]: {
      READ?: {
        title: {
          [key: string]: string;
        };
        code: {
          [key: string]: string;
        };
      };
      MEDIA?: {
        title: string;
        simulate?: {
          url: string;
          segmentations: string;
        };
        watch?: string;
        visualize?: {
          url?: string;
          histopathology?: string;
          mri?: string;
          credit?: string;
          segmentations?: string;
        };
        listen?: string;
      };
    };
  };
}

function SectionEditor({ title, initialCode }: { title: string; initialCode: string }) {
  const [code, setCode] = useState(initialCode);
  const [isExpanded, setIsExpanded] = useState(true);
  const { theme } = useTheme();

  return (
    <div className="mb-8 border rounded-lg overflow-hidden bg-card shadow-sm transition-all duration-200 hover:shadow-md">
      <div 
        className="flex items-center justify-between p-4 bg-muted/50 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold text-foreground">{title}</h2>
          <div className="px-2 py-1 text-xs font-medium rounded-full bg-primary/10 text-primary">
            {isExpanded ? "Expanded" : "Collapsed"}
          </div>
        </div>
        <button 
          className="p-1 rounded-full hover:bg-muted transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
        >
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </button>
      </div>

      <div className={cn(
        "transition-all duration-200 ease-in-out",
        isExpanded ? "max-h-[800px] opacity-100" : "max-h-0 opacity-0"
      )}>
        <LiveProvider 
          code={code} 
          scope={{ useState, AlertTriangle, Moon, Sun }} 
          noInline={true}
        >
          <div className="w-full h-full">
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="grid grid-cols-2 mb-4 p-1 bg-muted/50">
                <TabsTrigger value="editor" className="flex items-center gap-2">
                  <Code size={16} />
                  Editor
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye size={16} />
                  Preview
                </TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="w-full">
                <div className="border rounded-md overflow-hidden">
                  <LiveEditor
                    onChange={(newCode) => setCode(newCode)}
                    style={{
                      fontFamily: "monospace",
                      fontSize: "14px",
                      padding: "16px",
                      minHeight: "200px",
                      backgroundColor: theme === 'dark' ? "#0F172A" : "#F8FAFC",
                      color: theme === 'dark' ? "#E2E8F0" : "#0F172A",
                    }}
                  />
                </div>
              </TabsContent>
              <TabsContent value="preview" className="w-full">
                <div className="border rounded-md p-4 bg-background min-h-[200px] overflow-y-auto max-h-[400px]">
                  <LiveError className="text-red-500 mb-4" />
                  <LivePreview />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </LiveProvider>
      </div>
    </div>
  );
}

function NotesEditor() {
  const [notes, setNotes] = useState<MedicalNote[]>([]);
  const [selectedNote, setSelectedNote] = useState<MedicalNote | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchNotes();
  }, []);

  const fetchNotes = async () => {
    try {
      const response = await fetch('/api/medical-notes');
      if (!response.ok) throw new Error('Failed to fetch notes');
      const data = await response.json();
      setNotes(data);
      if (data.length > 0) {
        setSelectedNote(data[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNote = async (id: string) => {
    try {
      const response = await fetch(`/api/medical-notes/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete note');
      await fetchNotes();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete note');
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen text-red-500">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Medical Notes Editor</h1>
          <p className="text-muted-foreground">View and edit uploaded medical notes</p>
        </div>
        <ThemeToggle />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {notes.map((note) => (
                  <div
                    key={note.id}
                    className={cn(
                      "p-3 rounded-lg cursor-pointer transition-colors",
                      selectedNote?.id === note.id
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    )}
                    onClick={() => setSelectedNote(note)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{note.title.en || note.title.es || 'Untitled'}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteNote(note.id);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-3">
          {selectedNote ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">
                  {selectedNote.title.en || selectedNote.title.es || 'Untitled'}
                </h2>
                <Select defaultValue="en">
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {Object.entries(selectedNote.sections).map(([key, section]) => (
                <SectionEditor
                  key={key}
                  title={key}
                  initialCode={section.READ?.code.en || section.READ?.code.es || ''}
                />
              ))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-12">
              Select a note to view its contents
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function NotesEditorPage() {
  return (
    <ThemeProvider defaultTheme="dark">
      <NotesEditor />
    </ThemeProvider>
  );
}
