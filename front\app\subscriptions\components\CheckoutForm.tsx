'use client';

import { useState, ReactNode } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Loader2, AlertCircle } from 'lucide-react';
import { getStripe, SubscriptionType } from '@/app/utils/stripe';
import { createCheckoutSession } from '@/app/utils/subscription-api';

interface CheckoutFormProps {
  subscriptionType: SubscriptionType;
  buttonText?: ReactNode;
  className?: string;
}

export default function CheckoutForm({
  subscriptionType,
  buttonText = 'Subscribe',
  className = '',
}: CheckoutFormProps) {
  const { getToken, isLoaded, isSignedIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle Stripe redirection
  const handleCheckout = async () => {
    if (!isLoaded || !isSignedIn) {
      setError('Please sign in to subscribe');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Create checkout session via the API
      const { sessionId } = await createCheckoutSession(getToken, subscriptionType);

      // Redirect to Stripe Checkout
      const stripe = await getStripe();
      if (!stripe) {
        throw new Error('Stripe failed to initialize');
      }

      const { error: stripeError } = await stripe.redirectToCheckout({ sessionId });
      
      if (stripeError) {
        throw stripeError;
      }
    } catch (error) {
      console.error('Checkout error:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md flex items-center">
          <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
      
      <button
        onClick={handleCheckout}
        disabled={loading || !isLoaded || !isSignedIn}
        className={`w-full py-2 px-4 rounded-md flex items-center justify-center transition-colors ${
          loading || !isLoaded || !isSignedIn
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        } ${className}`}
      >
        {loading ? (
          <>
            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
            Processing...
          </>
        ) : (
          buttonText
        )}
      </button>
    </div>
  );
} 