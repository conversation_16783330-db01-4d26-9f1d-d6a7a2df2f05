package database

import (
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

// Initialize sets up the database connection and runs migrations
func Initialize(dbURL string) (*gorm.DB, error) {
	var err error
	DB, err = gorm.Open(postgres.Open(dbURL), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// Run migrations
	if err := runMigrations(DB); err != nil {
		return nil, err
	}

	// Initialize database with test data
	if err := initDB(DB); err != nil {
		return nil, err
	}

	return DB, nil
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}
