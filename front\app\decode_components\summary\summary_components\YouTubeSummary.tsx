import React from 'react';
import ReactMarkdown from 'react-markdown';
import { AlertTriangle } from 'lucide-react';

interface YouTubeSummaryProps {
  summary: string;
  theme: string;
}

const YouTubeSummary: React.FC<YouTubeSummaryProps> = ({ summary, theme }) => {
  // Check if summary is empty or undefined
  const hasSummary = summary && summary.trim() !== '';
  
  // Function to format YouTube summary according to the specified structure
  const formatYouTubeSummary = (summaryText: string) => {
    if (!summaryText) return '';
    
    // First, normalize the bullet points to ensure consistent rendering
    const normalizedText = summaryText
      // Ensure proper spacing for bullet points (there should be a space after the dash)
      .replace(/\n\s*-\s*/g, '\n- ')
      // Convert any asterisk bullet points to dash bullet points
      .replace(/\n\s*\*\s*/g, '\n- ')
      // Add bullet points to lines that don't have them but should
      .replace(/\n([A-Z][^-\n:]*\.)/g, '\n- $1')
      // Remove any trailing asterisks from unit titles
      .replace(/(Unit \d+:.*?)\*\*/g, '$1');
    
    // Check if the summary already has the Unit X: format
    if (normalizedText.trim().startsWith('**Unit') || normalizedText.trim().startsWith('## Unit')) {
      // Summary already has proper formatting, just ensure proper spacing and bullet points
      return normalizedText
        .replace(/\*\*Unit\s+(\d+):/g, '## Unit $1:') // Convert **Unit X: to ## Unit X:
        .replace(/Key points:/g, '**Key points:**') // Make "Key points:" bold
        .replace(/\*\*$/gm, ''); // Remove trailing asterisks at the end of lines
    }
    
    // Regular expression to identify units in the format "Unit X: Title"
    const unitRegex = /Unit\s+(\d+):\s+(.*?)(?=\n|$)/g;
    
    // If no unit headers found, treat the entire content as a single unit
    if (!unitRegex.test(normalizedText)) {
      // Process the content with proper formatting
      const lines = normalizedText.split('\n').filter(line => line.trim() !== '');
      let formattedContent = '';
      let currentSection = '';
      let inKeyPointsSection = false;
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // Check if line is a section header (e.g., "Key points:")
        if (line.endsWith(':')) {
          currentSection = line;
          inKeyPointsSection = currentSection.toLowerCase().includes('key points');
          formattedContent += `\n**${currentSection}**\n\n`;
        } 
        // Check if line is a bullet point
        else if (line.startsWith('-') || line.startsWith('•')) {
          formattedContent += `${line}\n`;
        } 
        // Regular paragraph text - add bullet points if in key points section
        else {
          if (inKeyPointsSection && line.trim()) {
            formattedContent += `- ${line}\n`;
          } else {
            formattedContent += `${line}\n\n`;
          }
        }
      }
      
      return `## Unit 1: Summary\n${formattedContent}`;
    }
    
    // Reset the regex lastIndex
    unitRegex.lastIndex = 0;
    
    // Split the summary by units
    const units = [];
    let match;
    
    // Find all unit headings
    while ((match = unitRegex.exec(normalizedText)) !== null) {
      const unitNumber = match[1];
      const unitTitle = match[2].trim().replace(/\*\*$/, ''); // Remove trailing asterisks
      const startIndex = match.index;
      
      // If this isn't the first unit, add the previous unit's content
      if (units.length > 0) {
        const prevUnit = units[units.length - 1];
        prevUnit.content = normalizedText.substring(prevUnit.startIndex, startIndex).trim();
      }
      
      units.push({
        number: unitNumber,
        title: unitTitle,
        startIndex: startIndex,
        content: ''
      });
    }
    
    // Add content for the last unit
    if (units.length > 0) {
      const lastUnit = units[units.length - 1];
      lastUnit.content = normalizedText.substring(lastUnit.startIndex + `Unit ${lastUnit.number}: ${lastUnit.title}`.length).trim();
    }
    
    // Format each unit according to the specified structure
    return units.map(unit => {
      // Process the content to extract subtitles and key points
      const lines = unit.content.split('\n').filter(line => line.trim() !== '');
      let formattedContent = '';
      let sourceText = '';
      
      // Extract source text if present (enclosed in square brackets)
      const sourceMatch = unit.content.match(/\[([\s\S]*?)\]/);
      if (sourceMatch) {
        sourceText = sourceMatch[0];
        // Remove source text from content to avoid duplication
        unit.content = unit.content.replace(sourceMatch[0], '');
      }
      
      // Process content line by line
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // Skip source text lines as we'll add them at the end
        if (line.startsWith('[') && line.endsWith(']')) continue;
        
        // Check if line is a bullet point, if not add one
        if (line.startsWith('-') || line.startsWith('•')) {
          formattedContent += `${line}\n`;
        } else if (line.trim()) {
          // Add bullet point to non-empty lines that don't have one
          formattedContent += `- ${line}\n`;
        }
      }
      
      // Add source text at the end if present
      if (sourceText) {
        formattedContent += `\n${sourceText}\n`;
      }
      
      return `## Unit ${unit.number}: ${unit.title}\n\n${formattedContent}`;
    }).join('\n\n');
  };
  
  return (
    <div className={`rounded-lg p-4 ${
      theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] ' : ''
    } shadow-sm`}>
      
      
      <div className="pt-1 mt-1">
        {hasSummary ? (
          <div className="prose prose-sm lg:prose-base dark:prose-invert w-full max-w-none">
            <ReactMarkdown components={{
              h2: ({children}) => <h2 className="text-2xl font-bold mt-6 mb-3">{children}</h2>,
              strong: ({children}) => <strong className="font-semibold text-lg block mt-4 mb-2">{children}</strong>,
              li: ({children}) => <li className="ml-4 mb-1">{children}</li>,
              ul: ({children}) => <ul className="list-disc pl-6 mb-4 mt-2">{children}</ul>,
              blockquote: ({children}) => (
                <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 py-1 my-4 text-sm text-gray-600 dark:text-gray-400 italic">
                  {children}
                </blockquote>
              ),
            }}>
              {formatYouTubeSummary(summary)}
            </ReactMarkdown>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertTriangle size={24} className="text-amber-500 mb-2" />
            <p className="text-gray-600 dark:text-gray-400">
              No summary content available for this YouTube unit.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
              Try generating units for this project again.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default YouTubeSummary;