package subscriptions

import (
	"decodemed/models"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/stripe/stripe-go/v76"
	billingportalsession "github.com/stripe/stripe-go/v76/billingportal/session"
	checkoutsession "github.com/stripe/stripe-go/v76/checkout/session"
	"github.com/stripe/stripe-go/v76/customer"
	"github.com/stripe/stripe-go/v76/subscription"
	"gorm.io/gorm"
)

// Pricing constants
const (
	MonthlyPriceUSD = 1000 // $10.00 USD (in cents)
	AnnualPriceUSD  = 6000 // $60.00 USD per month (in cents), billed annually as $720.00
)

// InitStripe initializes the Stripe API with the provided API key
func InitStripe() {
	// Load environment variables
	if err := loadEnv(); err != nil {
		log.Printf("Warning: %v", err)
	}

	stripe.Key = os.Getenv("STRIPE_API_KEY")
	if stripe.Key == "" {
		log.Println("Stripe API key not set")
		return
	}
	log.Println("Stripe API key set")

	// Check that price IDs are set in environment
	if os.Getenv("STRIPE_MONTHLY_PRICE_ID") == "" || os.Getenv("STRIPE_YEARLY_PRICE_ID") == "" {
		log.Println("Warning: STRIPE_MONTHLY_PRICE_ID or STRIPE_YEARLY_PRICE_ID not set")
	} else {
		log.Println("Stripe price IDs are set")
	}
}

// GetPlansHandler returns the available pricing plans to the frontend
func GetPlansHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Read price IDs from environment
		monthlyPriceID := os.Getenv("STRIPE_MONTHLY_PRICE_ID")
		yearlyPriceID := os.Getenv("STRIPE_YEARLY_PRICE_ID")

		if monthlyPriceID == "" || yearlyPriceID == "" {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Stripe price IDs not configured"})
			return
		}

		// Use hardcoded plan information instead of querying Stripe
		plans := []gin.H{
			{
				"id":          "monthly",
				"name":        "Monthly Plan",
				"description": "Monthly subscription plan with unlimited access",
				"price":       MonthlyPriceUSD / 100, // Convert cents to dollars
				"interval":    "month",
				"priceId":     monthlyPriceID,
			},
			{
				"id":          "yearly",
				"name":        "Annual Plan",
				"description": "Annual subscription plan with a discount",
				"price":       AnnualPriceUSD / 100, // Convert cents to dollars per month
				"interval":    "year",
				"priceId":     yearlyPriceID,
			},
		}

		c.JSON(http.StatusOK, gin.H{"plans": plans})
	}
}

// CreateCheckoutSessionHandler creates a checkout session for a selected plan
func CreateCheckoutSessionHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Parse request body
		var req struct {
			SubscriptionType string `json:"subscriptionType" binding:"required"`
			SuccessUrl       string `json:"successUrl" binding:"required"`
			CancelUrl        string `json:"cancelUrl" binding:"required"`
			IdempotencyKey   string `json:"idempotencyKey"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Map subscription type to price ID
		var priceID string
		if req.SubscriptionType == "monthly" {
			priceID = os.Getenv("STRIPE_MONTHLY_PRICE_ID")
		} else if req.SubscriptionType == "yearly" {
			priceID = os.Getenv("STRIPE_YEARLY_PRICE_ID")
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription type"})
			return
		}

		if priceID == "" {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Price ID not configured for " + req.SubscriptionType})
			return
		}

		// Lookup the user
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user"})
			return
		}

		// Check if the user already has a Stripe customer ID
		var stripeCustomerID string

		// Look up the Stripe customer from the combined model
		var customerSubscription models.StripeCustomerSubscription
		if err := db.Where("user_id = ?", user.ID).First(&customerSubscription).Error; err != nil {
			if err != gorm.ErrRecordNotFound {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query customer data"})
				return
			}

			// No existing customer found, create a new one
			customerParams := &stripe.CustomerParams{
				Email: stripe.String(user.Email),
			}
			customerParams.AddMetadata("user_id", fmt.Sprintf("%d", user.ID))

			// Use idempotency key for customer creation if provided
			if req.IdempotencyKey != "" {
				customerParams.SetIdempotencyKey(req.IdempotencyKey + "_customer")
			}

			newCustomer, err := customer.New(customerParams)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create Stripe customer"})
				return
			}

			stripeCustomerID = newCustomer.ID

			// Store the customer ID in the database immediately instead of waiting for webhook
			customerSubscription = models.StripeCustomerSubscription{
				UserID:     user.ID,
				CustomerID: stripeCustomerID,
				Status:     "none", // No subscription yet
			}

			if err := db.Create(&customerSubscription).Error; err != nil {
				log.Printf("Error storing Stripe customer ID in database: %v", err)
				// Continue anyway, the webhook handler will try to create it again
			} else {
				log.Printf("Successfully stored Stripe customer ID %s for user %d", stripeCustomerID, user.ID)
			}

			// Customer record is now created in the database, webhooks will update it as needed
		} else {
			// Use existing customer ID
			stripeCustomerID = customerSubscription.CustomerID
		}

		// Create Stripe checkout session with expanded objects
		checkoutParams := &stripe.CheckoutSessionParams{
			Customer:          stripe.String(stripeCustomerID),
			ClientReferenceID: stripe.String(fmt.Sprintf("%d", user.ID)),
			SuccessURL:        stripe.String(req.SuccessUrl),
			CancelURL:         stripe.String(req.CancelUrl),
			PaymentMethodTypes: stripe.StringSlice([]string{
				"card",
			}),
			Mode: stripe.String("subscription"),
			LineItems: []*stripe.CheckoutSessionLineItemParams{
				{
					Price:    stripe.String(priceID),
					Quantity: stripe.Int64(1),
				},
			},
			Params: stripe.Params{
				Expand: []*string{
					stripe.String("subscription"),
					stripe.String("customer"),
				},
			},
		}

		// Set idempotency key if provided to prevent duplicate charges
		if req.IdempotencyKey != "" {
			checkoutParams.SetIdempotencyKey(req.IdempotencyKey)
		}

		s, err := checkoutsession.New(checkoutParams)
		if err != nil {
			log.Printf("Error creating checkout session: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create checkout session"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"sessionId": s.ID,
			"customer": gin.H{
				"id":    s.Customer.ID,
				"email": s.Customer.Email,
			},
		})
	}
}

// GetSubscriptionStatusHandler returns the user's active subscription
func GetSubscriptionStatusHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Lookup the user
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user"})
			return
		}

		// Use the correct free projects limit from models package
		const FREE_PROJECTS_LIMIT = models.FreeUserTotalProjectLimit

		// Look up the user's subscription in the combined model
		var subscription models.StripeCustomerSubscription
		subQueryErr := db.Where("user_id = ?", user.ID).Order("created_at DESC").First(&subscription).Error

		// Get today's usage statistics
		todayUsage, tuErr := models.GetTodayUsage(db, user.ID)
		if tuErr != nil {
			todayUsage = &models.SubscriptionUsage{ProjectsCreatedToday: 0}
		}

		// Get user's daily project limit based on subscription
		dailyLimit, dlErr := models.GetUserDailyProjectLimit(db, user.ID)
		if dlErr != nil {
			dailyLimit = models.FreeUserDailyProjectLimit
		}

		// Calculate remaining projects for today
		remainingToday := 0
		if todayUsage != nil {
			remainingToday = dailyLimit - todayUsage.ProjectsCreatedToday
			if remainingToday < 0 {
				remainingToday = 0
			}
		}

		if subQueryErr != nil {
			if subQueryErr == gorm.ErrRecordNotFound {
				c.JSON(http.StatusOK, gin.H{
					"status":                  "none",
					"details":                 nil,
					"free_projects_count":     user.FreeProjectsCount,
					"free_projects_limit":     FREE_PROJECTS_LIMIT,
					"free_projects_remaining": FREE_PROJECTS_LIMIT - user.FreeProjectsCount,
				})
				return
			}

			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription"})
			return
		}

		// Format subscription details
		details := gin.H{
			"id":                   subscription.SubscriptionID,
			"customer_id":          subscription.CustomerID,
			"status":               subscription.Status,
			"current_period_end":   subscription.CurrentPeriodEnd,
			"plan_type":            subscription.PlanType,
			"cancel_at_period_end": subscription.CancelAtPeriod,
		}

		c.JSON(http.StatusOK, gin.H{
			"status":                  subscription.Status,
			"details":                 details,
			"free_projects_count":     user.FreeProjectsCount,
			"free_projects_limit":     FREE_PROJECTS_LIMIT,
			"free_projects_remaining": FREE_PROJECTS_LIMIT - user.FreeProjectsCount,
		})
	}
}

// CancelSubscriptionHandler cancels the user's subscription at the end of the billing period
func CancelSubscriptionHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Lookup the user
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user"})
			return
		}

		// Find the user's subscription in the combined model
		var subscriptionModel models.StripeCustomerSubscription
		err := db.Where("user_id = ?", user.ID).Order("created_at DESC").First(&subscriptionModel).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription"})
			return
		}

		// Cancel the subscription at the end of the billing period
		params := &stripe.SubscriptionParams{
			CancelAtPeriodEnd: stripe.Bool(true),
		}

		_, err = subscription.Update(subscriptionModel.SubscriptionID, params)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel subscription"})
			return
		}

		// Update the local model
		subscriptionModel.CancelAtPeriod = true
		if err := db.Save(&subscriptionModel).Error; err != nil {
			log.Printf("Failed to update subscription cancel status: %v", err)
			// Continue anyway - the Stripe API call succeeded
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Subscription will be canceled at the end of the billing period",
		})
	}
}

// ResumeSubscriptionHandler resumes a canceled subscription
func ResumeSubscriptionHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Lookup the user
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user"})
			return
		}

		// Find the user's subscription
		var subscriptionModel models.StripeCustomerSubscription
		err := db.Where("user_id = ?", user.ID).Order("created_at DESC").First(&subscriptionModel).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "No subscription found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription"})
			return
		}

		// Check if the subscription is set to cancel at period end
		if !subscriptionModel.CancelAtPeriod {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Subscription is not scheduled for cancellation"})
			return
		}

		// Resume the subscription by setting cancel_at_period_end to false
		params := &stripe.SubscriptionParams{
			CancelAtPeriodEnd: stripe.Bool(false),
		}

		_, err = subscription.Update(subscriptionModel.SubscriptionID, params)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to resume subscription"})
			return
		}

		// Update the local model
		subscriptionModel.CancelAtPeriod = false
		if err := db.Save(&subscriptionModel).Error; err != nil {
			log.Printf("Failed to update subscription cancel status: %v", err)
			// Continue anyway - the Stripe API call succeeded
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Subscription has been resumed",
		})
	}
}

// UpdateSubscriptionHandler changes the user's subscription plan
func UpdateSubscriptionHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Parse request body
		var req struct {
			SubscriptionType string `json:"subscriptionType" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Map subscription type to price ID
		var newPriceID string
		if req.SubscriptionType == "monthly" {
			newPriceID = os.Getenv("STRIPE_MONTHLY_PRICE_ID")
		} else if req.SubscriptionType == "yearly" {
			newPriceID = os.Getenv("STRIPE_YEARLY_PRICE_ID")
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription type"})
			return
		}

		// Lookup the user
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user"})
			return
		}

		// Find the user's subscription
		var subscriptionModel models.StripeCustomerSubscription
		err := db.Where("user_id = ?", user.ID).Order("created_at DESC").First(&subscriptionModel).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "No active subscription found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription"})
			return
		}

		// First, retrieve the subscription to get the subscription item ID
		sub, err := subscription.Get(subscriptionModel.SubscriptionID, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscription details"})
			return
		}

		// Make sure there's at least one item
		if len(sub.Items.Data) == 0 {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "No subscription items found"})
			return
		}

		// Get the first item's ID
		firstItemID := sub.Items.Data[0].ID

		// Update the subscription with the new plan
		params := &stripe.SubscriptionParams{
			ProrationBehavior: stripe.String("create_prorations"),
			Items: []*stripe.SubscriptionItemsParams{
				{
					ID:    stripe.String(firstItemID),
					Price: stripe.String(newPriceID),
				},
			},
		}

		updated, err := subscription.Update(subscriptionModel.SubscriptionID, params)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update subscription"})
			return
		}

		// Update the local model
		subscriptionModel.PlanType = req.SubscriptionType
		if err := db.Save(&subscriptionModel).Error; err != nil {
			log.Printf("Failed to update subscription plan type: %v", err)
			// Continue anyway - the Stripe API call succeeded
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Subscription updated successfully",
			"subscription": gin.H{
				"id":     updated.ID,
				"status": updated.Status,
			},
		})
	}
}

// CreatePortalSessionHandler creates a Stripe Customer Portal session
func CreatePortalSessionHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Parse request body
		var req struct {
			ReturnUrl string `json:"returnUrl" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Lookup the user
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user"})
			return
		}

		// Find the user's Stripe customer ID in the combined model
		var customerSubscription models.StripeCustomerSubscription
		err := db.Where("user_id = ?", user.ID).First(&customerSubscription).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "No Stripe customer found for this user"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve customer data"})
			return
		}

		// Create the portal session
		params := &stripe.BillingPortalSessionParams{
			Customer:  stripe.String(customerSubscription.CustomerID),
			ReturnURL: stripe.String(req.ReturnUrl),
		}

		// Optional: Use a specific configuration if you have one
		configID := os.Getenv("STRIPE_PORTAL_CONFIGURATION_ID")
		if configID != "" {
			params.Configuration = stripe.String(configID)
		}

		portalSession, err := billingportalsession.New(params)
		if err != nil {
			log.Printf("Error creating portal session: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create portal session"})
			return
		}

		// Return the URL to redirect to
		c.JSON(http.StatusOK, gin.H{
			"url": portalSession.URL,
		})
	}
}
