"use client"; // This is a client component

import { useState } from 'react';

// Define interfaces based on the provided JSON snippet structure
interface LocalizedString {
  en: string;
  es: string;
}

interface ReadSection {
  code: LocalizedString;
}

interface MediaFilePaths {
  segmentations?: string; // Assuming these are paths/URLs initially
  histopathology_file?: string; // Assuming this is a path/URL initially
  url?: string; // Assuming this is a URL or path initially
  // Note: The file upload inputs handle the actual File objects, but the JSON might contain initial paths/URLs
}

interface MediaSection {
  simulate?: MediaFilePaths;
  watch?: string;
  visualize?: MediaFilePaths;
  listen?: string;
}

interface SectionContent {
  READ?: ReadSection;
  MEDIA?: MediaSection;
  // TODO: Add other potential sub-sections if they exist in the backend model
}

// Define a basic interface for the medical note sections structure
interface MedicalNoteSections {
  etiology?: SectionContent;
  symptoms?: SectionContent;
  diagnosis?: SectionContent;
  management?: SectionContent;
  review?: SectionContent;
  // TODO: Add interfaces for other sections as needed
}

// Define interface for the complete MedicalNote data structure expected by the backend
interface MedicalNoteFormData {
    id?: string; // Backend generates UUID, but model includes it
    title: LocalizedString;
    medical_system: string;
    overview: {
        code: LocalizedString;
    };
    sections: MedicalNoteSections;
}

export default function UploadMedicalNotePage() {
  // Add state for top-level Medical Note fields
  const [noteTitleEn, setNoteTitleEn] = useState('');
  const [noteTitleEs, setNoteTitleEs] = useState('');
  const [overviewCodeEn, setOverviewCodeEn] = useState('');
  const [overviewCodeEs, setOverviewCodeEs] = useState('');
  const [selectedMedicalSystem, setSelectedMedicalSystem] = useState('');

  // Add states for granular section data
  // Etiology
  const [etiologyReadCodeEn, setEtiologyReadCodeEn] = useState('');
  const [etiologyReadCodeEs, setEtiologyReadCodeEs] = useState('');
  // Symptoms
  const [symptomsReadCodeEn, setSymptomsReadCodeEn] = useState('');
  const [symptomsReadCodeEs, setSymptomsReadCodeEs] = useState('');
  // Diagnosis
  const [diagnosisReadCodeEn, setDiagnosisReadCodeEn] = useState('');
  const [diagnosisReadCodeEs, setDiagnosisReadCodeEs] = useState('');
  // Management
  const [managementReadCodeEn, setManagementReadCodeEn] = useState('');
  const [managementReadCodeEs, setManagementReadCodeEs] = useState('');
  // Review
  const [reviewReadCodeEn, setReviewReadCodeEn] = useState('');
  const [reviewReadCodeEs, setReviewReadCodeEs] = useState('');

  const [files, setFiles] = useState<Record<string, File | null>>({});
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // Define available systems
  const medicalSystems = [
    { value: 'cardiovascular', label: 'Cardiovascular System' },
    { value: 'pulmonary', label: 'Pulmonary System' },
    { value: 'gastrointestinal', label: 'Gastrointestinal System' },
    { value: 'neurological', label: 'Neurological System' },
    { value: 'musculoskeletal', label: 'Musculoskeletal System' },
    { value: 'endocrine', label: 'Endocrine System' },
    { value: 'renal', label: 'Renal System' },
    { value: 'hematological', label: 'Hematological System' },
    { value: 'immune', label: 'Immune System' },
    { value: 'integumentary', label: 'Integumentary System' },
  ];

  const handleFileChange = (fieldName: string, file: File | null) => {
    setFiles(prevFiles => ({
      ...prevFiles,
      [fieldName]: file,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setMessage('');
    setError('');

    // --- Data Assembly ---
    const formData = new FormData();

    // Assemble the full Medical Note JSON object from granular state variables
    const sections: MedicalNoteSections = {};

    // Assemble Etiology section
    if (etiologyReadCodeEn || etiologyReadCodeEs || fileFields.some(field => field.name.startsWith('sections.etiology.') && files[field.name])) {
        sections.etiology = {};
        if (etiologyReadCodeEn || etiologyReadCodeEs) {
            sections.etiology.READ = {
                code: {
                    en: etiologyReadCodeEn,
                    es: etiologyReadCodeEs,
                },
            };
        }
        if (fileFields.some(field => field.name.startsWith('sections.etiology.') && files[field.name])) {
             sections.etiology.MEDIA = {};
         }
    }

     // Assemble Symptoms section
     if (symptomsReadCodeEn || symptomsReadCodeEs || fileFields.some(field => field.name.startsWith('sections.symptoms.') && files[field.name])) {
        sections.symptoms = {};
        if (symptomsReadCodeEn || symptomsReadCodeEs) {
            sections.symptoms.READ = {
                code: {
                    en: symptomsReadCodeEn,
                    es: symptomsReadCodeEs,
                },
            };
        }
         if (fileFields.some(field => field.name.startsWith('sections.symptoms.') && files[field.name])) {
             sections.symptoms.MEDIA = {};
         }
    }

    // Assemble Diagnosis section
    if (diagnosisReadCodeEn || diagnosisReadCodeEs || fileFields.some(field => field.name.startsWith('sections.diagnosis.') && files[field.name])) {
        sections.diagnosis = {};
        if (diagnosisReadCodeEn || diagnosisReadCodeEs) {
            sections.diagnosis.READ = {
                code: {
                    en: diagnosisReadCodeEn,
                    es: diagnosisReadCodeEs,
                },
            };
        }
        if (fileFields.some(field => field.name.startsWith('sections.diagnosis.') && files[field.name])) {
             sections.diagnosis.MEDIA = {};
         }
    }

     // Assemble Management section
    if (managementReadCodeEn || managementReadCodeEs || fileFields.some(field => field.name.startsWith('sections.management.') && files[field.name])) {
        sections.management = {};
        if (managementReadCodeEn || managementReadCodeEs) {
            sections.management.READ = {
                code: {
                    en: managementReadCodeEn,
                    es: managementReadCodeEs,
                },
            };
        }
        if (fileFields.some(field => field.name.startsWith('sections.management.') && files[field.name])) {
             sections.management.MEDIA = {};
         }
    }

     // Assemble Review section
    if (reviewReadCodeEn || reviewReadCodeEs || fileFields.some(field => field.name.startsWith('sections.review.') && files[field.name])) {
        sections.review = {};
        if (reviewReadCodeEn || reviewReadCodeEs) {
            sections.review.READ = {
                code: {
                    en: reviewReadCodeEn,
                    es: reviewReadCodeEs,
                },
            };
        }
        if (fileFields.some(field => field.name.startsWith('sections.review.') && files[field.name])) {
             sections.review.MEDIA = {};
         }
    }

    // Create the complete medical note object
    const medicalNote: MedicalNoteFormData = {
      title: {
        en: noteTitleEn,
        es: noteTitleEs,
      },
      medical_system: selectedMedicalSystem,
      overview: {
        code: {
          en: overviewCodeEn,
          es: overviewCodeEs,
        },
      },
      sections: sections,
    };

    // Validate if the overall structure is as expected by the backend (basic check)
    // This check might need refinement based on required fields
    if (!medicalNote.title.en && !medicalNote.title.es || (!medicalNote.overview.code.en && !medicalNote.overview.code.es) || Object.keys(medicalNote.sections).length === 0) {
        setError('Please provide a Title, Overview Code (at least one language), and data for at least one section (JSON or files).');
        return;
    }

    // Append the assembled JSON data
    formData.append('noteJson', JSON.stringify(medicalNote));

    // Append files
    for (const fieldName in files) {
      const file = files[fieldName];
      if (file) {
        formData.append(fieldName, file);
      }
    }

    try {
      const response = await fetch('http://localhost:8001/api/medical_notes/upload', {
        method: 'POST',
        // Do NOT set Content-Type header for multipart/form-data; browser sets it automatically
        // headers: { 'Content-Type': 'multipart/form-data' }, // Incorrect way to set
        // TODO: Add authentication headers if required by your backend
        // 'Authorization': `Bearer ${yourAuthToken}`,
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setMessage(`Upload successful! Note ID: ${result.id}`);
        // Clear the textareas on success
        setEtiologyReadCodeEn('');
        setEtiologyReadCodeEs('');

        setSymptomsReadCodeEn('');
        setSymptomsReadCodeEs('');

        setDiagnosisReadCodeEn('');
        setDiagnosisReadCodeEs('');

        setManagementReadCodeEn('');
        setManagementReadCodeEs('');

        setReviewReadCodeEn('');
        setReviewReadCodeEs('');

        // Clear top-level fields
        setNoteTitleEn('');
        setNoteTitleEs('');
        setOverviewCodeEn('');
        setOverviewCodeEs('');
        setSelectedMedicalSystem('');

        setFiles({}); // Clear selected files
      } else {
        const errorText = await response.text();
        setError(`Upload failed: ${response.status} - ${errorText}`);
      }

    } catch (err: unknown) {
      if (err instanceof Error) {
         setError(`An error occurred: ${err.message}`);
      } else {
        setError('An unknown error occurred.');
      }
    }
  };

  // Define the file input fields based on the JSON structure and backend expectations
  const fileFields = [
    { name: 'sections.etiology.MEDIA.simulate.segmentations_file', label: 'Etiology Simulate Segmentations File' },
    { name: 'sections.etiology.MEDIA.visualize.segmentations_file', label: 'Etiology Visualize Segmentations File' },
    { name: 'sections.symptoms.MEDIA.simulate.segmentations_file', label: 'Symptoms Simulate Segmentations File' },
    { name: 'sections.symptoms.MEDIA.visualize.segmentations_file', label: 'Symptoms Visualize Segmentations File' },
    { name: 'sections.diagnosis.MEDIA.simulate.segmentations_file', label: 'Diagnosis Simulate Segmentations File' },
    { name: 'sections.diagnosis.MEDIA.visualize.histopathology_file', label: 'Diagnosis Visualize Histopathology File' },
    { name: 'sections.diagnosis.MEDIA.visualize.segmentations_file', label: 'Diagnosis Visualize Segmentations File' },
    { name: 'sections.management.MEDIA.simulate.segmentations_file', label: 'Management Simulate Segmentations File' },
    { name: 'sections.management.MEDIA.visualize.segmentations_file', label: 'Management Visualize Segmentations File' },
    { name: 'sections.review.MEDIA.simulate.segmentations_file', label: 'Review Simulate Segmentations File' },
     // Note: Review Visualize URL was a local path in example, treating as uploadable file
    { name: 'sections.review.MEDIA.visualize.url_file', label: 'Review Visualize Image File' },
    // Add other file fields as needed based on your full structure
  ];

  return (
    <div style={{ 
      maxWidth: '800px', 
      margin: '0 auto', 
      padding: '40px 20px',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      backgroundColor: '#f8f9fa'
    }}>
      <h1 style={{ 
        textAlign: 'center', 
        marginBottom: '30px',
        color: '#1a365d',
        fontSize: '2rem'
      }}>Upload Medical Note</h1>

      {message && (
        <div style={{ 
          backgroundColor: '#d1fae5', 
          color: '#065f46', 
          padding: '12px', 
          borderRadius: '6px',
          marginBottom: '20px',
          textAlign: 'center',
          border: '1px solid #a7f3d0'
        }}>
          {message}
        </div>
      )}
      
      {error && (
        <div style={{ 
          backgroundColor: '#fee2e2', 
          color: '#991b1b', 
          padding: '12px', 
          borderRadius: '6px',
          marginBottom: '20px',
          textAlign: 'center',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{ 
        backgroundColor: 'white', 
        padding: '30px', 
        borderRadius: '12px', 
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        {/* Top-level Medical Note Fields */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ 
            color: '#1a365d', 
            fontSize: '1.5rem', 
            marginBottom: '20px',
            borderBottom: '2px solid #e5e7eb',
            paddingBottom: '10px'
          }}>
            Basic Information
          </h2>
          
          <div style={{ marginBottom: '20px' }}>
            <label style={{ 
              display: 'block', 
              marginBottom: '8px', 
              color: '#374151',
              fontWeight: '500'
            }}>
              Medical Note Title
            </label>
            <div style={{ display: 'grid', gap: '15px' }}>
              <div>
                <input
                  type="text"
                  id="noteTitleEn"
                  value={noteTitleEn}
                  onChange={(e) => setNoteTitleEn(e.target.value)}
                  style={{ 
                    width: '100%', 
                    padding: '12px', 
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '1rem',
                    backgroundColor: 'white',
                    color: '#1f2937'
                  }}
                  placeholder='Enter title in English'
                  required
                />
              </div>
              <div>
                <input
                  type="text"
                  id="noteTitleEs"
                  value={noteTitleEs}
                  onChange={(e) => setNoteTitleEs(e.target.value)}
                  style={{ 
                    width: '100%', 
                    padding: '12px', 
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '1rem',
                    backgroundColor: 'white',
                    color: '#1f2937'
                  }}
                  placeholder='Enter title in Spanish'
                  required
                />
              </div>
            </div>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ 
              display: 'block', 
              marginBottom: '8px', 
              color: '#374151',
              fontWeight: '500'
            }}>
              Overview Code
            </label>
            <div style={{ display: 'grid', gap: '15px' }}>
              <textarea
                id="overviewCodeEn"
                value={overviewCodeEn}
                onChange={(e) => setOverviewCodeEn(e.target.value)}
                rows={3}
                style={{ 
                  width: '100%', 
                  padding: '12px', 
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem',
                  resize: 'vertical',
                  backgroundColor: 'white',
                  color: '#1f2937'
                }}
                placeholder='Enter overview code in English'
              />
              <textarea
                id="overviewCodeEs"
                value={overviewCodeEs}
                onChange={(e) => setOverviewCodeEs(e.target.value)}
                rows={3}
                style={{ 
                  width: '100%', 
                  padding: '12px', 
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem',
                  resize: 'vertical',
                  backgroundColor: 'white',
                  color: '#1f2937'
                }}
                placeholder='Enter overview code in Spanish'
              />
            </div>
          </div>
        </div>

        {/* System Selection */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ 
            color: '#1a365d', 
            fontSize: '1.5rem', 
            marginBottom: '20px',
            borderBottom: '2px solid #e5e7eb',
            paddingBottom: '10px'
          }}>
            Medical System
          </h2>
          <select
            value={selectedMedicalSystem}
            onChange={(e) => setSelectedMedicalSystem(e.target.value)}
            style={{ 
              width: '100%', 
              padding: '12px', 
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '1rem',
              backgroundColor: 'white',
              color: '#1f2937'
            }}
            required
          >
            <option value="">Select a medical system</option>
            {medicalSystems.map((system) => (
              <option key={system.value} value={system.value}>
                {system.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sections */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ 
            color: '#1a365d', 
            fontSize: '1.5rem', 
            marginBottom: '20px',
            borderBottom: '2px solid #e5e7eb',
            paddingBottom: '10px'
          }}>
            Section Details
          </h2>

          {/* Section Components */}
          {['etiology', 'symptoms', 'diagnosis', 'management', 'review'].map((section) => (
            <div key={section} style={{ 
              marginBottom: '30px',
              padding: '20px',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              backgroundColor: '#f9fafb'
            }}>
              <h3 style={{ 
                color: '#1a365d', 
                fontSize: '1.25rem', 
                marginBottom: '15px',
                textTransform: 'capitalize'
              }}>
                {section}
              </h3>

              {/* READ Content */}
              <div style={{ marginBottom: '20px' }}>
                <h4 style={{ 
                  color: '#374151', 
                  fontSize: '1rem', 
                  marginBottom: '12px',
                  fontWeight: '500'
                }}>
                  Content
                </h4>
                <div style={{ display: 'grid', gap: '15px' }}>
                  <textarea
                    id={`${section}ReadCodeEn`}
                    value={eval(`${section}ReadCodeEn`)}
                    onChange={(e) => eval(`set${section.charAt(0).toUpperCase() + section.slice(1)}ReadCodeEn(e.target.value)`)}
                    rows={3}
                    style={{ 
                      width: '100%', 
                      padding: '10px', 
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '0.95rem',
                      resize: 'vertical',
                      backgroundColor: 'white',
                      color: '#1f2937'
                    }}
                    placeholder='Enter code in English'
                  />
                  <textarea
                    id={`${section}ReadCodeEs`}
                    value={eval(`${section}ReadCodeEs`)}
                    onChange={(e) => eval(`set${section.charAt(0).toUpperCase() + section.slice(1)}ReadCodeEs(e.target.value)`)}
                    rows={3}
                    style={{ 
                      width: '100%', 
                      padding: '10px', 
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '0.95rem',
                      resize: 'vertical',
                      backgroundColor: 'white',
                      color: '#1f2937'
                    }}
                    placeholder='Enter code in Spanish'
                  />
                </div>
              </div>

              {/* Media Files */}
              <div>
                <h4 style={{ 
                  color: '#374151', 
                  fontSize: '1rem', 
                  marginBottom: '12px',
                  fontWeight: '500'
                }}>
                  Media Files
                </h4>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {fileFields
                    .filter(field => field.name.startsWith(`sections.${section}.MEDIA.`))
                    .map(field => {
                      const fileInputId = `file-${field.name.replace(/[^a-zA-Z0-9]/g, '_')}`;
                      const selectedFileName = files[field.name]?.name || 'No file chosen';
                      return (
                        <div key={field.name} style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                          <input
                            type="file"
                            id={fileInputId}
                            onChange={(e) => handleFileChange(field.name, e.target.files ? e.target.files[0] : null)}
                            style={{ display: 'none' }}
                          />
                          <label
                            htmlFor={fileInputId}
                            style={{
                              padding: '8px 16px',
                              backgroundColor: '#2563eb',
                              color: 'white',
                              borderRadius: '6px',
                              cursor: 'pointer',
                              fontSize: '0.9rem',
                              transition: 'background-color 0.2s',
                              whiteSpace: 'nowrap',
                              border: 'none'
                            }}
                          >
                            Choose File
                          </label>
                          <span style={{ 
                            color: '#4b5563',
                            fontSize: '0.9rem',
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {selectedFileName}
                          </span>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          ))}
        </div>

        <button
          type="submit"
          style={{ 
            width: '100%',
            padding: '14px',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '1rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
        >
          Upload Medical Note
        </button>
      </form>
    </div>
  );
}
