import unittest
from unittest.mock import patch, AsyncMock
import asyncio
from ..ai_models.articles_data import get_pubmed_results, parse_articles
from ..ai_models.question_query import generate_search_query



class TestArticlesDataIntegration(unittest.TestCase):
    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        self.loop.close()

    def test_real_api_call(self):
        async def _test():
            # Test parameters
            question = "What is the impact of GLP-1 RAs on skin?"
            query = await generate_search_query(question, '')
            
            # API parameters
            params = {
                'max_results': 10,
                'start_year': None,
                'end_year': None,
                'cursor': '*',
                'min_citations': None,
                'max_citations': None
            }

            # Make actual API call
            articles, error, next_cursor, total_results = await get_pubmed_results(
                query=query,
                **params
            )

            # Basic assertions
            self.assertIsNone(error)
            self.assertIsNotNone(articles)
            self.assertIsInstance(articles, list)
            
            # Print results for manual verification
            print("\nIntegration Test Results:")
            print(f"Query: {query}")
            print(f"Total results found: {total_results}")
            print(f"Articles retrieved: {len(articles)}")
            
            for article in articles:
                print("\nArticle Details:")
                print(f"Title: {article['title']}")
                print(f"Authors: <AUTHORS>
                print(f"Journal: {article['journal_title']}")
                print(f"Year: {article['publication_date']}")
                print(f"Citations: {article['citation_count']}")
       
                print(f"DOI: {article['doi']}")
                print(f"License: {article['license_type']}")
                print(f"Open Access: {article['is_open_access']}")

        self.loop.run_until_complete(_test())

if __name__ == '__main__':
    unittest.main()


