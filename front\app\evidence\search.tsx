'use client';

// ===== IMPORTS =====
import React, { useState, useCallback, useEffect, useMemo } from 'react';
import axios from 'axios';
import remarkGfm from 'remark-gfm';
import {  Filter, Lock, Unlock, FileText, Calendar, Book, Quote, History, ChevronDown, ChevronUp, Search } from 'lucide-react';
import { serialize } from 'next-mdx-remote/serialize';
import { MDXRemoteSerializeResult } from 'next-mdx-remote';
import { EvidencePyramid } from './search_components/EvidencePyramid';
import { SearchBar } from './search_components/SearchBar';
import FilterTab from './search_components/FilterTab';
import SearchHistory from './search_components/SearchHistory';
import { ArticleSkeleton } from './search_components/LoadingSkeleton';
import { EvidenceReport } from './search_components/EvidenceReport';
import ArticlesSummary from './search_components/ArticlesSummary';
import { useTheme } from '@/components/theme/theme-provider';
import { useLanguage } from '@/app/providers/language-provider';
import { useAuth } from "@clerk/nextjs";

// Import from search_api.ts instead of decode_api.ts
import { API_URL, getWebSocketUrl } from '../utils/search_api';

// Define interfaces for your data structures
interface Article {
  title: string;
  authors: string[];
  publication_date: string;
  journal_title: string;
  citation_count: number;
  doi_link: string;
  pubmed_link: string;
  pdf_link?: string;
  is_open_access: boolean;
}

interface Data {
  articles_summary: string;
  articles: Article[];
  query: string;
}

interface EvidenceReport {
  title: string;
  report: string;
  mdxSource: MDXRemoteSerializeResult;
}

interface AppliedFilters {
  startYear: number | null;
  endYear: number | null;
  publicationTypes: string[];
  minCitations: number | null;
  maxCitations: number | null;
}

// ===== MAIN COMPONENT =====
export default function ResearchMode() {
  const { t } = useLanguage();
  // ===== Clerk AUTH STATE =====
  const { getToken, isLoaded } = useAuth();
  const [token, setToken] = useState<string | null>(null);
  const [authLoading, setAuthLoading] = useState(true);

  // ===== STATE DECLARATIONS =====
  const { theme } = useTheme();
  const [question, setQuestion] = useState<string>('');
  
  const [data, setData] = useState<Data>({ articles_summary: '', articles: [], query: '' });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [mdxSource, setMdxSource] = useState<MDXRemoteSerializeResult | null>(null);
  const [startYear, setStartYear] = useState<string>('');
  const [endYear, setEndYear] = useState<string>('');
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilters | null>(null);
  const [rawText, setRawText] = useState<string>('');
  const [abstractSummaries, setAbstractSummaries] = useState<Array<{ summary: string }>>([]);
  const [studyDesigns, setStudyDesigns] = useState<Array<{ study_design: string }>>([]);

  const [cursor, setCursor] = useState<string>('*');
  const [hasMore, setHasMore] = useState<boolean>(true);

  const [publicationTypes, setPublicationTypes] = useState<string[]>([]);
  const [minCitations, setMinCitations] = useState<string>('');
  const [maxCitations, setMaxCitations] = useState<string>('');

  const [isFilterApplied, setIsFilterApplied] = useState<boolean>(false);
  const [filterChanged, setFilterChanged] = useState<boolean>(false);

  const [visiblePyramids, setVisiblePyramids] = useState<Record<string, boolean>>({});

  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [isHistoryVisible, setIsHistoryVisible] = useState(false);

  const [page, setPage] = useState<number>(1);

  const [searchHistory, setSearchHistory] = useState<Array<{ id: number, question: string, created_at: string }>>([]);
  
  // Add state to track if search has been initiated
  const [searchInitiated, setSearchInitiated] = useState<boolean>(false);

  // Load authentication token
  useEffect(() => {
    async function fetchToken() {
      if (isLoaded) {
        try {
          const fetchedToken = await getToken();
          setToken(fetchedToken);
        } catch (error) {
          console.error("Error fetching token:", error);
        } finally {
          setAuthLoading(false);
        }
      }
    }
    
    fetchToken();
  }, [getToken, isLoaded]);

  // Create an axios instance with better error handling
  const api = useMemo(() => {
    // Create a default instance that will be replaced with authenticated one once token is available
    const instance = axios.create({
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json'
      }
    });
    
    // Add response interceptor for better error handling
    instance.interceptors.response.use(
      response => response,
      error => {
        if (error.message === 'Network Error') {
          console.error('CORS error or network issue:', error);
          setError('Network error. This might be due to CORS settings or server availability.');
        } else if (error.response) {
          console.error('API Error Response:', {
            status: error.response.status,
            data: error.response.data,
            headers: error.response.headers
          });
        } else {
          console.error('API Error:', error.message);
        }
        throw error;
      }
    );
    
    return instance;
  }, [token]);
  
  const togglePyramidVisibility = (title: string, force?: boolean) => {
    setVisiblePyramids((prevState) => {
      const newState = { ...prevState };
      Object.keys(newState).forEach((key) => {
        if (key !== title) {
          newState[key] = false;
        }
      });
      newState[title] = force !== undefined ? force : !prevState[title];
      return newState;
    });
  };

  
  // Wrap fetchSearchHistory in useCallback
  const fetchSearchHistory = useCallback(async () => {
    if (!token) return; // Skip if no token available
    
    try {
      const response = await api.get(
        `${API_URL}/search-api/search-history/`
      );
      setSearchHistory(response.data);
    } catch (error) {
      console.error('Error fetching search history:', error);
    }
  }, [api, token]);

  // Update useEffect to include fetchSearchHistory
  useEffect(() => {
    if (!authLoading && token) {
      fetchSearchHistory();
    }
  }, [fetchSearchHistory, authLoading, token]);

  // If still loading auth or no token is available but auth is loaded, show nothing
  // The parent component will handle showing the loading state or auth required message
  if (authLoading || (!token && isLoaded)) {
    return null;
  }

  // ===== HANDLERS =====
  // SEARCH HANDLER 
  const handleSearch = async (e?: React.FormEvent, isLoadMore = false) => {
    e?.preventDefault();
    if (!token) return; // Don't perform search without token
    
    // Immediately hide hero section
    if (!isLoadMore) {
      setSearchInitiated(true);
    }
    
    // Force isInitialState to false after search submission
    if (!isLoadMore && isInitialState) {
      // This will force the UI to switch from hero to results view
      setData(prev => ({ ...prev, articles: prev.articles.length ? prev.articles : [] }));
    }
    
    if (!isLoadMore) {
      // Reset state for new search
      setError(null);
      setIsLoading(true);
      setMdxSource(null);
      setRawText('');
      setAbstractSummaries([]);
      setStudyDesigns([]);
      setCursor('*');
      setHasMore(true);
      setData({ articles_summary: '', articles: [], query: '' });
      setPage(1);
    } else {
      setIsLoading(true);
    }
    
    try {
      const searchParams = { 
        question, 
        publicationTypes: appliedFilters?.publicationTypes,
        startYear: appliedFilters?.startYear,
        endYear: appliedFilters?.endYear,
        cursor: isLoadMore ? cursor : '*',
        minCitations: appliedFilters?.minCitations,
        maxCitations: appliedFilters?.maxCitations,
        page: isLoadMore ? page + 1 : 1
      };


      // Step 1: Generate search query (only for new searches)
      let query = '';
      if (!isLoadMore) {
        try {
          const queryResponse = await api.post(
            `${API_URL}/search-api/search-query/`,
            { question, publicationTypes }
          );
          query = queryResponse.data.query;
        } catch (err) {
          if (axios.isAxiosError(err)) {
            if (err.response?.status === 403) {
              setError(err.response.data.message || 'Search limit reached. Please upgrade your subscription.');
              setIsLoading(false);
              return; // Exit early
            } else if (err.response?.status === 401) {
              setError('Authentication failed. Please sign in again.');
              setIsLoading(false);
              return; // Exit early
            } else if (!err.response) {
              setError('Network error. Please check your connection and try again.');
              setIsLoading(false);
              return; // Exit early
            }
          }
          throw err; // Re-throw other errors
        }
      }

      // Step 2: Fetch articles
      const articlesResponse = await api.post(
        `${API_URL}/search-api/articles/`, 
        { ...searchParams, query }
      );
      const { articles, has_more } = articlesResponse.data;

      
      
      // STEP 3: PARALLEL PROCESSING
      if (articles?.length > 0) {
        
        const parallelProcessingPromise = api.post(`${API_URL}/search-api/parallel-processing/`, { question, articles });
        
        // Step 4: Start WebSocket connection for streaming summary (only for new searches)
        if (!isLoadMore) {
          const wsUrl = getWebSocketUrl('/ws/summary/');
          const ws = new WebSocket(wsUrl);
          ws.onopen = () => {
            ws.send(JSON.stringify({ question, articles }));
          };
          ws.onmessage = (event: MessageEvent) => {
            const data = JSON.parse(event.data);
            if (data.summary) {
              setRawText(prevText => {
                const newText = prevText + data.summary;
                const mdxContent = newText.replace(/\[(\d+)\]/g, (match, p1) => 
                  `<Reference number="${p1}"><a href="#article-${p1}" class="reference-link">${p1}</a></Reference>`
                );
                serialize(mdxContent, {
                  mdxOptions: { remarkPlugins: [remarkGfm] },
                }).then(serializedMdx => {
                  setMdxSource(serializedMdx);
                }).catch(serializeError => {
                  console.error('MDX serialization error:', serializeError);
                });
                return newText;
              });
            }
          };
          ws.onerror = (error: Event) => {
            console.error('WebSocket error:', error);
            setError('An error occurred while fetching the summary');
          };
          ws.onclose = () => {
            console.log('WebSocket closed');
          };
        }

        const parallelResponse = await parallelProcessingPromise;
        const { abstract_summaries, study_designs } = parallelResponse.data;

        if (isLoadMore) {
          setData(prevData => ({
            ...prevData,
            articles: [...prevData.articles, ...articles],
            query: query  // Store the query
          }));
          setAbstractSummaries(prev => [...prev, ...abstract_summaries]);
          setStudyDesigns(prev => [...prev, ...study_designs]);
        } else {
          setData({ articles_summary: '', articles, query });
          setAbstractSummaries(abstract_summaries);
          setStudyDesigns(study_designs);
        }

        setCursor(has_more ? cursor : '*');
        setHasMore(has_more);

      } else {
        setHasMore(false);
        if (!isLoadMore) {
          setError('No articles found');
        }
      }

      // Save search history
      await api.post(
        `${API_URL}/search-api/search-history/`,
        { question }
      );

      // Fetch updated search history
      fetchSearchHistory();
    } catch (err) {
      console.error('Search error:', err);
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 403) {
          setError(err.response.data.message || 'Search limit reached. Please upgrade your subscription.');
          setIsLoading(false);
          return; // Exit early
        } else if (err.response?.status === 401) {
          setError('Authentication failed. Please sign in again.');
          setIsLoading(false);
          return; // Exit early
        } else if (!err.response) {
          setError('Network error. Please check your connection and try again.');
          setIsLoading(false);
          return; // Exit early
        }
        const errorMessage = err.response?.data?.message || err.response?.data?.error || err.message;
        setError(`An error occurred: ${errorMessage}`);
      } else {
        setError(`An unexpected error occurred: ${(err as Error).message}`);
      }
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };


// APPLY FILTERS HANDLER
  const handleApplyFilters = () => {
    setIsFilterApplied(true);
    setFilterChanged(false);
    console.log('Applying filters:', { startYear, endYear, publicationTypes, minCitations, maxCitations });
    setAppliedFilters({
      startYear: startYear ? parseInt(startYear) : null,
      endYear: endYear ? parseInt(endYear) : null,
      publicationTypes,
      minCitations: minCitations ? parseInt(minCitations) : null,
      maxCitations: maxCitations ? parseInt(maxCitations) : null
    });
  };


// LOAD MORE ARTICLES BUTTON
  const handleLoadMore = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setPage(prevPage => prevPage + 1);
    handleSearch(undefined, true);
  };


  // Add these functions to toggle visibility
  const toggleFilterVisibility = () => {
    // If filter is already visible, hide it
    // If filter is hidden, show it and hide history
    if (isFilterVisible) {
      setIsFilterVisible(false);
    } else {
      setIsFilterVisible(true);
      setIsHistoryVisible(false);
    }
  };
  
  const toggleHistoryVisibility = () => {
    // If history is already visible, hide it
    // If history is hidden, show it and hide filter
    if (isHistoryVisible) {
      setIsHistoryVisible(false);
    } else {
      setIsHistoryVisible(true);
      setIsFilterVisible(false);
    }
  };

  // Add a check for initial state - same as the chat component
  const isInitialState = !data.articles || data.articles.length === 0;
  
  // Use both isInitialState and searchInitiated to determine if hero should be shown
  const showHeroSection = isInitialState && !searchInitiated;

  // ===== RENDER =====
  return (
    <div className={`${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'} text-${theme === 'dark' ? 'gray-300' : 'gray-900'} min-h-screen flex flex-col`}>
      
      <div className='flex flex-1 relative'> 
        {/*------------------- SEARCH BAR -------------------*/}
        <div className='flex-1 p-4 pr-0 mr-0'>
        
          <div className='w-full max-w-4xl mx-auto'>
           {/* Hero Section - only shown when no results displayed AND search not initiated */}
           {showHeroSection ? (
              <div className="flex flex-col items-center justify-center h-[80vh] w-full px-4 text-center">
                <div className="max-w-4xl w-full">
                  <h1 className={`text-4xl font-bold mb-6 ${theme === 'dark' ? 'text-white' : 'text-[#091225]'}`}>
                    {t('common.projectCreation.evidenceSearch.title')}
                  </h1>
                  <p className={`text-xl mb-8 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('common.projectCreation.evidenceSearch.subtitle')}
                  </p>
                  
                  {/* Hero search input - improved styling */}
                  <div className={`${theme === 'dark' ? 'bg-transparent' : 'bg-white'} 
                    mx-auto w-full max-w-2xl p-8 rounded-2xl shadow-transparent
                    ${theme === 'dark' ? 'border-transparent' : 'border border-transparent'}`}>
                    
                    <form onSubmit={handleSearch} className="flex items-center w-full relative">
                      <textarea
                        value={question}
                        onChange={(e) => setQuestion(e.target.value)}
                        className={`flex-grow border ${
                          theme === 'dark' 
                            ? 'border-transparent bg-[#1e1e1e] text-gray-100 hover:bg-[#242424]' 
                            : 'border-transparent text-black bg-gray-100 hover:bg-gray-100'
                        } focus:outline-none focus:ring-2 ${
                          theme === 'dark' ? 'focus:ring-gray-600' : 'focus:ring-blue-200'
                        } rounded-3xl px-6 py-4 w-full pr-24 resize-none overflow-hidden text-lg shadow-inner transition-all duration-200`}
                        placeholder="Search for medical evidence..."
                        rows={2}
                        style={{ minHeight: '80px', maxHeight: '200px' }}
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                        <button 
                          type="submit"
                          className={`p-3 rounded-full ${
                            theme === 'dark' 
                              ? 'text-white hover:bg-neutral-700 bg-neutral-800' 
                              : 'text-white hover:bg-[#1e315a] bg-[#091225]'
                          } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                            theme === 'dark' ? 'focus:ring-gray-500' : 'focus:ring-blue-500'
                          } transition-all duration-200 shadow-md`}
                        >
                          <Search size={22} />
                        </button>
                      </div>
                    </form>

                    {/* Filter and History Buttons - Now inside the hero section */}
                    <div className="flex justify-center gap-6 mt-6">
                      {/* Filter Button */}
                      <button
                        onClick={toggleFilterVisibility}
                        className={`flex items-center justify-center gap-2 px-5 py-2.5 rounded-lg ${
                          theme === 'dark'
                            ? 'bg-neutral-800 text-gray-200 hover:bg-neutral-700 border border-neutral-700'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200 border border-gray-200'
                        } transition-colors shadow-sm`}
                      >
                        <Filter size={18} />
                        <span className="font-medium">{t('common.projectCreation.evidenceSearch.filters')}</span>
                      </button>
                      
                      {/* History Button */}
                      <button
                        onClick={toggleHistoryVisibility}
                        className={`flex items-center justify-center gap-2 px-5 py-2.5 rounded-lg ${
                          theme === 'dark'
                            ? 'bg-neutral-800 text-gray-200 hover:bg-neutral-700 border border-neutral-700'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200 border border-gray-200'
                        } transition-colors shadow-sm`}
                      >
                        <History size={18} />
                        <span className="font-medium">{t('common.projectCreation.evidenceSearch.history')}</span>
                      </button>
                    </div>
                  </div>
                  
                  {/* Example research questions with icons */}
                  <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-5 text-left max-w-2xl mx-auto">
                    <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                      ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                      onClick={() => {
                        setQuestion(t('common.projectCreation.evidenceSearch.exampleQuestions.sglt2'));
                      }}>
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                        theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                      }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <div>
                        <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                          {t('common.projectCreation.evidenceSearch.exampleQuestions.sglt2')}
                        </h3>
                      </div>
                    </div>
                    <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                      ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                      onClick={() => {
                        setQuestion(t('common.projectCreation.evidenceSearch.exampleQuestions.cbt'));
                      }}>
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                        theme === 'dark' ? 'text-green-400' : 'text-green-600'
                      }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                      </svg>
                      <div>
                        <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                          {t('common.projectCreation.evidenceSearch.exampleQuestions.cbt')}
                        </h3>
                      </div>
                    </div>
                    <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                      ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                      onClick={() => {
                        setQuestion(t('common.projectCreation.evidenceSearch.exampleQuestions.biologics'));
                      }}>
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                        theme === 'dark' ? 'text-purple-400' : 'text-purple-600'
                      }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L12 20.364l7.682-7.682a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L12 7.636l-1.318-1.318a6 6 0 00-3.86-.517l-.318-.158a6 6 0 01-3.86-.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                      </svg>
                      <div>
                        <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                          {t('common.projectCreation.evidenceSearch.exampleQuestions.biologics')}
                        </h3>
                      </div>
                    </div>
                    <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                      ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                      onClick={() => {
                        setQuestion(t('common.projectCreation.evidenceSearch.exampleQuestions.longCovid'));
                      }}>
                      <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                        theme === 'dark' ? 'text-red-400' : 'text-red-600'
                      }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                      <div>
                        <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                          {t('common.projectCreation.evidenceSearch.exampleQuestions.longCovid')}
                        </h3>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <>
                {/* Original search form - shown when results are present */}
                <form onSubmit={handleSearch} >
                  <SearchBar
                    question={question}
                    setQuestion={setQuestion}
                    placeholder="Ask a question"
                  />
                </form>
            
                {/* Desktop Buttons Container - Below search bar */}
                <div className="flex justify-center gap-4 mt-2 mb-4">
                  {/* Filter Button */}
                  <button
                    onClick={toggleFilterVisibility}
                    className={`flex-1 max-w-[150px] rounded-lg px-4 py-2 flex items-center justify-center gap-2 ${theme === 'dark' ? 'text-neutral-200 hover:bg-neutral-700 border border-neutral-700' : 'text-gray-800 hover:bg-gray-200 border border-gray-200'}`}
                  >
                    <Filter size={20} />
                    <span>Filters</span>
                  </button>
                  
                  {/* History Button */}
                  <button
                    onClick={toggleHistoryVisibility}
                    className={`flex-1 max-w-[150px] rounded-lg px-4 py-2 flex items-center justify-center gap-2 ${theme === 'dark' ? 'text-neutral-200 hover:bg-neutral-700 border border-neutral-700' : 'text-gray-800 hover:bg-gray-200 border border-gray-200'}`}
                  >
                    <History size={20} />
                    <span>History</span>
                  </button>
                </div>
              </>
            )}

            {/*Loading Skeleton*/}
            {isLoading && (
              <div className="flex justify-center items-center mb-4">
                <div className={`animate-spin rounded-full h-5 w-5 border-b-2 ${theme === 'dark' ? 'border-neutral-400' : 'border-gray-500'} mr-2`}></div>
                <span className={theme === 'dark' ? 'text-neutral-400' : 'text-gray-500'}>Searching...</span>
              </div>
            )}
            
            {/*Error Message*/}
            {error && <p className="text-red-500 mb-4">{error}</p>}

            {/*------------------ARTICLES SUMMARY------------------  */}
            <ArticlesSummary 
              mdxSource={mdxSource}
              isLoading={isLoading}
              rawText={rawText}
              articles={data.articles}
            />

            {/*--------------- ARTICLES ---------------*/}
            {data.articles && data.articles.length > 0 ? (
              <div className="mt-8 space-y-4 sm:space-y-6">
                {data.articles.map((article, index) => (
                  <div key={index} id={`article-${index + 1}`} className={`${theme === 'dark' ? 'bg-neutral-900' : 'bg-white'} p-3 sm:p-4 md:p-6 rounded-lg shadow-md`}>
                    <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-2 flex items-start -ml-1">
                       <span className="hover:bg-neutral-700 bg-neutral-800 text-white rounded-full w-4 h-4 flex items-center justify-center mr-1 ml-0 mt-1 flex-shrink-0 text-xs sm:text-sm">
                        {index + 1}
                       </span>
                      <a href={article.doi_link} target="_blank" rel="noopener noreferrer" className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} hover:underline`}>
                       {article.title}
                      </a>
                    </h3>
                    <p className={`text-xs sm:text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-black'} mb-2 sm:mb-3`}>
                      {article.authors.slice(0, 2).join(', ')}
                      {article.authors.length > 2 ? ' et al.' : ''}
                    </p>
                    <div className="flex flex-wrap items-center gap-2 sm:gap-4 mb-2 sm:mb-3 text-xs sm:text-sm">
                      <div className="flex items-center">
                        <Calendar className={theme === 'dark' ? 'text-gray-300 mr-1' : 'text-gray-900 mr-1'} size={14} />
                        <span>{article.publication_date}</span>
                      </div>
                      {article.journal_title && (
                        <div className="flex items-center">
                          <Book className={theme === 'dark' ? 'text-gray-300 mr-1' : 'text-gray-900 mr-1'} size={14} />
                          <span>{article.journal_title}</span>
                        </div>
                      )}
                      <div className="flex items-center">
                        <Quote className={theme === 'dark' ? 'text-gray-300 mr-1' : 'text-gray-900 mr-1'} size={14} />
                        <span>{article.citation_count} citations</span>
                      </div>
                    </div>

                    {abstractSummaries[index] && (
                      <p className={`${theme === 'dark' ? 'bg-neutral-800' : 'bg-gradient-to-b from-gray-100 to-gray-200'} p-2 sm:p-3 rounded mb-2 sm:mb-3 text-xs sm:text-sm italic`}>
                        {abstractSummaries[index].summary}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-2 sm:gap-4 mt-2 sm:mt-4 text-xs sm:text-sm">
                      <div className={`flex items-center font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} px-1 py-1 rounded`}>
                        {article.is_open_access ? 
                          <Unlock className={theme === 'dark' ? 'text-gray-300 mr-1' : 'text-gray-700 mr-1'} size={14} /> : 
                          <Lock className={theme === 'dark' ? 'text-gray-300 mr-1' : 'text-gray-700 mr-1'} size={14} />
                        }
                        {article.is_open_access ? 'Open Access' : 'Non-Open Access'}
                      </div>
                      {article.pdf_link && (
                        <a 
                          href={article.pdf_link} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className={`flex items-center font-medium ${theme === 'dark' ? 'text-gray-300 px-1 py-1 rounded hover:bg-gray-700 hover:text-gray-100' : 'text-gray-700 px-1 py-1 rounded hover:bg-gray-400 hover:text-gray-100'}`}
                        >
                          <FileText className={theme === 'dark' ? 'text-gray-300 mr-1' : 'text-gray-700 mr-1'} size={14} />
                          PDF
                        </a>
                      )}
                    </div>

                    <div className='flex flex-col sm:flex-col lg:flex-row'>
                      {/* Buttons container */}
                      <div className="flex flex-col sm:gap-4 mt-2 sm:mt-4 text-xs sm:text-sm">
                        {studyDesigns[index] && (
                          <button
                            onClick={() => togglePyramidVisibility(article.title)}
                            className={`flex items-center font-semibold ${theme === 'dark' ? 'text-white py-2 px-1 rounded hover:bg-gray-700 hover:text-white' : 'text-gray-900 py-2 px-1 rounded hover:bg-gray-700 hover:text-white'} transition duration-300`}
                          >
                            <EvidencePyramid studyDesign={studyDesigns[index].study_design} isIcon={true} />
                            <span className="ml-2 mr-2">{studyDesigns[index].study_design}</span>
                            {visiblePyramids[article.title] ? (
                              <ChevronUp size={14} />
                            ) : (
                              <ChevronDown size={14} />
                            )}
                          </button>
                        )}
                        {/* Pyramid content */}
                      {visiblePyramids[article.title] && (
                        <div className="mt-2">
                          <EvidencePyramid studyDesign={studyDesigns[index].study_design} />
                        </div>
                      )}
                       
                      </div>

                      {/* Evidence Report content */}
                      
                        <div className="mt-4 flex flex-col justify-end h-full">
                          <EvidenceReport 
                            article={article} 
                            togglePyramidVisibility={togglePyramidVisibility} 
                          />
                        </div>
                    
                    </div>
                    
                </div>
                ))}
              </div>
            ) : isLoading ? (
              <div className="mt-8 space-y-4 sm:space-y-6">
                {[...Array(3)].map((_, index) => (
                  <ArticleSkeleton key={index} />
                ))}
              </div>
            ) : null}
    
            {/*Load More Articles Button*/}
            {data.articles && data.articles.length > 0 && (
              <div className="mt-8 flex justify-center">
                <button
                  onClick={handleLoadMore}
                  className={`${theme === 'dark' ? 'bg-neutral-800 hover:bg-neutral-700 text-neutral-200' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'} border-neutral-700 font-bold py-2 px-4 rounded ${
                    !hasMore || isLoading ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  disabled={isLoading || !hasMore}
                >
                  {isLoading ? 'Loading...' : hasMore ? 'Load More Articles' : 'No More Articles'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Right side tabs - Both Filter and History panels positioned fixed on right */}
        <div className="hidden md:block">
          {/* Filter panel - visible only when isFilterVisible is true */}
          <div className={`fixed top-12 right-0 h-[calc(100vh-48px)] z-20 transition-all duration-300 ${isFilterVisible ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'}`}>
            <FilterTab
              isFilterVisible={isFilterVisible}
              toggleFilterVisibility={toggleFilterVisibility}
              startYear={startYear}
              setStartYear={setStartYear}
              endYear={endYear}
              setEndYear={setEndYear}
              minCitations={minCitations}
              setMinCitations={setMinCitations}
              maxCitations={maxCitations}
              setMaxCitations={setMaxCitations}
              publicationTypes={publicationTypes}
              setPublicationTypes={setPublicationTypes}
              handleApplyFilters={handleApplyFilters}
              isFilterApplied={isFilterApplied}
              filterChanged={filterChanged}
              setFilterChanged={setFilterChanged}
              setIsFilterApplied={setIsFilterApplied}
            />
          </div>
          
          {/* History panel - visible only when isHistoryVisible is true */}
          <div className={`fixed top-12 right-0 h-[calc(100vh-48px)] z-20 transition-all duration-300 ${isHistoryVisible ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'}`}>
            <SearchHistory
              isHistoryVisible={isHistoryVisible}
              toggleHistoryVisibility={toggleHistoryVisibility}
              searchHistory={searchHistory}
              setQuestion={setQuestion}
            />
          </div>
        </div>
        
        {/* Mobile overlay and panels */}
        <div className={`md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300 ${(isFilterVisible || isHistoryVisible) ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'}`}
          onClick={() => {
            // Close both panels
            setIsFilterVisible(false);
            setIsHistoryVisible(false);
          }}
        />
        
        {/* Mobile Filter Panel (slide in from bottom) */}
        <div className={`md:hidden fixed bottom-0 left-0 right-0 z-40 ${theme === 'dark' ? 'bg-[#1b1b1b]' : 'bg-white'} rounded-t-xl transform transition-transform duration-300 ${isFilterVisible ? 'translate-y-0' : 'translate-y-full'}`}
          style={{ maxHeight: '80vh', overflowY: 'auto' }}
        >
          <div className="p-4">
            <div className="flex justify-between items-center mb-4 border-b pb-2">
              <h2 className={`text-lg font-semibold flex items-center ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                <Filter size={20} className="mr-2" />
                Filters
              </h2>
              <button 
                onClick={() => setIsFilterVisible(false)}
                className={`p-1 rounded-full ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-200'}`}
              >
                ✕
              </button>
            </div>
            {isFilterVisible && (
              <div className="px-2">
                {/* Date Range Controls */}
                <div className="mb-4">
                  <h3 className={`font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Date Range</h3>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      placeholder="Start Year"
                      value={startYear}
                      onChange={(e) => setStartYear(e.target.value)}
                      className={`flex-1 p-2 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300'}`}
                    />
                    <input
                      type="number"
                      placeholder="End Year"
                      value={endYear}
                      onChange={(e) => setEndYear(e.target.value)}
                      className={`flex-1 p-2 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300'}`}
                    />
                  </div>
                </div>
                
                {/* Citation Range Controls */}
                <div className="mb-4">
                  <h3 className={`font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Citation Range</h3>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      placeholder="Min Citations"
                      value={minCitations}
                      onChange={(e) => setMinCitations(e.target.value)}
                      className={`flex-1 p-2 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300'}`}
                    />
                    <input
                      type="number"
                      placeholder="Max Citations"
                      value={maxCitations}
                      onChange={(e) => setMaxCitations(e.target.value)}
                      className={`flex-1 p-2 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300'}`}
                    />
                  </div>
                </div>
                
                {/* Publication Types */}
                <div className="mb-4">
                  <h3 className={`font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Publication Types</h3>
                  <div className="flex flex-wrap gap-2">
                    {[
                      'Systematic Review',
                      'Meta-Analysis',
                      'Randomized Controlled Trial',
                      'Case-Control Study',
                      'Cohort Study',
                      'Cross-sectional Study',
                    ].map(type => (
                      <button
                        key={type}
                        onClick={() => {
                          setPublicationTypes(prev => 
                            prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]
                          );
                          setFilterChanged(true);
                          setIsFilterApplied(false);
                        }}
                        className={`px-3 py-1 text-sm rounded-full ${
                          publicationTypes.includes(type) 
                            ? 'bg-neutral-600 text-white' 
                            : theme === 'dark' ? 'bg-neutral-700 text-neutral-300' : 'bg-gray-200 text-gray-800'
                        }`}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Apply Button */}
                <button
                  onClick={() => {
                    handleApplyFilters();
                    setIsFilterVisible(false);
                  }}
                  className="w-full p-3 bg-neutral-600 text-white rounded-lg font-medium mt-4 hover:bg-neutral-700"
                >
                  Apply Filters
                </button>
              </div>
            )}
          </div>
        </div>
        
        {/* Mobile History Panel (slide in from bottom) */}
        <div className={`md:hidden fixed bottom-0 left-0 right-0 z-40 ${theme === 'dark' ? 'bg-neutral-900' : 'bg-white'} rounded-t-xl transform transition-transform duration-300 ${isHistoryVisible ? 'translate-y-0' : 'translate-y-full'}`}
          style={{ maxHeight: '80vh', overflowY: 'auto' }}
        >
          <div className="p-4">
            <div className="flex justify-between items-center mb-4 border-b pb-2">
              <h2 className={`text-lg font-semibold flex items-center ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                <History size={20} className="mr-2" />
                Search History
              </h2>
              <button 
                onClick={() => setIsHistoryVisible(false)}
                className={`p-1 rounded-full ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-200'}`}
              >
                ✕
              </button>
            </div>
            {isHistoryVisible && searchHistory.length > 0 && (
              <div className="space-y-2">
                {searchHistory.map(item => (
                  <button
                    key={item.id}
                    onClick={() => {
                      setQuestion(item.question);
                      setIsHistoryVisible(false);
                    }}
                    className={`w-full text-left p-2 ${theme === 'dark' ? 'hover:bg-neutral-800' : 'hover:bg-gray-100'} rounded`}
                  >
                    <p className={`line-clamp-2 ${theme === 'dark' ? 'text-neutral-300' : 'text-gray-800'}`}>{item.question}</p>
                    <p className={`text-xs ${theme === 'dark' ? 'text-neutral-400' : 'text-gray-500'} mt-1`}>
                      {new Date(item.created_at).toLocaleString()}
                    </p>
                  </button>
                ))}
              </div>
            )}
            {isHistoryVisible && searchHistory.length === 0 && (
              <p className={`text-center ${theme === 'dark' ? 'text-neutral-400' : 'text-gray-500'} py-4`}>No search history yet</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}