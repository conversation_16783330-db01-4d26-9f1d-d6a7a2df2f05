'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { History, Plus } from 'lucide-react';
import { getWebSocketUrl } from '@/app/utils/search_api';
import { useTheme } from '@/components/theme/theme-provider';
import Image from 'next/image';
import { NewChatButton } from './NewChatButton';

interface Conversation {
  id: number;
  question: string;
  conversation_id: string;
  first_created_at: string;
}

interface ConversationListProps {
  token: string;
  isMenuExpanded: boolean;
  setIsMenuExpanded: (value: boolean) => void;
  onConversationSelect: (conversationId: string) => void;
  handleNewChat: () => void;
  isMobile?: boolean;
}

function groupConversations(conversations: Conversation[]) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);
  
  const lastMonth = new Date(today);
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  return {
    today: conversations.filter(conv => new Date(conv.first_created_at) >= today),
    yesterday: conversations.filter(conv => {
      const date = new Date(conv.first_created_at);
      return date >= yesterday && date < today;
    }),
    lastWeek: conversations.filter(conv => {
      const date = new Date(conv.first_created_at);
      return date >= lastWeek && date < yesterday;
    }),
    lastMonth: conversations.filter(conv => {
      const date = new Date(conv.first_created_at);
      return date >= lastMonth && date < lastWeek;
    }),
    older: conversations.filter(conv => new Date(conv.first_created_at) < lastMonth)
  };
}

export default function ConversationList({ 
  token, 
  isMenuExpanded, 
  setIsMenuExpanded, 
  onConversationSelect,
  handleNewChat,
  isMobile = false 
}: ConversationListProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const conversationsSocket = useRef<WebSocket | null>(null);
  const { theme } = useTheme();

  // Set initial state to true for desktop
  useEffect(() => {
    if (!isMobile) {
      setIsMenuExpanded(true);
    } else {
      setIsMenuExpanded(false);
    }
  }, [isMobile, setIsMenuExpanded]);

  const fetchUserConversations = useCallback(() => {
    if (!token || !isMenuExpanded || !conversationsSocket.current) return;
    
    conversationsSocket.current.send(JSON.stringify({
      action: 'get_conversations',
      created_at: 'created_at',
      token: token
    }));
  }, [token, isMenuExpanded]);

  useEffect(() => {
    if (token && isMenuExpanded) {
      fetchUserConversations();
    }
  }, [token, isMenuExpanded, fetchUserConversations]);
  // WebSocket connection and data fetching
  useEffect(() => {
    const conversationsWebSocketUrl = `${getWebSocketUrl('/ws/conversations/')}?token=${token}`;
    conversationsSocket.current = new WebSocket(conversationsWebSocketUrl);
    
    conversationsSocket.current.onopen = () => {
      console.log('Conversations WebSocket connected');
      fetchUserConversations();
    };

    conversationsSocket.current.onmessage = handleConversationsMessage;

    return () => {
      if (conversationsSocket.current) {
        conversationsSocket.current.close();
      }
    };
  }, [token, fetchUserConversations]);

  const handleConversationsMessage = (event: MessageEvent) => {
    const data = JSON.parse(event.data);
    if (data.type === 'conversations') {
      setConversations(data.conversations);
    }
  };

  

  const handleConversationClick = (conversationId: string) => {
    onConversationSelect(conversationId);
    setIsMenuExpanded(false);
  };

  const renderGroupedConversations = () => {
    const grouped = groupConversations(conversations);
    
    return (
      <>
        {Object.entries({
          today: 'Today',
          yesterday: 'Yesterday',
          lastWeek: 'Last Week',
          lastMonth: 'Last Month',
          older: 'Older'
        }).map(([key, title]) => {
          const conversations = grouped[key as keyof typeof grouped];
          if (conversations.length === 0) return null;

          return (
            <div key={key} className="mb-4">
              <h3 className={`text-xs font-semibold ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} px-2 mb-1`}>
                {title}
              </h3>
              {conversations.map((conv, index) => (
                <div key={conv.conversation_id}>
                  <button
                    onClick={() => handleConversationClick(conv.conversation_id)}
                    className={`w-full text-left p-1.5 ${
                      theme === 'dark' 
                        ? 'text-gray-300 hover:bg-[#1a2234] hover:text-white' 
                        : 'text-black hover:bg-gray-200'
                    } rounded`}
                  >
                    <div className="text-xs leading-tight break-words whitespace-pre-wrap line-clamp-2">
                      {conv.question}
                    </div>
                  </button>
                  {index < conversations.length - 1 && (
                    <div className="mx-2" />
                  )}
                </div>
              ))}
            </div>
          );
        })}
      </>
    );
  };

  // Mobile view
  if (isMobile) {
    return (
      <div className={`md:hidden ${theme === 'dark' ? 'bg-[#0b0f1c]' : 'bg-white'} 
        flex flex-col transition-all duration-300 ease-in-out fixed top-16 
        h-[calc(100%-4rem)] z-40 
        ${isMenuExpanded ? 'w-64' : 'w-0'} 
        ${isMenuExpanded ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="flex flex-col items-center py-4 overflow-y-auto h-full">
          <div className="w-full px-2">
            {renderGroupedConversations()}
          </div>
        </div>
      </div>
    );
  }

  // Desktop view
  return (
    <div className="hidden md:block fixed top-0 h-screen z-30">
      <div className={`fixed top-0 left-0 transition-all duration-300 ease-in-out ${
        isMenuExpanded ? 'w-64' : 'w-16'
      }`}>
        <div className={`${theme === 'dark' ? 'bg-[#0b0f1c]' : 'bg-white'} 
          h-screen overflow-hidden ${isMenuExpanded ? 'w-64' : 'w-16'}
          transition-all duration-300 transform-none`}>
          <div className="flex flex-col h-full">
            {/* Logo and Buttons Section */}
            <div className="p-2 shrink-0">
              <div className={`flex ${isMenuExpanded ? 'flex-row items-center justify-between gap-4' : 'flex-col items-center gap-2'}`}>
                {/* Logo */}
                {isMenuExpanded ? (
                  <Image 
                    src={theme === 'dark' ? '/decodemed_text_dark.svg' : '/decodemed_text.svg'} 
                    alt="DecodeMed Text" 
                    width={72}
                    height={16}
                    className="w-full max-w-[120px]"
                  />
                ) : (
                  <Image 
                    src={theme === 'dark' ? '/decodemed_dark.svg' : '/decodemed.svg'} 
                    alt="DecodeMed Icon" 
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                )}
                
                {/* History Button */}
                <button
                  onClick={() => setIsMenuExpanded(!isMenuExpanded)}
                  className={`flex items-center justify-center p-2 rounded-xl ${
                    theme === 'dark' 
                      ? 'text-gray-300 hover:bg-[#1a2234] hover:text-white' 
                      : 'text-black hover:bg-gray-200'
                  }`}
                  title={isMenuExpanded ? "Collapse History" : "Expand History"}
                >
                  <History size={20} />
                </button>
              </div>
              

              {/* New Chat Button */}
              {isMenuExpanded ? (
                <div className="hidden md:block">
                  <NewChatButton handleNewChat={handleNewChat} />
                </div>
              ) : (
                <button
                  onClick={handleNewChat}
                  className={`w-full flex items-center justify-center p-2 mt-2 rounded-xl ${
                    theme === 'dark' 
                      ? 'text-gray-300 hover:bg-[#1a2234] hover:text-white' 
                      : 'text-black hover:bg-gray-200'
                  }`}
                  title="New Chat"
                >
                  <Plus size={20} />
                </button>
              )}
            </div>
            {/* Conversations list - only visible when expanded */}
            {isMenuExpanded && (
              <div className="flex-1 min-h-0">
                <div className={`h-full overflow-y-auto py-4 
                  ${theme === 'dark' 
                    ? '[&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-[#050912] [&::-webkit-scrollbar-thumb]:bg-gray-600 [&::-webkit-scrollbar-thumb]:rounded-full' 
                    : '[&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full'
                  }`}>
                  <div className="w-full px-2">
                    {renderGroupedConversations()}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
