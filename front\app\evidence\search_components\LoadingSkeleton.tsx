import React from 'react';

export const SummarySkeleton: React.FC = () => (
  <div className="mt-4 bg-gradient-to-b from-gray-100 to-gray-200 dark:from-neutral-800 dark:to-neutral-700 p-3 sm:p-4 md:p-6 rounded-lg shadow-md animate-pulse">
    <div className="h-4 bg-gray-300 dark:bg-neutral-600 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-300 dark:bg-neutral-600 rounded w-5/6 mb-2"></div>
    <div className="h-4 bg-gray-300 dark:bg-neutral-600 rounded w-4/5"></div>
  </div>
);

export const ArticleSkeleton: React.FC = () => (
  <div className="bg-white dark:bg-neutral-800 p-3 sm:p-4 md:p-6 rounded-lg shadow-md animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-neutral-600 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-300 dark:bg-neutral-600 rounded w-1/2 mb-2"></div>
    <div className="h-4 bg-gray-300 dark:bg-neutral-600 rounded w-1/3 mb-2"></div>
    <div className="h-20 bg-gray-300 dark:bg-neutral-600 rounded mb-2"></div>
    <div className="flex space-x-2">
      <div className="h-6 bg-gray-300 dark:bg-neutral-600 rounded w-24"></div>
      <div className="h-6 bg-gray-300 dark:bg-neutral-600 rounded w-24"></div>
    </div>
  </div>
);

export const LoadingSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      <SummarySkeleton />
      {[...Array(3)].map((_, index) => (
        <ArticleSkeleton key={index} />
      ))}
    </div>
  );
};
