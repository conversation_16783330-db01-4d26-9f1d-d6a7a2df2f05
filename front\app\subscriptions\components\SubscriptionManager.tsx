'use client';

import { useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';
import { UsageStats, formatSubscriptionStatus, formatSubscriptionType } from '@/app/utils/stripe';
import { cancelSubscription } from '@/app/utils/subscription-api';
import { useLanguage } from '@/app/providers/language-provider';

interface SubscriptionManagerProps {
  usage: UsageStats;
  onSubscriptionUpdated?: () => void;
}

export default function SubscriptionManager({ 
  usage, 
  onSubscriptionUpdated 
}: SubscriptionManagerProps) {
  const { getToken } = useAuth();
  const { theme } = useTheme();
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle subscription cancellation
  const handleCancel = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const result = await cancelSubscription(getToken);
      
      setSuccess(result.message || t('subscription_management.subscription_canceled'));
      if (onSubscriptionUpdated) onSubscriptionUpdated();
    } catch (error) {
      console.error('Cancellation error:', error);
      setError(error instanceof Error ? error.message : t('subscription_management.cancel_error'));
    } finally {
      setLoading(false);
    }
  };

  // Format a date string
  const formatDate = (date: string | null) => {
    if (!date) return 'N/A';
    const parsedDate = new Date(date);
    return parsedDate.toLocaleDateString();
  };

  const isActive = usage.status === 'active' || usage.status === 'trialing';
  const hasPremium = usage.subscription_type !== 'none';

  return (
    <div className="w-full">
      {error && (
        <div className={`mb-4 p-3 ${theme === 'dark' ? 'bg-red-900/20 text-red-400' : 'bg-red-50 text-red-600'} rounded-md flex items-center`}>
          <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
      
      {success && (
        <div className={`mb-4 p-3 ${theme === 'dark' ? 'bg-green-900/20 text-green-400' : 'bg-green-50 text-green-600'} rounded-md flex items-center`}>
          <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0" />
          <span>{success}</span>
        </div>
      )}
      
      <div className="space-y-4">
        {/* Current Subscription Info */}
        <div className={`p-5 ${theme === 'dark' ? 'bg-[hsl(0_0%_10%)]' : 'bg-gray-50'} rounded-md`}>
          <h3 className={`font-semibold text-lg mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            {t('subscription_management.current_subscription')}
          </h3>
          
          <div className="space-y-3">
            <div className="flex flex-col sm:flex-row sm:items-center">
              <span className={`font-medium min-w-[180px] ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('subscription_management.plan')}
              </span>
              <span className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} font-medium`}>
                {formatSubscriptionType(usage.subscription_type)}
              </span>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:items-center">
              <span className={`font-medium min-w-[180px] ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('subscription_management.status')}
              </span>
              <span className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} font-medium`}>
                {formatSubscriptionStatus(usage.status)}
              </span>
            </div>
            
            {isActive && (
              <div className="flex flex-col sm:flex-row sm:items-center">
                <span className={`font-medium min-w-[180px] ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {usage.will_cancel ? t('subscription_management.subscription_ends') : t('subscription_management.current_period_ends')}
                </span>
                <span className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} font-medium`}>
                  {formatDate(usage.current_period_end)}
                </span>
              </div>
            )}
          </div>
          
          {usage.will_cancel && (
            <div className={`mt-4 p-3 ${theme === 'dark' ? 'bg-yellow-900/20 border border-yellow-700/30 text-yellow-400' : 'bg-yellow-50 border border-yellow-100 text-yellow-700'} rounded-md text-sm`}>
              {t('subscription_management.cancel_notice')}
            </div>
          )}
        </div>
        
        {/* Subscription Actions */}
        <div className="flex flex-col sm:flex-row gap-3 mt-6">
          {isActive && hasPremium && (
            <button
              onClick={handleCancel}
              disabled={loading}
              className={`flex-1 py-2 px-4 rounded-md ${
                theme === 'dark' 
                  ? 'bg-gray-700 text-white hover:bg-gray-600' 
                  : 'bg-gray-600 text-white hover:bg-gray-700'
              } disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed flex items-center justify-center transition-colors`}
            >
              {loading ? <Loader2 className="w-5 h-5 mr-2 animate-spin" /> : null}
              {t('subscription_management.cancel_subscription')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}