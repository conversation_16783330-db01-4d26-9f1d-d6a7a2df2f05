package ai_tests

import (
	"context"
	"decodemed/ai/youtube"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/joho/godotenv"
)

func TestYouTubeVideoUnits(t *testing.T) {
	// Load environment variables from .env file
	envPath := "../../.env"
	err := godotenv.Load(envPath)
	if err != nil {
		t.Logf("Warning: Error loading .env file from %s: %v", envPath, err)
		// Try loading from current directory as fallback
		err = godotenv.Load()
		if err != nil {
			t.Logf("Warning: Error loading .env file from current directory: %v", err)
		}
	}

	// Check for required API credentials
	projectID := os.Getenv("VERTEX_PROJECT_ID")
	location := os.Getenv("VERTEX_LOCATION")
	credsFile := os.Getenv("VERTEX_CREDENTIALS")

	if projectID == "" || location == "" || credsFile == "" {
		t.Skip("VERTEX_PROJECT_ID, VERTEX_LOCATION, or VERTEX_CREDENTIALS not set, skipping test")
	}

	// Create a YouTube summarizer
	summarizer, err := youtube.NewYouTubeSummarizer()
	if err != nil {
		t.Fatalf("Failed to create YouTube summarizer: %v", err)
	}
	defer summarizer.Close()

	// Test video URL and title
	videoURL := "https://www.youtube.com/watch?v=SmZmBKc7Lrs&t" // "Backpropagation explained"
	videoTitle := "Backpropagation Explained"

	// Set test options - use DefaultSummaryOptions() values for consistency
	options := youtube.DefaultSummaryOptions()
	options.UnitCount = 10 // Generate 10 units for better coverage
	// Using default token limit from DefaultSummaryOptions()

	// Generate structured units from YouTube video - with extended timeout for longer processing
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// Generate the structured content with units
	units, fullContent, err := summarizer.GenerateStructuredUnits(ctx, videoURL, videoTitle, options)

	if err != nil {
		if strings.Contains(err.Error(), "permission denied") {
			t.Fatalf("Permission denied error: %v\n\nPlease check your GCP credentials and permissions", err)
		} else {
			t.Fatalf("Failed to generate structured content: %v", err)
		}
	}

	// Print only the generated text as requested
	t.Log(fullContent)

	// Log some basic statistics about the generated units to verify coverage
	t.Logf("Generated %d units in total", len(units))

	// Check first and last unit to verify video coverage
	if len(units) > 1 {
		t.Logf("First unit: %s", units[0].Title)
		t.Logf("Last unit: %s", units[len(units)-1].Title)
	}
}
