import requests
import json

BASE_URL = 'http://localhost:8000/api'
TEST_USER_ID = 'test_user_123'

def check_subscription_status():
    response = requests.get(f'{BASE_URL}/subscriptions/status', params={'userId': TEST_USER_ID})
    print('Subscription Status:', json.dumps(response.json(), indent=2))

def make_test_request(endpoint):
    headers = {'X-User-Id': TEST_USER_ID}
    response = requests.post(f'{BASE_URL}/{endpoint}', headers=headers)
    print(f'{endpoint} Response:', response.status_code)
    return response

def test_usage_limits():
    # Test search limit
    print("\nTesting Search Limit:")
    for i in range(4):
        print(f"Search request {i+1}:")
        make_test_request('search/query')
        check_subscription_status()

    # Test chat limit
    print("\nTesting Chat Limit:")
    for i in range(4):
        print(f"Chat request {i+1}:")
        make_test_request('chat/message')
        check_subscription_status()

if __name__ == '__main__':
    test_usage_limits()
