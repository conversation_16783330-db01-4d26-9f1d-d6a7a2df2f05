package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"decodemed/models"
	"decodemed/storage"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// ------------ GetProjects returns all projects for a user ------------
func GetProjects(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		medspaceID := c.Query("med_space_id")

		var projects []models.Project
		query := db.Where("user_id = ?", userID)

		// If medspaceID is provided, filter projects by MedSpace
		if medspaceID != "" {
			query = query.Where("med_space_id = ?", medspaceID)
		} else {
			// If no MedSpace is specified, show projects that aren't in any MedSpace (backward compatibility)
			// or fetch all projects if show_all=true is provided
			if showAll := c.Query("show_all"); showAll != "true" {
				query = query.Where("med_space_id IS NULL")
			}
		}

		if err := query.Find(&projects).Error; err != nil {
			log.Printf("Error fetching projects: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch projects"})
			return
		}

		// Always return an array, even if empty
		c.JSON(http.StatusOK, gin.H{"projects": projects})
	}
}

// ---------------- CreateProject creates a new project ----------------
func CreateProject(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("CreateProject handler called: %s %s", c.Request.Method, c.Request.URL.Path)

		// Get file from form
		file, err := c.FormFile("file")
		if err != nil {
			log.Printf("No file uploaded or error getting file: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "File is required"})
			return
		}

		// Check file size (limit to 50MB)
		if file.Size > 50*1024*1024 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File size too large"})
			return
		}

		// Check file type
		fileExt := filepath.Ext(file.Filename)
		allowedExts := map[string]bool{
			".pdf":  true,
			".docx": true,
			".txt":  true,
		}

		if !allowedExts[fileExt] {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file type. Only PDF, DOCX, and TXT are supported"})
			return
		}

		// Extract project name from the filename by removing extension
		fileName := file.Filename
		name := fileName[:len(fileName)-len(fileExt)]

		userID, exists := c.Get("user_id")
		if !exists {
			log.Printf("Error: user_id not found in context")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "User ID not found"})
			return
		}

		// Convert userID to uint safely
		var userIDUint uint
		switch v := userID.(type) {
		case uint:
			userIDUint = v
		case int:
			userIDUint = uint(v)
		case string:
			id, err := strconv.ParseUint(v, 10, 32)
			if err != nil {
				log.Printf("Error converting user ID string to uint: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
				return
			}
			userIDUint = uint(id)
		default:
			log.Printf("Error: user_id is of unexpected type: %T", userID)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID type"})
			return
		}

		// Check if the user exists
		var user models.User
		result := db.First(&user, userIDUint)
		if result.Error != nil {
			log.Printf("User with ID %d not found, creating debug user", userIDUint)
			user = models.User{
				Model: gorm.Model{
					ID: userIDUint,
				},
				ClerkID: "debug_user_" + strconv.FormatUint(uint64(userIDUint), 10),
				Email:   "debug_" + strconv.FormatUint(uint64(userIDUint), 10) + "@example.com",
			}
			if err := db.Create(&user).Error; err != nil {
				log.Printf("Error creating debug user: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create debug user"})
				return
			}
			log.Printf("Debug user created with ID: %d", user.ID)
		}

		// Get user's subscription status
		subscriptionStatus, err := models.GetSubscriptionStatusForUser(db, userIDUint)
		if err != nil {
			log.Printf("Error getting subscription status for user %d: %v", userIDUint, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check subscription status"})
			return
		}
		isPremium := subscriptionStatus == "active" || subscriptionStatus == "trialing"

		// For non-premium users, check total free projects limit first
		if !isPremium {
			if user.FreeProjectsCount >= models.FreeUserTotalProjectLimit {
				log.Printf("User %d has reached total free project limit: %d/%d", userIDUint, user.FreeProjectsCount, models.FreeUserTotalProjectLimit)
				c.JSON(http.StatusPaymentRequired, gin.H{
					"error":           fmt.Sprintf("You have reached your limit of %d total free projects. Please subscribe for unlimited projects.", models.FreeUserTotalProjectLimit),
					"limit":           models.FreeUserTotalProjectLimit,
					"used":            user.FreeProjectsCount,
					"is_premium":      false,
					"require_upgrade": true,
				})
				return
			}
		}

		// Check if user can create more projects today based on subscription limits (daily check)
		canCreate, err := models.CanCreateProject(db, userIDUint)
		if err != nil {
			log.Printf("Error checking project creation limit: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check usage limits"})
			return
		}

		if !canCreate {
			// Get user's daily limit and calculate remaining projects
			limit, _ := models.GetUserDailyProjectLimit(db, userIDUint)
			usage, _ := models.GetTodayUsage(db, userIDUint)

			errorMsg := "Daily project creation limit reached"
			if isPremium {
				errorMsg = "You've reached your daily limit of " + strconv.Itoa(limit) + " projects. Please try again tomorrow."
			} else {
				errorMsg = "You've reached your free tier limit of " + strconv.Itoa(limit) + " projects. Subscribe to create unlimited projects."
			}

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":           errorMsg,
				"limit":           limit,
				"used":            usage.ProjectsCreatedToday,
				"is_premium":      isPremium,
				"require_upgrade": !isPremium,
			})
			return
		}

		// Initialize storage client
		ctx := context.Background()
		storageClient, err := storage.GetStorage(ctx)
		if err != nil {
			log.Printf("Failed to initialize storage client: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize storage client"})
			return
		}
		defer storageClient.Close()

		// Check if this project should be associated with a MedSpace
		var medSpaceID *uint
		medSpaceIDStr := c.Request.FormValue("med_space_id")
		if medSpaceIDStr != "" {
			// Validate that the MedSpace exists and belongs to this user
			msID, err := strconv.ParseUint(medSpaceIDStr, 10, 32)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid MedSpace ID format"})
				return
			}

			msIDUint := uint(msID)
			var medSpace models.MedSpace
			if err := db.Where("id = ? AND user_id = ?", msIDUint, userIDUint).First(&medSpace).Error; err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "MedSpace not found or does not belong to user"})
				return
			}

			medSpaceID = &msIDUint
		}

		// Read num_units from form data
		numUnitsStr := c.Request.FormValue("num_units")
		numUnits := 6 // Default value
		if numUnitsStr != "" {
			parsedNum, err := strconv.Atoi(numUnitsStr)
			if err == nil && parsedNum >= 1 && parsedNum <= 200 {
				numUnits = parsedNum
			} else {
				log.Printf("Invalid num_units value received: %s. Using default.", numUnitsStr)
				// Optionally, you could return an error here if num_units is mandatory or must be valid
				// c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid num_units value. Must be between 1 and 200."} )
				// return
			}
		}

		// Create project first to get the ID
		project := models.Project{
			Name:       name,
			UserID:     userIDUint,
			MedSpaceID: medSpaceID,
			NumUnits:   numUnits, // Save the parsed or default numUnits
		}

		// Start database transaction to create project and update user counts atomically
		err = db.Transaction(func(tx *gorm.DB) error {
			// Create the project
			if err := tx.Create(&project).Error; err != nil {
				return err
			}

			// Increment the user's project usage for today
			if err := models.IncrementProjectUsage(tx, userIDUint); err != nil {
				return err
			}

			// If user doesn't have an active subscription, also increment their total free projects count
			if !isPremium {
				var userInTx models.User
				if err := tx.First(&userInTx, userIDUint).Error; err != nil {
					log.Printf("Error fetching user %d in transaction: %v", userIDUint, err)
					return err
				}

				if err := tx.Model(&userInTx).Update("free_projects_count", userInTx.FreeProjectsCount+1).Error; err != nil {
					return err
				}
				log.Printf("Incremented free projects count for user %d to %d", userInTx.ID, userInTx.FreeProjectsCount+1)
			}

			return nil
		})

		if err != nil {
			log.Printf("Error creating project in database: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project"})
			return
		}

		// Upload file to storage
		projectFolder := fmt.Sprintf("projects/%d", project.ID)
		fileURL, err := storageClient.UploadFile(ctx, file, projectFolder)
		if err != nil {
			log.Printf("Failed to upload file: %v", err)
			// Delete the project if file upload fails
			db.Delete(&project)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
			return
		}

		// Update project with file URL
		project.FileURL = fileURL
		if err := db.Save(&project).Error; err != nil {
			log.Printf("Error updating project with file URL: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project with file URL"})
			return
		}

		// Generate thumbnail asynchronously
		objectName := storage.ExtractObjectNameFromURL(fileURL)
		if objectName != "" {
			go func(projectID uint, objName string) {
				// Create a new context for the background operation
				bgCtx := context.Background()

				// Get a new storage client for the background operation
				bgStorageClient, err := storage.GetStorage(bgCtx)
				if err != nil {
					log.Printf("Failed to initialize storage client for thumbnail generation: %v", err)
					return
				}
				defer bgStorageClient.Close()

				// Generate thumbnail
				thumbnailURL, err := bgStorageClient.GetThumbnailURL(bgCtx, objName)
				if err != nil {
					log.Printf("Failed to generate thumbnail for project %d: %v", projectID, err)
					return
				}

				// Open a new DB connection for the background operation
				// We need to use a new connection since the original one might be closed
				db, err := gorm.Open(postgres.Open(os.Getenv("DATABASE_URL")), &gorm.Config{})
				if err != nil {
					log.Printf("Failed to connect to database for thumbnail update: %v", err)
					return
				}

				// Get the DB connection from the GORM DB instance
				sqlDB, err := db.DB()
				if err != nil {
					log.Printf("Failed to get DB connection: %v", err)
					return
				}
				defer sqlDB.Close()

				// Update the project with the thumbnail URL
				if err := db.Model(&models.Project{}).Where("id = ?", projectID).Update("thumbnail_url", thumbnailURL).Error; err != nil {
					log.Printf("Failed to update project %d with thumbnail URL: %v", projectID, err)
					return
				}

				log.Printf("Successfully generated and stored thumbnail URL for project %d", projectID)
			}(project.ID, objectName)
		}

		// Start a background goroutine to check for placeholder thumbnails and update them
		go func() {
			// Wait a bit to allow the thumbnail to be generated
			time.Sleep(30 * time.Second)

			// Check if the thumbnail URL is still a placeholder
			var newProject models.Project
			if err := db.First(&newProject, project.ID).Error; err != nil {
				log.Printf("Failed to fetch project %d for thumbnail check: %v", project.ID, err)
				return
			}

			// If the thumbnail URL contains "processing_placeholder", try to get the real thumbnail
			if strings.Contains(newProject.ThumbnailURL, "processing_placeholder") {
				// Get the storage client
				storageClient, err := storage.GetStorage(context.Background())
				if err != nil {
					log.Printf("Failed to get storage client for thumbnail update: %v", err)
					return
				}

				// Try to get the real thumbnail
				thumbnailURL, err := storageClient.GetThumbnailURL(context.Background(), objectName)
				if err != nil {
					log.Printf("Failed to get thumbnail URL for project %d: %v", project.ID, err)
					return
				}

				// If the new URL is not a placeholder, update the project
				if !strings.Contains(thumbnailURL, "processing_placeholder") {
					if err := db.Model(&models.Project{}).Where("id = ?", project.ID).Update("thumbnail_url", thumbnailURL).Error; err != nil {
						log.Printf("Failed to update project %d with real thumbnail URL: %v", project.ID, err)
					} else {
						log.Printf("Successfully updated project %d with real thumbnail URL", project.ID)
					}
				}
			}
		}()

		// Get remaining projects for today to return to client
		remaining, _ := models.GetRemainingDailyProjects(db, userIDUint)

		log.Printf("Project created successfully: ID=%d", project.ID)
		c.JSON(http.StatusCreated, gin.H{
			"project": project,
			"daily_usage": gin.H{
				"projects_created_today": project.ID, // Just using project ID as a proxy, it's actually from usage model
				"remaining_today":        remaining,
				"daily_limit":            models.FreeUserDailyProjectLimit, // This will be different for premium
			},
		})
	}
}

// ------------ GetProject returns a specific project ------------
func GetProject(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Param("id")
		userID, _ := c.Get("user_id")

		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", projectID, userID).
			Preload("Units").
			Preload("Units.Mindmap").
			First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"project": project})
	}
}

// ------------ UpdateProject updates a project ------------
func UpdateProject(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Param("id")
		userID, _ := c.Get("user_id")

		var req struct {
			Name       string `json:"name"`
			MedSpaceID *uint  `json:"med_space_id"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Update fields
		if req.Name != "" {
			project.Name = req.Name
		}

		// If MedSpaceID is provided, validate that the MedSpace exists and belongs to this user
		if req.MedSpaceID != nil {
			if *req.MedSpaceID > 0 {
				var medSpace models.MedSpace
				if err := db.Where("id = ? AND user_id = ?", *req.MedSpaceID, userID).First(&medSpace).Error; err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": "MedSpace not found or does not belong to user"})
					return
				}
				project.MedSpaceID = req.MedSpaceID
			} else {
				// If MedSpaceID is 0, remove the project from any MedSpace
				project.MedSpaceID = nil
			}
		}

		if err := db.Save(&project).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"project": project})
	}
}

// ------------ DeleteProject deletes a project ------------
func DeleteProject(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Param("id")
		userID, _ := c.Get("user_id")

		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		log.Printf("Starting deletion of project ID %d", project.ID)

		// Start a transaction to ensure all deletes succeed or fail together
		err := db.Transaction(func(tx *gorm.DB) error {
			// 1. First get all units associated with this project
			var unitIDs []uint
			if err := tx.Model(&models.Unit{}).Where("project_id = ?", project.ID).Pluck("id", &unitIDs).Error; err != nil {
				log.Printf("Error finding unit IDs for project %d: %v", project.ID, err)
				return err
			}

			log.Printf("Found %d units to delete for project %d: %v", len(unitIDs), project.ID, unitIDs)

			if len(unitIDs) > 0 {
				// Get all flashcard IDs before deleting them (for debugging)
				var flashcardIDs []uint
				if err := tx.Model(&models.Flashcard{}).Where("unit_id IN ?", unitIDs).Pluck("id", &flashcardIDs).Error; err != nil {
					log.Printf("Error finding flashcard IDs: %v", err)
				} else {
					log.Printf("Found flashcard IDs to delete: %v", flashcardIDs)
				}

				// Delete flashcard reviews first
				reviewResult := tx.Exec("DELETE FROM flashcard_reviews WHERE flashcard_id IN (SELECT id FROM flashcards WHERE unit_id IN ?)", unitIDs)
				if reviewResult.Error != nil {
					log.Printf("Error deleting flashcard reviews: %v", reviewResult.Error)
					return reviewResult.Error
				}
				log.Printf("Deleted %d flashcard reviews", reviewResult.RowsAffected)

				// Delete flashcards
				cardResult := tx.Exec("DELETE FROM flashcards WHERE unit_id IN ?", unitIDs)
				if cardResult.Error != nil {
					log.Printf("Error deleting flashcards: %v", cardResult.Error)
					return cardResult.Error
				}
				log.Printf("Deleted %d flashcards", cardResult.RowsAffected)

				// Also use direct model deletion as a backup
				if err := tx.Where("unit_id IN ?", unitIDs).Delete(&models.Flashcard{}).Error; err != nil {
					log.Printf("Warning: Backup flashcard deletion had error: %v", err)
				}

				// Delete mindmaps and quizzes
				if err := tx.Where("unit_id IN ?", unitIDs).Delete(&models.Mindmap{}).Error; err != nil {
					log.Printf("Error deleting mindmaps: %v", err)
					return err
				}

				if err := tx.Where("unit_id IN ?", unitIDs).Delete(&models.Quiz{}).Error; err != nil {
					log.Printf("Error deleting quizzes: %v", err)
					return err
				}
			}

			// Delete all units for the project
			if err := tx.Where("project_id = ?", project.ID).Delete(&models.Unit{}).Error; err != nil {
				log.Printf("Error deleting units for project %d: %v", project.ID, err)
				return err
			}

			// Delete the project itself
			if err := tx.Delete(&project).Error; err != nil {
				log.Printf("Error deleting project %d: %v", project.ID, err)
				return err
			}

			log.Printf("Successfully deleted project %d and all related data in transaction", project.ID)
			return nil
		})

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete project: " + err.Error()})
			return
		}

		// If the project has a file URL, delete the file from storage
		if project.FileURL != "" {
			// Initialize the storage client
			storageClient, err := storage.GetStorage(context.Background())
			if err != nil {
				// Log the error but don't fail the request since the DB records are already deleted
				log.Printf("Failed to initialize storage client: %v", err)
			} else {
				defer storageClient.Close()

				// Extract the object name from the file URL
				objectName := storage.ExtractObjectNameFromURL(project.FileURL)
				if objectName != "" {
					// Delete the file from Google Cloud Storage
					if err := storageClient.DeleteFile(context.Background(), objectName); err != nil {
						// Log the error but don't fail the request
						log.Printf("Failed to delete file %s from storage: %v", objectName, err)
					} else {
						log.Printf("Successfully deleted file %s from storage", objectName)
					}

					// Also delete the thumbnail file if it exists
					thumbnailObjectName := "thumbnails/" + objectName
					if err := storageClient.DeleteFile(context.Background(), thumbnailObjectName); err != nil {
						// Log the error but don't fail the request
						log.Printf("Failed to delete thumbnail %s from storage: %v", thumbnailObjectName, err)
					} else {
						log.Printf("Successfully deleted thumbnail %s from storage", thumbnailObjectName)
					}
				}
			}
		}

		c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
	}
}
