import React, { useState } from 'react';
import { Calendar, Quote, Clipboard, Filter, Plus, Minus } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';

interface FilterTabProps {
  isFilterVisible: boolean;
  toggleFilterVisibility: () => void;
  startYear: string;
  setStartYear: (value: string) => void;
  endYear: string;
  setEndYear: (value: string) => void;
  minCitations: string;
  setMinCitations: (value: string) => void;
  maxCitations: string;
  setMaxCitations: (value: string) => void;
  publicationTypes: string[];
  setPublicationTypes: React.Dispatch<React.SetStateAction<string[]>>;
  handleApplyFilters: () => void;
  isFilterApplied: boolean;
  filterChanged: boolean;
  setFilterChanged: (value: boolean) => void;
  setIsFilterApplied: (value: boolean) => void;
}

const FilterTab: React.FC<FilterTabProps> = ({

  startYear,
  setStartYear,
  endYear,
  setEndYear,
  minCitations,
  setMinCitations,
  maxCitations,
  setMaxCitations,
  publicationTypes,
  setPublicationTypes,
  handleApplyFilters,
  isFilterApplied,
  filterChanged,
  setFilterChanged,
  setIsFilterApplied
}) => {
  const { theme } = useTheme();
  const [isDateRangeExpanded, setIsDateRangeExpanded] = useState<boolean>(true);
  const [isCitationRangeExpanded, setIsCitationRangeExpanded] = useState<boolean>(true);
  const [isPublicationTypeExpanded, setIsPublicationTypeExpanded] = useState<boolean>(true);

  const togglePublicationType = (type: string) => {
    setPublicationTypes(prev => 
      prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]
    );
    setFilterChanged(true);
    setIsFilterApplied(false);
  };

  // Desktop/Tablet Filter Panel
  const DesktopFilterPanel = () => (
    <div className={`flex flex-col z-10 ${
      theme === 'dark' 
        ? 'bg-neutral-900 shadow-md border-l border-neutral-800 text-neutral-200' 
        : 'bg-white shadow-md border-l border-gray-200 text-gray-900'
    } h-full w-64`}>
      <div className={`p-4 ${
        theme === 'dark' ? 'border-b border-neutral-800' : 'border-b border-gray-200'
      }`}>
        <h2 className={`text-lg font-medium flex items-center ${
          theme === 'dark' ? 'text-neutral-200' : 'text-gray-900'
        }`}>
          <Filter size={20} className="mr-2" />
          Filters
        </h2>
      </div>
      <div className={`p-4 ${theme === 'dark' ? 'bg-neutral-900' : 'bg-white'} overflow-y-auto flex-1`}>
        {/* PUBLICATION DATE RANGE FILTER */}
        <div className="mt-4">
          <button
            onClick={() => setIsDateRangeExpanded(!isDateRangeExpanded)}
            className={`flex items-center justify-between w-full text-left text-base font-medium ${
              theme === 'dark' ? 'text-neutral-200' : 'text-gray-900'
            }`}
          >
            <span className="flex items-center">
              <Calendar size={16} className="mr-2" /> Date range
            </span>
            {isDateRangeExpanded ? <Minus size={16} /> : <Plus size={16} />}
          </button>
          {isDateRangeExpanded && (
            <div className="flex flex-col pl-4 mt-2 space-y-2">
              <input
                type="number"
                placeholder="Start Year"
                value={startYear}
                onChange={(e) => setStartYear(e.target.value)}
                onBlur={(e) => {
                  const value = Math.max(1980, Math.min(2024, parseInt(e.target.value) || 1980));
                  setStartYear(value.toString());
                }}
                min="1980"
                max="2024"
                className={`px-2 py-1 text-sm rounded-xl ${
                  theme === 'dark' 
                    ? 'bg-neutral-800 border-neutral-700 text-white' 
                    : 'bg-white border border-gray-300 text-gray-900'
                }`}
              />
              <input
                type="number"
                placeholder="End Year"
                value={endYear}
                onChange={(e) => setEndYear(e.target.value)}
                onBlur={(e) => {
                  const value = Math.max(1980, Math.min(2024, parseInt(e.target.value) || 2024));
                  setEndYear(value.toString());
                }}
                min="1980"
                max="2024"
                className={`px-2 py-1 text-sm rounded-xl ${
                  theme === 'dark' 
                    ? 'bg-neutral-800 border-neutral-700 text-white' 
                    : 'bg-white border border-gray-300 text-gray-900'
                }`}
              />
            </div>
          )}
        </div>

        {/* CITATION FILTER */}
        <div className="mt-4">
          <button
            onClick={() => setIsCitationRangeExpanded(!isCitationRangeExpanded)}
            className={`flex items-center justify-between w-full text-left text-base font-medium ${
              theme === 'dark' ? 'text-neutral-200' : 'text-gray-900'
            }`}
          >
            <span className="flex items-center">
              <Quote size={16} className="mr-2" /> Citation range
            </span>
            {isCitationRangeExpanded ? <Minus size={16} /> : <Plus size={16} />}
          </button>
          {isCitationRangeExpanded && (
            <div className="flex flex-col pl-4 mt-2 space-y-2">
              <input
                type="number"
                placeholder="Min Citations"
                value={minCitations}
                onChange={(e) => {
                  const value = Math.max(0, parseInt(e.target.value) || 0);
                  setMinCitations(value.toString());
                  setFilterChanged(true);
                  setIsFilterApplied(false);
                }}
                min="0"
                className={`px-2 py-1 text-sm rounded-xl ${
                  theme === 'dark' 
                    ? 'bg-neutral-800 border-neutral-700 text-white' 
                    : 'bg-white border border-gray-300 text-gray-900'
                }`}
              />
              <input
                type="number"
                placeholder="Max Citations"
                value={maxCitations}
                onChange={(e) => {
                  const value = Math.max(0, parseInt(e.target.value) || 0);
                  setMaxCitations(value.toString());
                  setFilterChanged(true);
                  setIsFilterApplied(false);
                }}
                min="0"
                className={`px-2 py-1 text-sm rounded-xl ${
                  theme === 'dark' 
                    ? 'bg-neutral-800 border-neutral-700 text-white' 
                    : 'bg-white border border-gray-300 text-gray-900'
                }`}
              />
            </div>
          )}
        </div>
        
        {/* PUBLICATION TYPE FILTER */}
        <div className="mt-4">
          <button
            onClick={() => setIsPublicationTypeExpanded(!isPublicationTypeExpanded)}
            className={`flex items-center justify-between w-full text-left text-base font-medium ${
              theme === 'dark' ? 'text-neutral-200' : 'text-gray-900'
            }`}
          >
            <span className="flex items-center">
              <Clipboard size={16} className="mr-1" /> Publication Type
            </span>
            {isPublicationTypeExpanded ? <Minus size={16} /> : <Plus size={16} />}
          </button>
          {isPublicationTypeExpanded && (
            <div className="flex flex-col pl-1 text-start mt-2 space-y-2">
              {[
                'Systematic Review',
                'Meta-Analysis',
                'Randomized Controlled Trial',
                'Case-Control Study',
                'Cohort Study',
                'Cross-sectional Study',
                'Case Series',
                'Case Report',
                'Narrative Review',
                'Expert Opinion',
                'Editorial Opinion',
                'In vivo study',
                'In vitro study'
              ].map((type) => (
                <button
                  key={type}
                  onClick={() => togglePublicationType(type)}
                  className={`px-2 py-1 text-sm rounded flex items-center ${
                    publicationTypes.includes(type)
                      ? 'bg-neutral-700 text-white font-semibold rounded-xl'
                      : theme === 'dark'
                        ? 'bg-neutral-800 font-semibold rounded-xl text-neutral-200 hover:bg-neutral-700'
                        : 'bg-white font-semibold rounded-xl text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
          )}
        </div>
        
        {/* APPLY FILTERS BUTTON */}
        <button
          onClick={handleApplyFilters}
          className={`mt-4 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 flex items-center justify-center ${
            isFilterApplied && !filterChanged
              ? 'bg-neutral-700 text-white font-semibold rounded-xl'
              : theme === 'dark'
                ? 'bg-neutral-800 text-neutral-200 hover:bg-neutral-700'
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
          }`}
        >
          <Filter size={16} className="mr-2" />
          {isFilterApplied && !filterChanged ? 'Filter Applied' : 'Apply Filter'}
        </button>
      </div>
    </div>
  );

  return <DesktopFilterPanel />;
};

export default FilterTab;
