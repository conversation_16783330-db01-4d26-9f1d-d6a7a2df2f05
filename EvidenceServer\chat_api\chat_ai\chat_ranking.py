import os
from openai import <PERSON>ync<PERSON><PERSON>A<PERSON>
from dotenv import load_dotenv
import numpy as np
import faiss 
from .chat_query import chat_query
from .chat_data import fetch_pubmed_articles, parse_articles
load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

if not api_key:
    raise ValueError("The OpenAI API key must be set in the environment variables.")

async def get_embedding(text):
    # Validate and format input
    if isinstance(text, list):
        # Ensure all items in list are strings and not empty
        text = [str(item).strip() for item in text if item and str(item).strip()]
        if not text:
            raise ValueError("No valid text items in list")
    else:
        # If single item, ensure it's a non-empty string
        text = str(text).strip()
        if not text:
            raise ValueError("Empty text input")

    try:
        response = await client.embeddings.create(
            model="text-embedding-3-small",  # Changed model
            input=text
        )
        return [embedding.embedding for embedding in response.data]
    except Exception as e:
        print(f"Embedding API error: {str(e)}")
        raise

async def rank_articles(question, chat_context):
    # Get search query
    search_query = await chat_query(question, chat_context)
  

    # Fetch articles
    fetch_articles, error = await fetch_pubmed_articles(search_query)
    if error:
        print(f"Error fetching articles: {error}")
        return None
    
    # Articles data
    articles_data = await parse_articles(fetch_articles)
    if not articles_data:
        return None

    # Convert search query and articles to embeddings
    texts = [search_query]
    for article in articles_data:
        title = article.get('title', '')
        abstract = article.get('abstract', '')
        if title is None:
            title = ''
        if abstract is None:
            abstract = ''
        texts.append(f"{title} {abstract}".strip())

    
    embeddings = await get_embedding(texts)
    
    # Convert embeddings to numpy array
    query_embedding = np.array(embeddings[0]).reshape(1, -1)
    article_embeddings = np.array(embeddings[1:])
    
    # Use FAISS to rank articles
    index = faiss.IndexFlatL2(query_embedding.shape[1])
    index.add(article_embeddings)
    distances, indices = index.search(query_embedding, len(articles_data))
    
    articles = [articles_data[i] for i in indices[0]]
    
    return articles
    
 
