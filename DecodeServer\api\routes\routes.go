package routes

import (
	handlers "decodemed/api/handlers/decode"
	notes_api "decodemed/api/handlers/medical_notes/notes_api"
	notes_upload "decodemed/api/handlers/medical_notes/upload"
	"decodemed/api/handlers/subscriptions"
	"decodemed/api/middleware"
	"decodemed/models"
	"log"
	"net/http"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all the routes for the application
func SetupRoutes(router *gin.Engine, db *gorm.DB) {
	// Initialize services first, before any route registration

	// Initialize Stripe
	subscriptions.InitStripe()
	log.Println("Stripe initialized")

	// Initialize AutoGenService for automatic quiz generation
	handlers.InitAutoGenService(db)
	log.Println("AutoGenService initialized for automatic content generation")

	// Initialize auto generation service
	handlers.InitYouTubeAutoGenService(db)
	log.Println("YouTubeAutoGenService initialized for YouTube-specific content generation")

	// Add DB to the context
	router.Use(func(c *gin.Context) {
		c.Set("db", db)
		c.Next()
	})

	// Serve static files from the uploads directory
	router.Static("/uploads", "./uploads")

	// Add a simple test endpoint
	router.GET("/test", func(c *gin.Context) {
		log.Println("Test endpoint called")
		c.JSON(http.StatusOK, gin.H{
			"message": "Server is working correctly",
			"time":    time.Now().String(),
		})
	})

	// Register YouTube handlers
	// handlers.RegisterYouTubeHandlers(router, db) - Removed as these will be moved to protected routes

	// Add a simple project creation endpoint that doesn't require authentication
	router.POST("/test/create-project", func(c *gin.Context) {
		log.Println("Test create project endpoint called")

		var req struct {
			Name     string `json:"name"`
			FileName string `json:"file_name" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// If name is not provided, use filename without extension
		projectName := req.Name
		if projectName == "" {
			fileExt := filepath.Ext(req.FileName)
			projectName = req.FileName[:len(req.FileName)-len(fileExt)]
		}

		log.Printf("Creating test project: %s", projectName)

		project := models.Project{
			Name:   projectName,
			UserID: 1, // Use a fixed user ID for testing
		}

		if err := db.Create(&project).Error; err != nil {
			log.Printf("Error creating project: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project: " + err.Error()})
			return
		}

		log.Printf("Test project created successfully with ID: %d", project.ID)
		c.JSON(http.StatusCreated, gin.H{"project": project})
	})

	// WebSocket endpoint for real-time updates
	router.GET("/ws", handlers.HandleWebSocket)
	router.GET("/ws/quiz", handlers.QuizWebSocketHandler)

	// Secure API routes with auth middleware
	apiRouter := router.Group("/api")

	// API routes
	{
		// -----------------Auth routes-----------------
		auth := apiRouter.Group("/auth")
		{
			auth.POST("/register", handlers.RegisterHandler(db))
			auth.POST("/login", handlers.LoginHandler(db))
		}

		// -----------------Medical Notes routes-----------------
		medicalNotes := apiRouter.Group("/medical_notes")
		{
			medicalNotes.GET("", notes_api.GetNotes)
			medicalNotes.GET("/:id", notes_api.GetNote)
			medicalNotes.DELETE("/:id", notes_api.DeleteNote)
			medicalNotes.POST("/upload", func(c *gin.Context) {
				notes_upload.UploadMedicalNoteHandler(c)
			})
		}

		// -----------------Subscription API routes (accessible with authentication but doesn't require subscription)-----------------
		subscriptionApiRouter := apiRouter.Group("/subscription-api")
		subscriptionApiRouter.Use(handlers.AuthMiddleware())        // Auth middleware
		subscriptionApiRouter.Use(middleware.CheckSubscription(db)) // Add subscription status to context without requiring it
		{
			subscriptionApiRouter.GET("/plans", subscriptions.GetPlansHandler())
			subscriptionApiRouter.POST("/create-checkout-session", subscriptions.CreateCheckoutSessionHandler(db))
			subscriptionApiRouter.GET("/status", subscriptions.GetSubscriptionStatusHandler(db))
			subscriptionApiRouter.POST("/cancel", subscriptions.CancelSubscriptionHandler(db))
			subscriptionApiRouter.POST("/resume", subscriptions.ResumeSubscriptionHandler(db))
			subscriptionApiRouter.POST("/update", subscriptions.UpdateSubscriptionHandler(db))
			subscriptionApiRouter.POST("/create-portal-session", subscriptions.CreatePortalSessionHandler(db))
		}

		// Stripe webhook endpoint (unprotected as it's called by Stripe)
		apiRouter.POST("/webhooks/stripe", subscriptions.StripeWebhookHandler(db))

		//-----------------------DECODE ROUTES-----------------------
		// Protected routes - using auth middleware and requiring subscription for premium features
		protected := apiRouter.Group("/decode")
		// Add auth middleware
		protected.Use(handlers.AuthMiddleware())
		// Add subscription check middleware (doesn't block requests, just adds subscription status to context)
		protected.Use(middleware.CheckSubscription(db))
		{

			// MedSpaces - Basic feature
			protected.GET("/medspaces", handlers.GetMedSpaces(db))
			protected.POST("/medspaces", handlers.CreateMedSpace(db))
			protected.GET("/medspaces/:id", handlers.GetMedSpace(db))
			protected.PUT("/medspaces/:id", handlers.UpdateMedSpace(db))
			protected.DELETE("/medspaces/:id", handlers.DeleteMedSpace(db))
			protected.POST("/medspaces/:id/projects", handlers.AddProjectToMedSpace(db))
			protected.DELETE("/medspaces/:id/projects/:project_id", handlers.RemoveProjectFromMedSpace(db))

			// Projects - Basic feature
			protected.GET("/projects", handlers.GetProjects(db))
			protected.POST("/projects", handlers.CreateProject(db))
			protected.POST("/projects/create/", handlers.CreateProject(db))
			protected.GET("/projects/:id", handlers.GetProject(db))
			protected.PUT("/projects/:id", handlers.UpdateProject(db))
			protected.PUT("/projects/:id/update/", handlers.UpdateProject(db))
			protected.DELETE("/projects/:id", handlers.DeleteProject(db))
			protected.DELETE("/projects/:id/delete/", handlers.DeleteProject(db))

			// Premium features - require subscription
			premium := protected.Group("")
			premium.Use(middleware.SubscriptionRequired(db))
			{
				// Units with async processing - Premium feature
				premium.GET("/units", handlers.GetUnitsByProject(db))
				premium.GET("/units/all", handlers.GetAllUnits(db))
				premium.GET("/units/status/:jobId", handlers.GetGenerationStatus(db))
				premium.GET("/units/:id", handlers.GetUnit(db))
				premium.POST("/units/generate", handlers.HandleAsyncUnitGeneration(db))
				premium.POST("/units/generate/", handlers.HandleAsyncUnitGeneration(db))
				premium.DELETE("/units/:id", handlers.DeleteUnit(db))

				// Quiz endpoints with async processing - Premium feature
				premium.POST("/quiz/generate", handlers.HandleAsyncQuizGeneration(db))
				premium.POST("/quiz/all-quizzes", handlers.GenerateAllQuizzes(db))
				premium.GET("/quiz/:id", handlers.GetQuiz(db))
				premium.GET("/quiz/status/:jobId", handlers.GetQuizStatus(db))
				premium.POST("/quiz/progress/:id", handlers.UpdateQuizProgress(db))

				// Mindmap endpoints with async processing - Premium feature
				premium.GET("/mindmap/:projectId/:unitId", handlers.GetMindmap(db))
				premium.POST("/mindmap/generate", handlers.HandleAsyncMindmapGeneration(db))
				premium.GET("/mindmap/status/:jobId", handlers.GetMindmapStatus(db))
				premium.POST("/mindmap/generate-all", handlers.HandleAllMindmapsGeneration(db))

				// YouTube endpoints - Premium feature
				premium.GET("/youtube/info", handlers.FetchYouTubeInfo)
				premium.POST("/youtube/projects", handlers.CreateYouTubeProject(db))
				premium.POST("/youtube/generate-units", handlers.GenerateYouTubeUnits(db))

				// Flashcards endpoints - Premium feature
				flashcardHandler := handlers.NewFlashcardHandler(db)
				premium.POST("/flashcards/generate", flashcardHandler.GenerateFlashcards)
				premium.GET("/flashcards/unit/:unit_id", flashcardHandler.GetFlashcardsByUnit)
				premium.GET("/flashcards/project/:project_id", flashcardHandler.GetFlashcardsByProject)
				premium.GET("/flashcards/by-project", flashcardHandler.GetFlashcardsByProject)
				premium.GET("/flashcards/due", flashcardHandler.GetDueFlashcards)
				premium.POST("/flashcards/:id/review", flashcardHandler.ReviewFlashcard)
				premium.GET("/flashcards/stats", flashcardHandler.GetFlashcardStats)
				premium.GET("/flashcards/review-history/:id", flashcardHandler.GetFlashcardReviewHistory)
				premium.POST("/projects/:project_id/update-status", flashcardHandler.UpdateProjectFlashcardsStatus)

				// Auto-generation settings - Premium feature
				premium.POST("/autogen/settings", handlers.EnableAutoGeneration(db))
			}

			// File access features - Basic feature
			protected.POST("/fileview/signed-url", handlers.GetFileViewerURL(db))
			protected.GET("/fileview/file/:projectId", handlers.GetFileViewerURL(db))
			protected.GET("/fileview/proxy", handlers.ProxyFileContent(db))

			// Thumbnail endpoints - Basic feature
			protected.POST("/thumbnail", handlers.GetThumbnailURL(db))
			protected.GET("/thumbnail/:projectId", handlers.GetThumbnailURL(db))
			protected.POST("/thumbnails/batch", handlers.GetBatchThumbnailURLs(db))
		}
	}

	// Initialize and register the flashcard schema
	flashcardHandler := handlers.NewFlashcardHandler(db)
	if err := flashcardHandler.Init(); err != nil {
		log.Printf("Error initializing flashcard schema: %v", err)
	}
}
