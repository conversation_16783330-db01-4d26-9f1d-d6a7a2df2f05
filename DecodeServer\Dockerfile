FROM golang:1.23-alpine

# Install required packages
RUN apk add --no-cache git ca-certificates tzdata build-base poppler-utils imagemagick

WORKDIR /app

# Set environment variables
ENV GOFLAGS="-buildvcs=false"
ENV CGO_ENABLED=0
ENV GO111MODULE=on

# Copy go.mod and go.sum first for better layer caching
COPY go.mod go.sum* ./

# Fix Go version in go.mod
RUN go mod edit -go=1.23 && \
    sed -i '/toolchain/d' go.mod

# Download dependencies with expanded scope
RUN go mod download && \
    go mod tidy

# Copy source code
COPY . .

# Ensure missing packages are fetched (including internal packages)
RUN go mod tidy && \
    go mod download all

# Build the application
RUN go build -o app

# Configure runtime
EXPOSE 8080
CMD ["./app"] 