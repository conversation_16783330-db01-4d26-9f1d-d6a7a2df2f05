from openai import Async<PERSON>penAI
from dotenv import load_dotenv
import os
import faiss
import numpy as np
import asyncio
from ..ai_models.question_query import generate_search_query
from ..ai_models.articles_data import get_pubmed_results, parse_articles
from concurrent.futures import ThreadPoolExecutor
import functools

load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

if not api_key:
    raise ValueError("The OpenAI API key must be set in the environment variables.")

# Limit the number of threads to avoid overwhelming the system
thread_pool = ThreadPoolExecutor(max_workers=2)

# Get embeddings text
async def getEmbeddings(texts):
    response = await client.embeddings.create(
        model="text-embedding-3-small",
        input=texts
    )
    return [embedding.embedding for embedding in response.data]

# Rank articles
async def rankArticles(question, max_results=None, publication_types=None, start_year=None, end_year=None, cursor='*', min_citations=10, max_citations=None):
    # Generate search query
    search_query = await generate_search_query(question, publication_types)
    print(f"Search query: {search_query}")
    
    # Articles data
    articles, error, next_cursor, total_results = await get_pubmed_results(
        query=search_query,
        max_results=max_results,
        start_year=start_year,
        end_year=end_year,
        cursor=cursor,
        min_citations=min_citations,
        max_citations=max_citations
    )
    if articles is None:
        return None

    # Get separate embeddings for titles and abstracts
    title_texts = [search_query] + [article['title'] for article in articles]
    title_embeddings = await getEmbeddings(title_texts)
    
    # Get abstract embeddings (using empty string for missing abstracts)
    abstract_texts = [search_query] + [
        article.get('abstract', '').strip() or article['title'] 
        for article in articles
    ]
    abstract_embeddings = await getEmbeddings(abstract_texts)
    
    # Pre-compute arrays more efficiently
    query_embedding = np.array(title_embeddings[0]).reshape(1, -1)
    articles_title_embeddings = np.array(title_embeddings[1:])
    articles_abstract_embeddings = np.array(abstract_embeddings[1:])
    
    # Create single reusable FAISS index
    dimension = query_embedding.shape[1]
    index = faiss.IndexFlatL2(dimension)
    
    # Define computation functions
    def compute_title_distances():
        index_local = faiss.IndexFlatL2(dimension)  # Create local index for thread safety
        index_local.add(articles_title_embeddings)
        distances, _ = index_local.search(query_embedding, len(articles))
        return distances

    def compute_abstract_distances():
        index_local = faiss.IndexFlatL2(dimension)  # Create local index for thread safety
        index_local.add(articles_abstract_embeddings)
        distances, _ = index_local.search(query_embedding, len(articles))
        return distances

    # Run computations in parallel using the thread pool
    loop = asyncio.get_event_loop()
    title_future = loop.run_in_executor(thread_pool, compute_title_distances)
    abstract_future = loop.run_in_executor(thread_pool, compute_abstract_distances)
    
    # Wait for both computations to complete
    title_distances, abstract_distances = await asyncio.gather(title_future, abstract_future)
    
    # Combine scores with weights and penalize missing abstracts
    ranked_pairs = []
    for i in range(len(articles)):
        title_score = title_distances[0][i]
        abstract_score = abstract_distances[0][i]
        
        if articles[i].get('abstract') and articles[i]['abstract'].strip():
            combined_score = (0.5 * title_score + 0.5 * abstract_score)
        else:
            combined_score = title_score + 10.0
            
        ranked_pairs.append((combined_score, articles[i]))
    
    # Sort by score in ascending order (lower scores = more similar)
    ranked_articles = [article for _, article in sorted(ranked_pairs, key=lambda x: x[0])]
    return ranked_articles

