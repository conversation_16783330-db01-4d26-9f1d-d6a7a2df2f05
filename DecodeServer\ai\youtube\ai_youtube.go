package youtube

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"cloud.google.com/go/vertexai/genai"
	"google.golang.org/api/option"
)

// SummaryOptions contains configuration options for video summarization
type SummaryOptions struct {
	ModelID     string
	Detailed    bool
	MaxTokens   int
	Temperature float32
	UnitCount   int // Number of units to generate
}

// DefaultSummaryOptions returns the default configuration for video summarization
func DefaultSummaryOptions() SummaryOptions {
	return SummaryOptions{
		ModelID:     "gemini-2.5-flash-lite",
		Detailed:    true,
		MaxTokens:   8000, // Set to 8000 to stay under the API limit of 8193
		Temperature: 0.2,
		UnitCount:   5, // Default to 5 units
	}
}

// YouTubeSummarizer handles summarization of YouTube videos
type YouTubeSummarizer struct {
	projectID string
	location  string
	client    *genai.Client
}

// YouTubeUnit represents a structured educational unit from a YouTube video
type YouTubeUnit struct {
	Title          string
	YoutubeSummary string
	SourceText     string
}

// NewYouTubeSummarizer creates a new YouTube video summarizer
func NewYouTubeSummarizer() (*YouTubeSummarizer, error) {
	projectID := os.Getenv("VERTEX_PROJECT_ID")
	location := os.Getenv("VERTEX_LOCATION")
	credentialsPath := os.Getenv("VERTEX_CREDENTIALS")

	if projectID == "" || location == "" {
		return nil, fmt.Errorf("missing required environment variables: VERTEX_PROJECT_ID and VERTEX_LOCATION")
	}

	// Create Vertex AI client for Gemini
	ctx := context.Background()
	var client *genai.Client
	var err error

	// Handle credentials if provided
	if credentialsPath != "" {
		var jsonKey []byte

		// Check if credentialsPath is base64 encoded content
		if strings.HasPrefix(strings.TrimSpace(credentialsPath), "eyJ") || 
			strings.Contains(credentialsPath, "+") || 
			strings.Contains(credentialsPath, "/") || 
			strings.Contains(credentialsPath, "=") {
			// It looks like base64 encoded content, try to decode it
			decoded, err := base64.StdEncoding.DecodeString(credentialsPath)
			if err != nil {
				return nil, fmt.Errorf("failed to decode base64 credentials: %v", err)
			}
			jsonKey = decoded
			log.Printf("Successfully decoded base64 credentials for Vertex AI")
		} else {
			// It's a file path, read the file
			jsonKey, err = os.ReadFile(credentialsPath)
			if err != nil {
				return nil, fmt.Errorf("failed to read credentials file: %v", err)
			}
		}

		// Create client with the credentials JSON
		client, err = genai.NewClient(ctx, projectID, location, option.WithCredentialsJSON(jsonKey))
		if err != nil {
			return nil, fmt.Errorf("failed to create Vertex AI client with credentials: %v", err)
		}
	} else {
		// Use default credentials
		client, err = genai.NewClient(ctx, projectID, location)
		if err != nil {
			return nil, fmt.Errorf("failed to create Vertex AI client: %v", err)
		}
	}

	return &YouTubeSummarizer{
		projectID: projectID,
		location:  location,
		client:    client,
	}, nil
}

// Close closes the connection to Vertex AI
func (s *YouTubeSummarizer) Close() error {
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}

// SummarizeYouTubeVideo summarizes a YouTube video using Gemini AI
func (s *YouTubeSummarizer) SummarizeYouTubeVideo(ctx context.Context, youtubeURL string, opts SummaryOptions) (string, error) {
	if opts.ModelID == "" {
		opts = DefaultSummaryOptions()
	}

	// Create a generative model with the specified model ID
	model := s.client.GenerativeModel(opts.ModelID)

	// Configure the generation parameters
	model.SetTemperature(opts.Temperature)
	model.SetMaxOutputTokens(int32(opts.MaxTokens))

	// Create the prompt based on detailed or brief summary request
	prompt := "Give a detailed summary of this video."
	if !opts.Detailed {
		prompt = "Summarize this video briefly."
	}

	// Define the video input
	videoData := genai.FileData{
		MIMEType: "video/webm",
		FileURI:  youtubeURL,
	}

	// Generate content using the video and prompt
	resp, err := model.GenerateContent(ctx, videoData, genai.Text(prompt))
	if err != nil {
		return "", fmt.Errorf("failed to generate content: %v", err)
	}

	// Extract the text from the response
	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no content generated")
	}

	// Extract the generated text
	text, ok := resp.Candidates[0].Content.Parts[0].(genai.Text)
	if !ok {
		return "", fmt.Errorf("unexpected response format")
	}

	return string(text), nil
}

// GenerateStructuredUnits generates educational units from a YouTube video
func (s *YouTubeSummarizer) GenerateStructuredUnits(ctx context.Context, youtubeURL string, videoTitle string, opts SummaryOptions) ([]YouTubeUnit, string, error) {
	if opts.ModelID == "" {
		opts = DefaultSummaryOptions()
	}

	// Create a generative model with the specified model ID
	model := s.client.GenerativeModel(opts.ModelID)

	// Configure the generation parameters
	model.SetTemperature(opts.Temperature)
	model.SetMaxOutputTokens(int32(opts.MaxTokens))

	// Create a structured prompt for generating educational units
	prompt := s.buildUnitsPrompt(videoTitle, opts.UnitCount)

	// Define the video input
	videoData := genai.FileData{
		MIMEType: "video/webm",
		FileURI:  youtubeURL,
	}

	// Generate content using the video and prompt
	resp, err := model.GenerateContent(ctx, videoData, genai.Text(prompt))
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate structured content: %v", err)
	}

	// Extract the text from the response
	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return nil, "", fmt.Errorf("no content generated")
	}

	// Extract the generated text
	text, ok := resp.Candidates[0].Content.Parts[0].(genai.Text)
	if !ok {
		return nil, "", fmt.Errorf("unexpected response format")
	}

	// Get the full generated content
	fullContent := string(text)

	// Parse the content into units
	units := s.ParseIntoUnits(fullContent)

	return units, fullContent, nil
}

// buildUnitsPrompt creates a prompt for generating educational units from a YouTube video
func (s *YouTubeSummarizer) buildUnitsPrompt(videoTitle string, unitCount int) string {
	return fmt.Sprintf(`Analyze this YouTube video and create a structured educational course with %d distinct units.
	Each unit must follow this exact structure with markdown format:

	Unit 1: Clear Topic Title
	 -Bullet points for the unit with one or two sentences 
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences
	 
	
	Unit 2: Clear Topic Title
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences
	 -Bullet points for the unit with one or two sentences

	 So on...

	
	Requirements:
	1. Create exactly %d distinct units
	2. Each unit must start with "Unit X: " followed by a clear title
	3. Each unit must have a comprehensive summary
	4. Units should flow logically and build upon each other
	5. Keep summaries focused and educational
	6. CRUCIAL: Your units MUST cover the ENTIRE duration of the video from beginning to end
	7. CRUCIAL: Distribute the units evenly across the video's full timeline to ensure complete coverage
	8. The first unit should cover the introduction/beginning of the video
	9. The last unit should cover content from the final section of the video
	10. VERY IMPORTANT: Units MUST be numbered sequentially starting from 1, 2, 3, etc. Do not skip numbers or use non-sequential numbering.
	 
	Language requirements:
	1. The input content will be in any language, the output should be in any language, as long as it's the same language as the input content.
	2. The youtube units and summaries should be in the same language as the input content.

	Video Title: %s`, unitCount, unitCount, videoTitle)
}

// ParseIntoUnits splits content into logical educational units
func (s *YouTubeSummarizer) ParseIntoUnits(content string) []YouTubeUnit {
	// Updated regex to handle markdown formatted unit headers
	// Matches both "Unit X:" and "**Unit X:**" patterns
	unitRegex := regexp.MustCompile(`(?m)^(\*\*)?Unit\s+(\d+):(\*\*)?\s*([^\n]+)`)

	// Find all matches for unit headers
	matches := unitRegex.FindAllStringSubmatch(content, -1)
	matchLocations := unitRegex.FindAllStringSubmatchIndex(content, -1)

	if len(matches) == 0 {
		// Fallback if no unit markers found - return entire content as one unit
		return []YouTubeUnit{
			{
				Title:          "Complete Video Summary",
				YoutubeSummary: content,
				SourceText:     "Full video",
			},
		}
	}

	type unitWithNumber struct {
		number int
		unit   YouTubeUnit
	}
	var unitsWithNumbers []unitWithNumber

	// Extract units based on start/end positions
	for i := 0; i < len(matches); i++ {
		start := matchLocations[i][0]
		end := len(content)
		if i < len(matchLocations)-1 {
			end = matchLocations[i+1][0]
		}

		unitText := content[start:end]
		unitText = strings.TrimSpace(unitText)

		if unitText != "" {
			// Extract title with unit number
			title := "Unit"
			titleMatch := matches[i]
			unitNumStr := titleMatch[2]
			unitNumber, err := strconv.Atoi(unitNumStr)
			if err != nil {
				unitNumber = i + 1
			}

			title = fmt.Sprintf("Unit %s: %s", unitNumStr, strings.TrimSpace(titleMatch[4]))

			// Set a generic source text for each unit
			totalUnits := len(matches)
			sourceText := fmt.Sprintf("Part %d of %d", i+1, totalUnits)

			unitsWithNumbers = append(unitsWithNumbers, unitWithNumber{
				number: unitNumber,
				unit: YouTubeUnit{
					Title:          title,
					YoutubeSummary: unitText,
					SourceText:     sourceText,
				},
			})
		}
	}

	// Sort units by their number
	sort.Slice(unitsWithNumbers, func(i, j int) bool {
		return unitsWithNumbers[i].number < unitsWithNumbers[j].number
	})

	// Convert to final slice
	var units []YouTubeUnit
	for _, u := range unitsWithNumbers {
		units = append(units, u.unit)
	}

	return units
}
