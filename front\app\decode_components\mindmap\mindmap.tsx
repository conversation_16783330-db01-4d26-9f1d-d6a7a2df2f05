'use client';
import React, { useEffect, useState, useMemo } from 'react';
import ReactFlow, { 
  Background, 
  Controls,
  Position,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  NodeTypes,
  EdgeTypes,
  Node,
  Edge,
} from 'reactflow';
import { useAuth } from '@clerk/nextjs';
import 'reactflow/dist/style.css';
import { useTheme } from '@/components/theme/theme-provider';
import {  ChevronLeft, ChevronRight } from 'lucide-react';
import CustomNode from './mindmap_components/CustomNode';
import CustomEdge from './mindmap_components/CustomEdge';
import { API_URL } from '@/app/utils/decode_api';
import dagre from 'dagre';

interface MindmapProps {
  projectId?: string;
}

interface Project {
  id?: number;
  ID?: number;
  name?: string;
  Name?: string;
  description?: string;
  Description?: string;
  created_at?: string;
  CreatedAt?: string;
  updated_at?: string;
  UpdatedAt?: string;
  user_id?: number;
  UserID?: number;
  userId?: number;
  unit_count?: number;
  UnitCount?: number;
}

interface Unit {
  id?: number;
  ID?: number;
  title?: string;
  Title?: string;
  content?: string;
  Content?: string;
  project_id?: number;
  ProjectID?: number;
  description?: string;
  Description?: string;
  order?: number;
  Order?: number;
}

interface MindmapNode {
  id?: string | number;
  ID?: string | number;
  data?: { label?: string; Label?: string; [key: string]: unknown };
  Data?: { label?: string; Label?: string; [key: string]: unknown };
  position?: { x: number; y: number };
  Position?: { x: number; y: number };
}

interface MindmapEdge {
  id?: string | number;
  ID?: string | number;
  source?: string | number;
  Source?: string | number;
  target?: string | number;
  Target?: string | number;
  style?: React.CSSProperties;
}

const MindmapContent: React.FC<MindmapProps> = ({ projectId }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState(false);
  const { theme } = useTheme();

  // Project and Unit related states
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [units, setUnits] = useState<Unit[]>([]);
  
  // Add state for carousel
  const [carouselIndex, setCarouselIndex] = useState(0);

  // Get authentication from Clerk
  const { getToken } = useAuth();

  // Define nodeTypes outside of render cycle to avoid unnecessary re-creation
  const nodeTypes = useMemo<NodeTypes>(() => ({ 
    custom: CustomNode 
  }), []);

  // Define edgeTypes for custom edges
  const edgeTypes = useMemo<EdgeTypes>(() => ({ 
    customEdge: CustomEdge 
  }), []);

  // Navigate carousel
  const navigateCarousel = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCarouselIndex(prev => (prev > 0 ? prev - 1 : units.length - 1));
    } else {
      setCarouselIndex(prev => (prev < units.length - 1 ? prev + 1 : 0));
    }
  };

  // When carousel index changes, update and fetch the mindmap
  useEffect(() => {
    if (units.length > 0 && carouselIndex >= 0 && carouselIndex < units.length) {
      // Make sure we have a selected project with a valid ID before fetching mindmap
      if (selectedProject && (selectedProject.id || selectedProject.ID) && units[carouselIndex] && (units[carouselIndex].id || units[carouselIndex].ID)) {
        const projectId = selectedProject.id || selectedProject.ID;
        const unitId = units[carouselIndex].id || units[carouselIndex].ID;
        
        if (projectId !== undefined && unitId !== undefined) {
          console.log('Fetching mindmap with project ID:', projectId, 'and unit ID:', unitId);
          fetchMindmap(projectId, unitId);
        }
      }
    }
  }, [carouselIndex, units, selectedProject]);

  // Use the projectId from props if available
  useEffect(() => {
    if (projectId) {
      console.log("Mindmap component: Loading project with ID:", projectId);
      // Find the project in the list or fetch it
      const fetchAndSelectProject = async () => {
        try {
          const token = await getToken();
          if (!token) return;
          
          // Make sure projectId is not undefined before making the request
          if (!projectId) {
            console.error("Project ID is undefined");
            return;
          }
          
          console.log("Fetching project with ID:", projectId);
          const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });
          
          if (!response.ok) {
            console.error(`Failed to fetch project: ${response.status}`);
            throw new Error(`Failed to fetch project: ${response.status}`);
          }
          
          const data = await response.json();
          if (data.project) {
            console.log("Project data received:", data.project);
            // Set the selected project
            setSelectedProject(data.project);
            
            // Extract project ID, handling both uppercase and lowercase property names
            const rawProjectId = data.project.id || data.project.ID;
            console.log("Raw project ID:", rawProjectId, "type:", typeof rawProjectId);
            
            // Safely convert to number if it's a string
            const projectIdNumber = typeof rawProjectId === 'string' ? parseInt(rawProjectId, 10) : rawProjectId;
            
            if (projectIdNumber && !isNaN(projectIdNumber)) {
              console.log("Using project ID for fetching units:", projectIdNumber);
              fetchUnits(projectIdNumber);
            } else {
              console.error("Invalid project ID:", rawProjectId);
            }
          } else if (data.ID || data.id) {
            // If data itself is the project
            console.log("Direct project data received:", data);
            setSelectedProject(data);
            
            // Extract project ID, handling both uppercase and lowercase property names
            const rawProjectId = data.id || data.ID;
            console.log("Raw project ID:", rawProjectId, "type:", typeof rawProjectId);
            
            // Safely convert to number if it's a string
            const projectIdNumber = typeof rawProjectId === 'string' ? parseInt(rawProjectId, 10) : rawProjectId;
            
            if (projectIdNumber && !isNaN(projectIdNumber)) {
              console.log("Using project ID for fetching units:", projectIdNumber);
              fetchUnits(projectIdNumber);
            } else {
              console.error("Invalid project ID:", rawProjectId);
            }
          } else {
            console.error("Project data missing in response:", data);
          }
        } catch (error) {
          console.error("Error fetching specific project:", error);
        }
      };
      
      fetchAndSelectProject();
    } else {
      // If no specific projectId, fetch all projects
      fetchProjects();
    }
  }, [projectId]);

  // Fetch projects
  const fetchProjects = async () => {
    try {
      // Get the Clerk token when making the request
      const authToken = await getToken();
      
      const response = await fetch(`${API_URL}/api/decode/projects`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });
      if (!response.ok) {
        console.error('Failed to fetch projects, status:', response.status);
        throw new Error('Failed to fetch projects');
      }
      const data = await response.json();
      
      console.log('Fetched projects data in mindmap.tsx:', data);
      
      let projectsData = [];
      
      // Handle different response formats
      if (Array.isArray(data)) {
        // Backend returned an array directly
        projectsData = data;
      } else if (data && Array.isArray(data.projects)) {
        // Backend returned {projects: [...]}
        projectsData = data.projects;
      } else if (data && data.project) {
        // Backend returned a single project {project: {...}}
        projectsData = [data.project];
      }
      
      // Normalize projects to ensure consistent property naming
      const normalizedProjects = projectsData.map((project: Project) => {
        if (!project) return null;
        return {
          id: project.ID || project.id,
          name: project.name || project.Name,
          description: project.description || project.Description,
          createdAt: project.CreatedAt || project.created_at,
          updatedAt: project.UpdatedAt || project.updated_at,
          userID: project.UserID || project.user_id || project.userId,
        };
      }).filter(Boolean); // Remove any null entries
      
      // Select the first project by default if none is selected
      if (normalizedProjects.length > 0 && !selectedProject) {
        setSelectedProject(normalizedProjects[0]);
        if (normalizedProjects[0].id !== undefined) {
          fetchUnits(normalizedProjects[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    }
  };

  // Fetch units when project is selected (redundant for projectId case, but needed for other cases)
  useEffect(() => {
    if (selectedProject && !projectId) {
      // Only call this if we're not already handling it via the projectId prop
      // This prevents duplicate API calls
      if (selectedProject.id !== undefined) {
        fetchUnits(selectedProject.id);
      }
    }
  }, [selectedProject, projectId]);

  const fetchUnits = async (projectId: number) => {
    // Guard against undefined or invalid projectId
    if (!projectId || isNaN(projectId)) {
      console.error('Invalid project ID provided to fetchUnits:', projectId);
      return;
    }
    
    try {
      const authToken = await getToken();
      // Using the same endpoint as in dashboard.tsx
      console.log('Fetching units for project ID:', projectId);
      const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch units');
      const data = await response.json();
      
      console.log('Project units response:', data);
      
      // Check all possible paths where units might be located
      let unitsToSet: Unit[] = [];
      
      if (data.project?.Units && Array.isArray(data.project.Units)) {
        unitsToSet = data.project.Units;
      } else if (data.project?.units && Array.isArray(data.project.units)) {
        unitsToSet = data.project.units;
      } else if (data.Units && Array.isArray(data.Units)) {
        unitsToSet = data.Units;
      } else if (data.units && Array.isArray(data.units)) {
        unitsToSet = data.units;
      } else if (Array.isArray(data)) {
        unitsToSet = data;
      }

      // Normalize and sort the units
      const normalizedUnits = unitsToSet
        .filter(unit => !!unit)
        .map(unit => {
          // Handle both uppercase and lowercase property names
          const unitId = unit.id || unit.ID;
          const unitTitle = unit.title || unit.Title;
          const unitContent = unit.content || unit.Content;
          const unitDescription = unit.description || unit.Description;
          const unitProjectId = unit.project_id || unit.ProjectID || projectId;
          const unitOrder = unit.order || unit.Order || 0;
          
          return {
            id: typeof unitId === 'string' ? parseInt(unitId, 10) : unitId || 0,
            title: unitTitle || '',
            content: unitContent || '',
            project_id: typeof unitProjectId === 'string' ? parseInt(unitProjectId, 10) : unitProjectId || projectId,
            description: unitDescription || '',
            order: typeof unitOrder === 'string' ? parseInt(unitOrder, 10) : unitOrder
          };
        })
        .sort((a, b) => {
          // Try to sort by order if available
          const orderA = (a as Unit).order || (a as Unit).Order || 0;
          const orderB = (b as Unit).order || (b as Unit).Order || 0;
          return orderA - orderB;
        });

      console.log('Setting normalized units:', normalizedUnits);
      setUnits(normalizedUnits);
      
      // Units are already sorted by order so we can safely pick the first one
      if (normalizedUnits.length > 0) {
        // Reset carousel index to ensure we always start with the first ordered unit
        setCarouselIndex(0);
      }
    } catch (error) {
      console.error('Error fetching units:', error);
      setUnits([]);
    }
  };

  // ---------------- Fetch mindmap data for a specific project and unit ----------------
  const fetchMindmap = async (projectId: string | number, unitId: string | number) => {
    // Validate projectId
    if (!projectId) {
      console.error('Invalid project ID: undefined');
      setLoading(false);
      return;
    }

    if (typeof projectId === 'string' && projectId.trim() === '') {
      console.error('Invalid project ID: empty string');
      setLoading(false);
      return;
    }

    // Validate unitId
    if (!unitId) {
      console.error('Invalid unit ID: undefined');
      setLoading(false);
      return;
    }

    if (typeof unitId === 'string' && unitId.trim() === '') {
      console.error('Invalid unit ID: empty string');
      setLoading(false);
      return;
    }

    setLoading(true);
    const dagreGraph = new dagre.graphlib.Graph();
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    // Configure layout direction (Left-to-Right) and increase horizontal separation
    dagreGraph.setGraph({ rankdir: 'LR', nodesep: 80, ranksep: 400 });

    // Define estimated node dimensions (adjust as needed)
    const nodeWidth = 170;
    const nodeHeight = 50;

    try {
      const authToken = await getToken();
      console.log(`Fetching mindmap with project ID: ${projectId} and unit ID: ${unitId}`);
      const response = await fetch(`${API_URL}/api/decode/mindmap/${projectId}/${unitId}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch mindmap: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Mindmap data:', data);
      
      // Access the nested data property from the response
      const mindmapData = data.data;
      
      if (!mindmapData) {
        console.error('Invalid mindmap data structure:', data);
        setLoading(false);
        alert('Invalid mindmap data structure. Please try again.');
        return;
      }
      
      // Handle both uppercase and lowercase property names
      const nodes = mindmapData.nodes || mindmapData.Nodes;
      const edges = mindmapData.edges || mindmapData.Edges;
      
      if (!nodes || !edges) {
        console.error('Invalid mindmap data structure:', mindmapData);
        setLoading(false);
        alert('Invalid mindmap data structure. Please try again.');
        return;
      }
      
      // Convert nodes and edges to the format expected by react-flow
      const flowNodes = nodes.map((node: MindmapNode) => {
        // Handle both uppercase and lowercase properties
        const id = String(node.id || node.ID || `node-${Math.random().toString(36).substr(2, 9)}`);
        const data = node.data || node.Data || { label: 'Unknown' };
        
        // Add node to Dagre graph for layout calculation
        dagreGraph.setNode(id, { width: nodeWidth, height: nodeHeight });
        
        return {
          id: id,
          type: 'custom',
          data: {
            ...data,
            label: data.label || data.Label || 'Unknown',
            isRoot: id === '1' 
          },
          position: { x: 0, y: 0 },
          // Set source/target handles based on layout direction (TB)
          sourcePosition: Position.Right,
          targetPosition: Position.Left,
        };
      });
      
      const flowEdges = edges.map((edge: MindmapEdge) => {
        // Handle both uppercase and lowercase properties
        const id = String(edge.id || edge.ID || `edge-${Math.random().toString(36).substr(2, 9)}`);
        const source = String(edge.source || edge.Source);
        const target = String(edge.target || edge.Target);
        
        if (!source || !target) {
          console.warn('Invalid edge data:', edge);
          return null;
        }
        
        // Add edge to Dagre graph
        dagreGraph.setEdge(source, target);

        return {
          id: id,
          source: source,
          target: target,
          type: 'customEdge', 
          targetHandle: 'l',
          sourceHandle: 'r',
          style: edge.style || {}
        };
      }).filter(Boolean) as Edge[];
      
      // Calculate layout using Dagre
      dagre.layout(dagreGraph);

      // Apply calculated positions to React Flow nodes
      const positionedNodes = flowNodes.map((node: Node) => {
        const nodeWithPosition = dagreGraph.node(node.id);
        
        node.position = {
          x: nodeWithPosition.x - nodeWidth / 2,
          y: nodeWithPosition.y - nodeHeight / 2,
        };
        // Ensure handles match LR layout after calculation
        node.targetPosition = Position.Left;
        node.sourcePosition = Position.Right;
        return node;
      });
      
      setNodes(positionedNodes);
      setEdges(flowEdges);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching mindmap:', error);
      setLoading(false);
      alert('Failed to fetch mindmap. Please try again.');
    }
  };

  return (
    <div className={`flex flex-col h-full w-full ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-900'}`}>
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className={`flex-1 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
          >
            <Background
              color={theme === 'dark' ? '#333' : '#ccc'}
              gap={16}
              size={1}
            />
            <Controls />
          </ReactFlow>
        </div>
        
        {/* Units carousel - minimalistic bottom navigation */}
        {selectedProject && (
          <div className="w-full flex-shrink-0 py-2 px-3 rounded-2xl border-gray-100 dark:border-gray-800 bg-white dark:bg-[hsl(0_0%_9%)] sticky bottom-0 z-10 shadow-sm">
            {units.length > 0 && carouselIndex < units.length && (
              <div className="flex items-center justify-between">
                {/* Left navigation button */}
                <button 
                  onClick={() => navigateCarousel('prev')} 
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    theme === 'dark' 
                      ? `text-gray-400 hover:text-gray-200 hover:bg-gray-800 ${loading ? 'opacity-50' : ''}` 
                      : `text-gray-500 hover:text-gray-700 hover:bg-gray-100 ${loading ? 'opacity-50' : ''}`
                  }`}
                  disabled={units.length <= 1 || loading}
                  aria-label="Previous unit"
                >
                  <ChevronLeft size={18} />
                </button>
                
                {/* Center - unit info */}
                <div className="flex flex-col items-center">
                  <div className={`text-sm font-medium truncate max-w-[200px] sm:max-w-[300px] ${
                    loading
                      ? 'text-gray-400 dark:text-gray-500'
                      : 'text-gray-700 dark:text-gray-300'
                  }`}>
                    {loading ? 'Loading...' : units[carouselIndex].title}
                  </div>
                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-0.5">
                    {loading ? '' : `${carouselIndex + 1} / ${units.length}`}
                  </div>
                </div>
                
                {/* Right navigation button */}
                <button 
                  onClick={() => navigateCarousel('next')} 
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    theme === 'dark' 
                      ? `text-gray-400 hover:text-gray-200 hover:bg-gray-800 ${loading ? 'opacity-50' : ''}` 
                      : `text-gray-500 hover:text-gray-700 hover:bg-gray-100 ${loading ? 'opacity-50' : ''}`
                  }`}
                  disabled={units.length <= 1 || loading}
                  aria-label="Next unit"
                >
                  <ChevronRight size={18} />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const Mindmap: React.FC<MindmapProps> = ({ projectId }) => {
  return (
    <ReactFlowProvider>
      <MindmapContent projectId={projectId} />
    </ReactFlowProvider>
  );
};

export default Mindmap;
