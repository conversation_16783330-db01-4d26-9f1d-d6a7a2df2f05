'use client'

import Link from 'next/link'
import { Check } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { HeroHeader } from '@/containers/main/hero5-header'
import { MainThemeProvider } from '@/components/theme/main-theme-provider'
import { useLanguage } from '@/app/providers/language-provider'

const PricingContent = () => {
    const { t } = useLanguage()
    // Only monthly plan is available
    const proMonthlyPrice = 15;

    const pricingPlans = {
        free: {
            nameKey: 'pricing.freePlanName',
            // Price for free is always 0
            descriptionKey: 'pricing.freePlanDesc',
            featureKeys: ['pricing.freeFeature1', 'pricing.freeFeature3']
        },
        pro: {
            nameKey: 'pricing.proPlanName',
            // Use defined prices
            descriptionKey: 'pricing.proPlanDescMonthly',
            featureKeys: [
                'pricing.proFeature1', 
                'pricing.proFeature2', 
                'pricing.proFeature3', 
                'pricing.proFeature4', 
                'pricing.proFeature5'
            ]
        }
    }
 
    
    return (
        <section id="pricing" className="py-16 md:py-32 bg-white dark:bg-[hsl(240_10%_3.9%)]">
            <div className="mx-auto max-w-6xl px-6">
                
                <div className="mx-auto max-w-2xl space-y-6 text-center">
                    <h1 className="text-center text-4xl font-semibold lg:text-5xl">{t('pricing.title')}</h1>
                    <p>{t('pricing.subtitle')}</p>
                
                    
                </div>

                <div className="mt-8 grid gap-6 [--color-card:var(--color-muted)] *:border-none *:shadow-none md:mt-20 md:grid-cols-2 dark:[--color-muted:var(--color-zinc-900)]">
                    <Card className="flex flex-col">
                        <CardHeader>
                            <CardTitle className="font-medium">{t(pricingPlans.free.nameKey)}</CardTitle>
                            {/* Display $0 directly */}
                            <span className="my-3 block text-2xl font-semibold">$0 / mo</span> 
                            <CardDescription className="text-sm">{t(pricingPlans.free.descriptionKey)}</CardDescription>
                        </CardHeader>

                        <CardContent className="space-y-4">
                            <hr className="border-dashed" />

                            <ul className="list-outside space-y-3 text-sm">
                                {pricingPlans.free.featureKeys.map((key, index) => (
                                    <li key={index} className="flex items-center gap-2">
                                        <Check className="size-3" />
                                        {t(key)}
                                    </li>
                                ))}
                            </ul>
                        </CardContent>

                        <CardFooter className="mt-auto">
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/sign-in">{t('pricing.getStarted')}</Link>
                            </Button>
                        </CardFooter>
                    </Card>

                    <Card className="relative">
                        <span className="absolute inset-x-0 -top-3 mx-auto flex h-6 w-fit items-center rounded-full from-neutral-400 to-neutral-300 bg-gradient-to-br px-3 py-1 text-xs font-medium text-neutral-950 ring-1 ring-inset ring-white/20 ring-offset-1 ring-offset-neutral-950/5">{t('pricing.popular')}</span>

                        <div className="flex flex-col">
                            <CardHeader>
                                <CardTitle className="font-medium">{t(pricingPlans.pro.nameKey)}</CardTitle>
                                <span className="my-3 block text-2xl font-semibold">
                                    ${proMonthlyPrice} / mo
                                </span>
                                <CardDescription className="text-sm">
                                    {t(pricingPlans.pro.descriptionKey)}
                                </CardDescription>
                            </CardHeader>

                            <CardContent className="space-y-4">
                                <hr className="border-dashed" />
                                <ul className="list-outside space-y-3 text-sm">
                                    {pricingPlans.pro.featureKeys.map((key, index) => (
                                        <li key={index} className="flex items-center gap-2">
                                            <Check className="size-3" />
                                            {t(key)}
                                        </li>
                                    ))}
                                </ul>
                            </CardContent>

                            <CardFooter>
                                <Button asChild className="w-full bg-neutral-900 hover:bg-neutral-800 text-white dark:bg-neutral-800 dark:hover:bg-neutral-700">
                                    <Link href="/sign-up">{t('pricing.upgradeToPro')}</Link>
                                </Button>
                            </CardFooter>
                        </div>
                    </Card>
                </div>
            </div>
        </section>
    )
}

export default function PricingPage() {
    return (
        <MainThemeProvider>
            <HeroHeader />
            <PricingContent />
        </MainThemeProvider>
    )
}
