# subscriptions/revenue_cat/RevenueCat_webhook.py

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
import json
from ..models import UserSubscription
from datetime import datetime
import hmac
import hashlib
import os
import dotenv

dotenv.load_dotenv()

REVENUECAT_PRODUCT_MAPPING = {
    'premium_monthly:premium-monthly': 'monthly',
    'premium_yearly:yearly': 'yearly',
    'none': 'free trial'
}


def get_subscription_type(product_id):
    """Helper function to map RevenueCat product IDs to subscription types"""
    return REVENUECAT_PRODUCT_MAPPING.get(product_id, 'none')

@csrf_exempt
@require_POST
def revenuecat_webhook(request):
    try:
        payload = json.loads(request.body)
        event = payload.get('event', {})
        event_type = event.get('type')
        
        # Validate webhook secret (except for test events)
        if event_type != 'TEST':
            webhook_secret = os.getenv('REVENUECAT_WEBHOOK_SECRET')
            auth_header = request.headers.get('Authorization')
            if not auth_header or auth_header != webhook_secret:
                return JsonResponse({'error': 'Invalid authorization'}, status=401)

        # Essential event handlers
        if event_type == 'INITIAL_PURCHASE':
            handle_initial_purchase(event)
        elif event_type == 'RENEWAL':
            handle_renewal(event)
        elif event_type == 'CANCELLATION':
            handle_cancellation(event)
        elif event_type == 'EXPIRATION':
            handle_expiration(event)
        elif event_type == 'BILLING_ISSUE':
            handle_billing_issue(event)
        elif event_type == 'TEST':
            print("📝 Received test webhook from RevenueCat")
            return JsonResponse({'status': 'success', 'message': 'Test webhook received'})
        
        return JsonResponse({'status': 'success'})
    except Exception as e:
        print(f"❌ Error processing webhook: {str(e)}")
        return JsonResponse({'error': str(e)}, status=400)

def handle_initial_purchase(event):
    user_id = event.get('app_user_id')
    subscription_data = event
    product_id = subscription_data.get('product_id')
    
    try:
        subscription, created = UserSubscription.objects.get_or_create(
            user_id=user_id,
            defaults={
                'subscription_provider': 'revenuecat',
                'subscription_status': 'active',
                'revenuecat_user_id': user_id,
                'revenuecat_product_id': product_id,
                'subscription_type': get_subscription_type(product_id),
                'subscription_end_date': datetime.fromtimestamp(subscription_data.get('expiration_at_ms', 0)/1000)
            }
        )
        
        # Update all relevant fields if subscription already exists
        if not created:
            subscription.subscription_status = 'active'
            subscription.subscription_type = get_subscription_type(product_id)
            subscription.revenuecat_product_id = product_id
            subscription.subscription_end_date = datetime.fromtimestamp(subscription_data.get('expiration_at_ms', 0)/1000)
            subscription.will_cancel = False  # Reset cancellation flag
            subscription.save()
            
        print(f"✅ Successfully processed RevenueCat purchase for user {user_id}")
    except Exception as e:
        print(f"❌ Error processing RevenueCat purchase: {str(e)}")
        raise

def handle_renewal(event):
    user_id = event['app_user_id']
    subscription_data = event
    product_id = subscription_data.get('product_id')
    
    try:
        subscription = UserSubscription.objects.get(user_id=user_id)
        subscription.subscription_status = 'active'
        subscription.subscription_type = get_subscription_type(product_id)
        subscription.subscription_end_date = datetime.fromtimestamp(subscription_data.get('expiration_at_ms', 0)/1000)
        subscription.will_cancel = False  # Reset the cancellation flag on renewal
        subscription.save()
    except UserSubscription.DoesNotExist:
        print(f"No subscription found for user {user_id}")

def handle_cancellation(event):
    user_id = event['app_user_id']
    subscription_data = event
    
    try:
        subscription = UserSubscription.objects.get(user_id=user_id)
        subscription.will_cancel = True
        subscription.subscription_end_date = datetime.fromtimestamp(subscription_data.get('expiration_at_ms', 0)/1000)
        subscription.save()
    except UserSubscription.DoesNotExist:
        print(f"No subscription found for user {user_id}")

def handle_expiration(event):
    user_id = event['app_user_id']
    
    try:
        subscription = UserSubscription.objects.get(user_id=user_id)
        subscription.subscription_status = 'expired'
        subscription.subscription_type = 'free trial'
        subscription.will_cancel = False
        subscription.revenuecat_product_id = None
        subscription.subscription_end_date = None
        subscription.save()
    except UserSubscription.DoesNotExist:
        print(f"No subscription found for user {user_id}")

def handle_billing_issue(event):
    user_id = event['app_user_id']
    try:
        subscription = UserSubscription.objects.get(user_id=user_id)
        subscription.subscription_status = 'past_due'
        subscription.save()
    except UserSubscription.DoesNotExist:
        print(f"No subscription found for user {user_id}")