# Generated by Django 5.1.2 on 2024-10-28 20:57

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.Char<PERSON>ield(max_length=255)),
                ('search_count', models.IntegerField(default=0)),
                ('chat_count', models.IntegerField(default=0)),
                ('last_reset', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'subscription_usage',
            },
        ),
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.Char<PERSON>ield(max_length=255)),
                ('stripe_customer_id', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('search_subscription_id', models.CharField(blank=True, max_length=255, null=True)),
                ('chat_subscription_id', models.CharField(blank=True, max_length=255, null=True)),
                ('search_subscription_status', models.CharField(default='inactive', max_length=50)),
                ('chat_subscription_status', models.CharField(default='inactive', max_length=50)),
                ('search_count', models.IntegerField(default=0)),
                ('chat_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'user_subscriptions',
            },
        ),
    ]
