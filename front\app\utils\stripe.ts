import { loadStripe, Stripe } from '@stripe/stripe-js';

// Singleton pattern for Stripe instance
let stripePromise: Promise<Stripe | null>;

/**
 * Gets a Stripe instance to use for client-side operations
 * Using a singleton pattern to avoid loading <PERSON><PERSON> multiple times
 */
export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return stripePromise;
};

/**
 * Subscription types supported by the application
 */
export type SubscriptionType = 'monthly' | 'yearly' | 'none' | 'unknown';

/**
 * Subscription status values from Stripe
 */
export type SubscriptionStatus = 
  | 'active' 
  | 'trialing' 
  | 'incomplete' 
  | 'incomplete_expired' 
  | 'past_due' 
  | 'canceled' 
  | 'unpaid' 
  | 'none';

/**
 * Subscription details returned from the API
 */
export interface SubscriptionDetails {
  id: string;
  status: SubscriptionStatus;
  current_period_end: string;
  plan_type: SubscriptionType;
  cancel_at_period_end: boolean;
}

/**
 * Subscription data returned from the API
 */
export interface SubscriptionData {
  status: SubscriptionStatus;
  details: SubscriptionDetails | null;
  free_projects_count?: number;
  free_projects_limit?: number;
  free_projects_remaining?: number;
}

/**
 * Usage statistics for the subscription
 */
export interface UsageStats {
  subscription_type: SubscriptionType;
  status: string;
  current_period_end: string | null;
  will_cancel: boolean;
  created_at: string;
  updated_at: string;
  search: { 
    usage: number; 
    limit: number 
  };
  chat: { 
    usage: number; 
    limit: number 
  };
}

/**
 * Format a subscription status for display
 */
export const formatSubscriptionStatus = (status: string): string => {
  switch (status) {
    case 'active':
      return 'Active';
    case 'trialing':
      return 'Trial';
    case 'past_due':
      return 'Past Due';
    case 'canceled':
      return 'Cancelled';
    case 'unpaid':
      return 'Unpaid';
    case 'incomplete':
      return 'Incomplete';
    case 'incomplete_expired':
      return 'Expired';
    default:
      return 'Free Trial';
  }
};

/**
 * Format a subscription type for display
 */
export const formatSubscriptionType = (
  type: SubscriptionType,
): string => {
  if (type === 'monthly') return 'Monthly';
  if (type === 'yearly') return 'Yearly';
  // If type is 'none', 'unknown', or any other value, it defaults to Free Trial.
  // The detailed status of the subscription (active, trialing, etc.) 
  // is handled by formatSubscriptionStatus.
  return 'Free Trial';
};
