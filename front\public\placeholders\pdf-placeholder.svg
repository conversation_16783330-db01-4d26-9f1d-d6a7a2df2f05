<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="260px" viewBox="0 0 200 260" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>PDF Document Placeholder</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="pdfGradient">
            <stop stop-color="#F5F5F5" offset="0%"></stop>
            <stop stop-color="#E0E0E0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Document background -->
        <rect fill="url(#pdfGradient)" x="20" y="10" width="160" height="240" rx="8"></rect>
        
        <!-- PDF logo -->
        <g transform="translate(60, 80)">
            <rect fill="#FF5252" x="0" y="0" width="80" height="80" rx="4"></rect>
            <text font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF" text-anchor="middle" x="40" y="50">
                PDF
            </text>
        </g>
        
        <!-- Document lines -->
        <rect fill="#CCCCCC" x="40" y="180" width="120" height="6" rx="3"></rect>
        <rect fill="#CCCCCC" x="40" y="200" width="80" height="6" rx="3"></rect>
        <rect fill="#CCCCCC" x="40" y="220" width="100" height="6" rx="3"></rect>
        
        <!-- Folded corner -->
        <path d="M160,10 L180,30 L160,30 Z" fill="#D0D0D0"></path>
    </g>
</svg>
