# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Environment variables
.env
.venv
env/
venv/
ENV/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Test cache
.coverage
htmlcov/
.tox/
.pytest_cache/
# Local development
*.DS_Store

# Add to .gitignore
clerk_public_key.pem
.env.example
.env.local
.env.production
.env.development