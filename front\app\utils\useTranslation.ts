import { useState, useEffect } from 'react'
import { useLanguage } from "@/app/providers/language-provider"
import type { Translations } from '@/app/utils/types/language'
import { getTranslations } from './translations'

export function useTranslation() {
  const { language } = useLanguage()
  const [translations, setTranslations] = useState<Translations>({})

  useEffect(() => {
    getTranslations(language).then(setTranslations)
  }, [language])

  const t = (key: string) => {
    const result = key.split('.').reduce<string | number | Translations | undefined>((obj, k) => 
      obj && typeof obj === 'object' ? (obj as Record<string, unknown>)[k] as string | number | Translations | undefined : undefined, 
      translations
    );
    return typeof result === 'string' ? result : key;
  }

  return { t }
} 