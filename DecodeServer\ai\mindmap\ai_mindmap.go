package mindmap

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/joho/godotenv"
	"google.golang.org/genai"
)

// Generator handles mindmap generation using AI
type Generator struct {
	client *genai.Client
	config ModelConfig
}

// ModelConfig represents the configuration for the AI model
type ModelConfig struct {
	Model       string  // Model version to use
	Temperature float32 // Controls randomness (0.0 to 1.0)
	MaxTokens   int32   // Maximum tokens to generate
}

// DefaultConfig returns the default model configuration
func DefaultConfig() ModelConfig {
	return ModelConfig{
		Model:       "gemini-2.5-flash", // Using the pro version for better performance
		Temperature: 0.2,                // Similar to OpenAI config
		MaxTokens:   8000,
	}
}

// loadEnv loads environment variables from .env file
func loadEnv() error {
	// Try to find .env file in current directory and parent directories
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	for {
		envFile := filepath.Join(dir, ".env")
		if _, err := os.Stat(envFile); err == nil {
			// Found .env file, load it
			if err := godotenv.Load(envFile); err != nil {
				return fmt.Errorf("error loading .env file: %v", err)
			}
			return nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}

	return fmt.Errorf(".env file not found in current or parent directories")
}

// NewGenerator creates a new mindmap generator
func NewGenerator() (*Generator, error) {
	// Load environment variables from .env file
	if err := loadEnv(); err != nil {
		fmt.Printf("Warning: %v\n", err)
	}

	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("GEMINI_API_KEY environment variable is not set. Please add it to your .env file")
	}

	ctx := context.Background()
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		APIKey:  apiKey,
		Backend: genai.BackendGeminiAPI,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %v", err)
	}

	return &Generator{
		client: client,
		config: DefaultConfig(),
	}, nil
}

// SetConfig updates the model configuration
func (g *Generator) SetConfig(config ModelConfig) {
	g.config = config
}

// GenerateFromContent generates a mindmap from the given content
func (g *Generator) GenerateFromContent(ctx context.Context, content string, customPrompt string) (string, error) {
	prompt := g.buildPrompt(content, customPrompt)

	response, err := g.generateContent(ctx, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate content: %v", err)
	}

	// Extract React Flow data from response
	reactFlowData, err := g.extractReactFlowData(response)
	if err != nil {
		return "", fmt.Errorf("failed to extract React Flow data: %v", err)
	}

	return reactFlowData, nil
}

// generateContent sends a prompt to the AI model and returns the response
func (g *Generator) generateContent(ctx context.Context, prompt string) (string, error) {
	// Create the content from the prompt
	content := genai.Text(prompt)

	// Set generation config
	config := &genai.GenerateContentConfig{
		Temperature:     genai.Ptr(float32(g.config.Temperature)),
		MaxOutputTokens: genai.Ptr(int32(g.config.MaxTokens)),
	}

	// Generate content
	result, err := g.client.Models.GenerateContent(ctx, g.config.Model, content, config)
	if err != nil {
		return "", fmt.Errorf("error generating content: %v", err)
	}

	if len(result.Candidates) == 0 {
		return "", fmt.Errorf("no candidates in response")
	}

	// Get the text from the response
	text := result.Text()

	return text, nil
}

// extractReactFlowData extracts the React Flow data structure from the AI response
func (g *Generator) extractReactFlowData(response string) (string, error) {
	// Clean up any newlines and normalize spaces
	jsonData := strings.TrimSpace(response)

	// Remove any markdown code block formatting if present
	jsonData = regexp.MustCompile("^```(json)?").ReplaceAllString(jsonData, "")
	jsonData = regexp.MustCompile("```$").ReplaceAllString(jsonData, "")

	// Try to parse the response as JSON directly
	var jsonObj interface{}
	err := json.Unmarshal([]byte(jsonData), &jsonObj)

	if err != nil {
		// If initial parse fails, look for a JSON object in the response
		start := strings.Index(response, "{")
		end := strings.LastIndex(response, "}")

		if start == -1 || end == -1 || end <= start {
			return "", fmt.Errorf("could not find valid JSON object in response")
		}

		// Extract what looks like JSON
		jsonData = response[start : end+1]

		// Remove any newlines and normalize spaces
		jsonData = strings.ReplaceAll(jsonData, "\n", "")
		jsonData = strings.ReplaceAll(jsonData, "\r", "")

		// Clean up any multiple spaces
		for strings.Contains(jsonData, "  ") {
			jsonData = strings.ReplaceAll(jsonData, "  ", " ")
		}

		// Try to parse again
		if err := json.Unmarshal([]byte(jsonData), &jsonObj); err != nil {
			return "", fmt.Errorf("invalid JSON data: %v\nReceived data: %s", err, jsonData)
		}
	}

	// Convert to properly formatted JSON
	prettyJSON, err := json.Marshal(jsonObj)
	if err != nil {
		return "", fmt.Errorf("error formatting JSON: %v", err)
	}

	return string(prettyJSON), nil
}

// buildPrompt creates the prompt for the AI
func (g *Generator) buildPrompt(content string, customPrompt string) string {
	basePrompt := `You are an API that generates JSON data structures for radial mindmaps.   
	
	
	Respond ONLY with the raw JSON data, 
no explanations, no JavaScript variable assignments, and no markdown formatting. The response should be a valid JSON object
with the following structure:

{
  "nodes": [
    { "id": "1", "type": "default", "position": { "x": 400, "y": 300 }, "data": { "label": "Central Topic" } }
  ],
  "edges": [
    { "id": "e1-2", "source": "1", "target": "2", "type": "bezier", "style": { "stroke": "#colors" } }
  ],
  "viewport": { "x": 0, "y": 0, "zoom": 1 }
}

Create a radial mind map structure where:
1. The central topic is in the middle (around x: 400, y: 300).
2. Main subtopics branch out radially from the center.
3. Related ideas branch out from their respective subtopics.
4. Arrange nodes to be visually balanced and easy to read, avoiding overlaps.
+   Create an organic, aesthetically pleasing radial layout, ensuring ample space between branches and nodes.
    Ensure node positions allow for smooth, gentle bezier curves between connected nodes.
5. Use curved connection lines (edges) of type 'bezier' for a smooth appearance.
6. Use consistent colors for each main branch and its related ideas:
   - Branch 1: #a1d99b (soft green)
   - Branch 2: #9ecae1 (soft blue)
   - Branch 3: #fdd0a2 (soft orange)
   - Branch 4: #c6dbef (soft light blue)
   - Branch 5: #bcbddc (soft purple)
   - Branch 6: #dadaeb (soft grey-purple)
   (Assign colors sequentially if more than 6 main branches)
7. Use unique IDs following this pattern:
   - Center: '1'
   - Subtopics: '2', '3', '4', etc.
   - Related ideas: 'sub-2-1', 'sub-2-2' (for ideas related to subtopic '2')
8. Ensure adequate spacing between nodes for clarity.
Spread out nodes horizontally and vertically to avoid crowding and allow space for connections.
Ensure node positions allow for smooth, gentle bezier curves between connected nodes.
9. The language output depends on the language input. 
`

	if customPrompt != "" {
		basePrompt = fmt.Sprintf("%s\n\nAdditional instructions: %s", basePrompt, customPrompt)
	}

	return fmt.Sprintf("%s\n\nContent to analyze:\n%s", basePrompt, content)
}
