import { useState } from 'react';
import { RefreshCw, Activity } from 'lucide-react';
import { API_URL } from '@/app/utils/decode_api';

interface ReactivateSubscriptionProps {
  token?: string;
  subscriptionEndDate: string;
  onReactivationComplete?: () => void;
}

export default function ReactivateSubscription({
  token,
  subscriptionEndDate,
  onReactivationComplete = () => {}
}: ReactivateSubscriptionProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleReactivateSubscription = async () => {
    if (!token) return;
    
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_URL}/api/subscription-api/resume`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });
  
      if (!response.ok) throw new Error('Failed to reactivate subscription');
      await response.json();
      setShowSuccess(true);
      // Don't immediately call onReactivationComplete
    } catch (err) {
      console.error('Error reactivating subscription:', err);
      setError('Error reactivating subscription');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center space-y-4">
        <div className="flex items-center justify-center space-x-2 text-white">
          <Activity className="w-5 h-5" />
          <span>Processing reactivation...</span>
        </div>
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="flex flex-col items-center space-y-4">
        <div className="text-green-400 flex items-center justify-center space-x-2">
          <span>Subscription successfully reactivated!</span>
        </div>
        <button
          onClick={onReactivationComplete}
          className="inline-flex items-center space-x-2 bg-purple-500 text-white py-2 px-4 rounded-md hover:bg-purple-600"
        >
          <span>Continue</span>
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="text-yellow-400 flex items-center justify-center space-x-2">
        <span>
          Subscription will end on {new Date(subscriptionEndDate).toLocaleDateString()}
        </span>
      </div>
      {error && <div className="text-red-500">{error}</div>}
      <button
        onClick={handleReactivateSubscription}
        disabled={loading}
        className="inline-flex items-center space-x-2 bg-green-500 text-white py-3 px-6 rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <RefreshCw className="w-5 h-5" />
        <span>Reactivate Subscription</span>
      </button>
    </div>
  );
}
