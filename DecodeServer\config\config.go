package config

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"encoding/base64"
)

// Config holds all configuration for the application
type Config struct {
	// Server settings
	Port int
	Env  string // "development", "production", etc.

	// Database settings
	DatabaseURL string

	// Auth settings
	ClerkSecretKey string
	ClerkJWKSURL   string
	DebugAuth      bool

	// CORS settings
	AllowedOrigins []string

	// AI services
	
	GeminiAPIKey string

	// Google Cloud Storage
	GCSBucketName      string
	GCSProjectID       string
	GCSCredentialsFile string
}

// LoadConfig loads configuration from environment variables
func LoadConfig() (*Config, error) {
	// Load environment variables
	if err := LoadEnvironment(); err != nil {
		return nil, fmt.Errorf("failed to load environment: %w", err)
	}

	config := &Config{
		// Server defaults
		Port: 8001,
		Env:  "development",

		// Empty defaults for required fields (will validate later)
		DatabaseURL:    os.Getenv("DATABASE_URL"),
		ClerkSecretKey: os.Getenv("CLERK_SECRET_KEY"),
		ClerkJWKSURL:   os.Getenv("CLERK_JWKS_URL"),

		// Parse debug flags
		DebugAuth: parseEnvBool("DEBUG_AUTH", false),

		// CORS
		AllowedOrigins: parseAllowedOrigins(os.Getenv("ALLOWED_ORIGINS")),

		// AI services
		
		GeminiAPIKey: os.Getenv("GEMINI_API_KEY"),

		// Google Cloud Storage
		GCSBucketName:      os.Getenv("GCS_BUCKET_NAME"),
		GCSProjectID:       os.Getenv("GCS_PROJECT_ID"),
		GCSCredentialsFile: os.Getenv("GCS_CREDENTIALS_FILE"),
	}

	// Parse port from environment if provided
	if portStr := os.Getenv("PORT"); portStr != "" {
		port, err := strconv.Atoi(portStr)
		if err != nil {
			return nil, fmt.Errorf("invalid PORT: %w", err)
		}
		config.Port = port
	}

	// Use environment variable for application environment if set
	if env := os.Getenv("APP_ENV"); env != "" {
		config.Env = env
	}

	// Validate required configuration
	if err := config.Validate(); err != nil {
		return nil, err
	}

	return config, nil
}

// Validate checks if all required configuration fields are set
func (c *Config) Validate() error {
	if c.DatabaseURL == "" {
		return errors.New("DATABASE_URL is required")
	}

	// In production mode, enforce stricter validation
	if c.Env == "production" {
		if c.ClerkSecretKey == "" {
			return errors.New("CLERK_SECRET_KEY is required in production")
		}

		if c.ClerkJWKSURL == "" {
			return errors.New("CLERK_JWKS_URL is required in production")
		}

		if c.GeminiAPIKey == "" {
			return errors.New("GEMINI_API_KEY is required in production")
		}

		if len(c.AllowedOrigins) == 0 {
			return errors.New("ALLOWED_ORIGINS is required in production")
		}
	}

	// Google Cloud Storage validation (if being used)
	if c.GCSBucketName != "" {
		if c.GCSProjectID == "" {
			return errors.New("GCS_PROJECT_ID is required when GCS_BUCKET_NAME is set")
		}

		if c.GCSCredentialsFile == "" {
			return errors.New("GCS_CREDENTIALS_FILE is required when GCS_BUCKET_NAME is set")
		}

		// Check if credentials are valid
		// If it starts with eyJ or contains base64 characters, it's likely base64 encoded
		if strings.HasPrefix(strings.TrimSpace(c.GCSCredentialsFile), "eyJ") || 
			strings.Contains(c.GCSCredentialsFile, "+") || 
			strings.Contains(c.GCSCredentialsFile, "/") || 
			strings.Contains(c.GCSCredentialsFile, "=") {
			// Try to decode it to validate it's proper base64
			_, err := base64.StdEncoding.DecodeString(c.GCSCredentialsFile)
			if err != nil {
				return fmt.Errorf("GCS credentials contain invalid base64: %v", err)
			}
		} else {
			// Check if credentials file exists
			if _, err := os.Stat(c.GCSCredentialsFile); os.IsNotExist(err) {
				return fmt.Errorf("GCS credentials file not found at %s", c.GCSCredentialsFile)
			}
		}
	}

	return nil
}

// IsDevelopment returns true if running in development mode
func (c *Config) IsDevelopment() bool {
	return c.Env == "development"
}

// IsProduction returns true if running in production mode
func (c *Config) IsProduction() bool {
	return c.Env == "production"
}

// ServerAddress returns the formatted server address with port
func (c *Config) ServerAddress() string {
	return fmt.Sprintf(":%d", c.Port)
}

// Helper function to parse boolean environment variables
func parseEnvBool(key string, defaultVal bool) bool {
	val := os.Getenv(key)
	if val == "" {
		return defaultVal
	}

	boolVal, err := strconv.ParseBool(val)
	if err != nil {
		return defaultVal
	}

	return boolVal
}

// Helper function to parse allowed origins
func parseAllowedOrigins(origins string) []string {
	if origins == "" {
		return []string{}
	}

	return strings.Split(origins, ",")
}

// GetInstance is a singleton getter for the application config
var configInstance *Config

func GetInstance() (*Config, error) {
	if configInstance == nil {
		var err error
		configInstance, err = LoadConfig()
		if err != nil {
			return nil, err
		}
	}

	return configInstance, nil
}
