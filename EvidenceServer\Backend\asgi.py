"""
ASGI config for Backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/asgi/
"""

import os
import django
import logging
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator, OriginValidator
from django.urls import path, re_path
from channels.sessions import SessionMiddlewareStack

# Set up logging
logger = logging.getLogger('django')

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

import chat_api.routing
import search_api.routing
from subscriptions.middleware import AuthenticationMiddleware, SubscriptionMiddleware

# Create Django ASGI application
django_asgi_app = get_asgi_application()

# Create a middleware stack
def create_application():
    # Get list of allowed hosts from settings
    from django.conf import settings
    allowed_hosts = settings.ALLOWED_HOSTS
    logger.info(f"ASGI Initialization - Allowed Hosts: {allowed_hosts}")

    # Log CORS settings
    cors_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
    allow_all = getattr(settings, 'CORS_ORIGIN_ALLOW_ALL', False)
    logger.info(f"CORS Settings - Allow All: {allow_all}, Origins: {cors_origins}")

    # Return more permissive application for troubleshooting
    application = ProtocolTypeRouter({
        "http": SessionMiddlewareStack(
            AuthenticationMiddleware(
                SubscriptionMiddleware(django_asgi_app)
            )
        ),
        "websocket": SessionMiddlewareStack(
            URLRouter(
                chat_api.routing.websocket_urlpatterns +
                search_api.routing.websocket_urlpatterns
            )
        ),
    })
    
    # Log application initialization
    logger.info("ASGI Application initialized successfully")
    return application

# Initialize application
application = create_application()
