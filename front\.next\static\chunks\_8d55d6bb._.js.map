{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/theme/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"./theme-provider\"\n\nexport function ThemeToggle() {\n  const { theme, toggleTheme } = useTheme()\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className=\"p-2 rounded-md transition-colors flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-800\"\n      aria-label=\"Toggle theme\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-5 w-5\" />\n      ) : (\n        <Sun className=\"h-5 w-5\" />\n      )}\n    </button>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;iCAEhB,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;;;;;;AAIvB;GAhBgB;;QACiB,4IAAA,CAAA,WAAQ;;;KADzB", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/providers/language-switcher.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useLanguage } from \"@/app/providers/language-provider\"\n\nimport { useState } from 'react'\nimport { ChevronDown } from 'lucide-react'\nimport type { Locale } from '@/app/utils/types/language'\n\nexport function LanguageSwitcher() {\n  const { language, setLanguage } = useLanguage()\n  const [isOpen, setIsOpen] = useState(false)\n\n  const languages = {\n    en: { name: 'English', flag: '🇺🇸' },\n    es: { name: 'Español', flag: '🇪🇸' },\n    pt: { name: 'Português', flag: '🇧🇷' }\n  }\n\n  const currentLanguage = languages[language as keyof typeof languages]\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center gap-2 text-base font-medium text-gray-300 hover:text-white hover:bg-[#111827] px-3 py-2 rounded-xl transition-all duration-300\"\n        aria-label=\"Change language\"\n      >\n        {currentLanguage.flag} {currentLanguage.name}\n        <ChevronDown className=\"w-4 h-4\" />\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-[#111827] rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50\">\n          {Object.entries(languages).map(([code, { name, flag }]) => (\n            <button\n              key={code}\n              onClick={() => {\n                setLanguage(code as Locale)\n                setIsOpen(false)\n              }}\n              className={`w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 ${\n                language === code ? 'bg-gray-100 dark:bg-gray-700' : ''\n              }`}\n            >\n              {flag} {name}\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAQO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY;QAChB,IAAI;YAAE,MAAM;YAAW,MAAM;QAAO;QACpC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAO;QACpC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAO;IACxC;IAEA,MAAM,kBAAkB,SAAS,CAAC,SAAmC;IAErE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;;oBAEV,gBAAgB,IAAI;oBAAC;oBAAE,gBAAgB,IAAI;kCAC5C,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAGxB,wBACC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,iBACpD,6LAAC;wBAEC,SAAS;4BACP,YAAY;4BACZ,UAAU;wBACZ;wBACA,WAAW,CAAC,qGAAqG,EAC/G,aAAa,OAAO,iCAAiC,IACrD;;4BAED;4BAAK;4BAAE;;uBATH;;;;;;;;;;;;;;;;AAgBnB;GA3CgB;;QACoB,4IAAA,CAAA,cAAW;;;KAD/B", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/header/nav-bar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { usePathname } from 'next/navigation';\nimport { ThemeToggle } from '@/components/theme/theme-toggle';\nimport { useTheme } from '@/components/theme/theme-provider';\nimport { SignInButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs';\nimport { Menu } from 'lucide-react';\nimport { LanguageSwitcher } from '@/app/providers/language-switcher';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface NavBarProps {\n  handleNewChat?: () => void;\n  toggleHistoryVisibility?: () => void;\n  toggleFilterVisibility?: () => void;\n  isLeftTabVisible?: boolean;\n  onShowLeftTab?: () => void;\n}\n\nexport default function NavBar({ \n  isLeftTabVisible = true,\n  onShowLeftTab\n}: NavBarProps) {\n  const pathname = usePathname();\n  const { theme } = useTheme();\n\n  // Profile visibility logic\n  const isAuthPage = pathname.startsWith('/sign-in') || pathname.startsWith('/sign-up');\n  const isFeaturePage = pathname.startsWith('/home/<USER>');\n  const isPricingPage = pathname.startsWith('/home/<USER>');\n  const isContactPage = pathname.startsWith('/home/<USER>');\n  const isAboutPage = pathname.startsWith('/home/<USER>');\n  const isTermsPage = pathname.startsWith('/home/<USER>/terms');\n  const isPrivacyPage = pathname.startsWith('/home/<USER>/privacy');\n\n  const shouldShowProfile = !(\n    pathname === '/' ||\n    pathname === '/home' ||\n    isAuthPage ||\n    isFeaturePage ||\n    isPricingPage ||\n    isContactPage ||\n    isAboutPage ||\n    isTermsPage ||\n    isPrivacyPage\n  );\n\n  // Get the appropriate logo based on theme\n  const getLogoSrc = () => {\n    return theme === \"dark\" ? \"/decodemed_text_dark.svg\" : \"/decodemed_text.svg\";\n  };\n\n  return (\n    <div className={`flex flex-col ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-900'}`}>\n      <header className={`${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'} z-10`}>\n        <div className=\"max-w-full px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-12\">\n            {/* Left side: Menu button and logo when LeftTab is hidden */}\n            <div className=\"flex items-center\">\n              {!isLeftTabVisible && onShowLeftTab && (\n                <>\n                  <button \n                    onClick={onShowLeftTab}\n                    className=\"mr-4 p-1.5 rounded-md focus:outline-none hover:bg-gray-200 dark:hover:bg-gray-700\"\n                    aria-label=\"Show sidebar\"\n                  >\n                    <Menu size={24} />\n                  </button>\n                  <Link href=\"/studio\" className=\"ml-2\">\n                    <Image src={getLogoSrc()} alt=\"DecodeMed\" width={150} height={30} />\n                  </Link>\n                </>\n              )}\n            </div>\n            \n            {/* Center section - empty for now */}\n            <div className=\"flex-1 flex justify-center\">\n            </div>\n            \n            {/* Right side: User menu */}\n            <div className=\"flex items-center space-x-2\">\n              {shouldShowProfile && (\n                <div className=\"ml-auto flex items-center gap-4\">\n                  <LanguageSwitcher />\n                  <ThemeToggle />\n                  <SignedIn>\n                    <UserButton\n                      afterSignOutUrl=\"/\"\n                      appearance={{\n                        elements: {\n                          avatarBox: 'w-9 h-9'\n                        }\n                      }}\n                    />\n                  </SignedIn>\n                  <SignedOut>\n                    <SignInButton mode=\"modal\">\n                      <button className=\"px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors\">\n                        Sign in\n                      </button>\n                    </SignInButton>\n                  </SignedOut>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAoBe,SAAS,OAAO,EAC7B,mBAAmB,IAAI,EACvB,aAAa,EACD;;IACZ,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD;IAEzB,2BAA2B;IAC3B,MAAM,aAAa,SAAS,UAAU,CAAC,eAAe,SAAS,UAAU,CAAC;IAC1E,MAAM,gBAAgB,SAAS,UAAU,CAAC;IAC1C,MAAM,gBAAgB,SAAS,UAAU,CAAC;IAC1C,MAAM,gBAAgB,SAAS,UAAU,CAAC;IAC1C,MAAM,cAAc,SAAS,UAAU,CAAC;IACxC,MAAM,cAAc,SAAS,UAAU,CAAC;IACxC,MAAM,gBAAgB,SAAS,UAAU,CAAC;IAE1C,MAAM,oBAAoB,CAAC,CACzB,aAAa,OACb,aAAa,WACb,cACA,iBACA,iBACA,iBACA,eACA,eACA,aACF;IAEA,0CAA0C;IAC1C,MAAM,aAAa;QACjB,OAAO,UAAU,SAAS,6BAA6B;IACzD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,cAAc,EAAE,UAAU,SAAS,mCAAmC,0BAA0B;kBAC/G,cAAA,6LAAC;YAAO,WAAW,GAAG,UAAU,SAAS,wBAAwB,WAAW,KAAK,CAAC;sBAChF,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,CAAC,oBAAoB,+BACpB;;kDACE,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAEd,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAC7B,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAK;4CAAc,KAAI;4CAAY,OAAO;4CAAK,QAAQ;;;;;;;;;;;;;;;;;;sCAOtE,6LAAC;4BAAI,WAAU;;;;;;sCAIf,6LAAC;4BAAI,WAAU;sCACZ,mCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4IAAA,CAAA,mBAAgB;;;;;kDACjB,6LAAC,0IAAA,CAAA,cAAW;;;;;kDACZ,6LAAC,4KAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,8KAAA,CAAA,aAAU;4CACT,iBAAgB;4CAChB,YAAY;gDACV,UAAU;oDACR,WAAW;gDACb;4CACF;;;;;;;;;;;kDAGJ,6LAAC,4KAAA,CAAA,YAAS;kDACR,cAAA,6LAAC,8KAAA,CAAA,eAAY;4CAAC,MAAK;sDACjB,cAAA,6LAAC;gDAAO,WAAU;0DAAkF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa1H;GA3FwB;;QAIL,qIAAA,CAAA,cAAW;QACV,4IAAA,CAAA,WAAQ;;;KALJ", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/left_tab/LeftTab.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { useTheme } from \"@/components/theme/theme-provider\";\nimport {\n  Menu,\n  LayoutDashboard,\n  Settings,\n  CreditCard,\n  Activity,\n  BookOpen,\n  Search,\n  GraduationCap,\n  Microscope,\n  CodeSquare,\n  DollarSign,\n} from \"lucide-react\";\nimport { useLanguage } from \"@/app/providers/language-provider\";\nimport { usePathname } from \"next/navigation\";\n\ninterface LeftSidebarProps {\n  isExpanded?: boolean;\n  toggleSidebar?: () => void;\n  onHide?: () => void;\n}\n\nexport default function LeftSidebar({\n  isExpanded: expandedProp,\n  toggleSidebar: toggleSidebarProp,\n  onHide,\n}: LeftSidebarProps) {\n  const { t } = useLanguage();\n  const { theme } = useTheme();\n  const pathname = usePathname();\n  const [isExpanded, setIsExpanded] = useState(typeof expandedProp !== 'undefined' ? expandedProp : true);\n  const [showSettings, setShowSettings] = useState(false);\n\n  // Use the prop if provided, otherwise use internal state\n  useEffect(() => {\n    if (typeof expandedProp !== 'undefined') {\n      setIsExpanded(expandedProp);\n    }\n  }, [expandedProp]);\n\n  // Handle hide sidebar - call the onHide callback\n  const handleHide = () => {\n    if (onHide) {\n      onHide();\n    }\n    // If toggleSidebar prop exists, call it too\n    if (toggleSidebarProp) {\n      toggleSidebarProp();\n    } else {\n      setIsExpanded(false);\n    }\n  };\n\n  // Toggle settings visibility\n  const toggleSettings = () => {\n    setShowSettings(!showSettings);\n  };\n\n  // Get the appropriate logo based on theme and sidebar state\n  const getLogo = () => {\n    if (isExpanded) {\n      return theme === \"dark\" ? \"/decodemed_text_dark.svg\" : \"/decodemed_text.svg\";\n    } else {\n      return theme === \"dark\" ? \"/decodemed_dark.svg\" : \"/decodemed.svg\";\n    }\n  };\n\n  return (\n    <div\n      className={`fixed top-0 left-0 h-screen flex-shrink-0 z-30 transition-all duration-300 ${\n        isExpanded ? \"w-64\" : \"w-16\"\n      } ${theme === \"dark\" ? \"bg-[#080808] text-white border-[#121212]\" : \"bg-gray-100 text-neutral-800 border-neutral-200\"}`}\n    >\n      <div className=\"h-full flex flex-col\">\n        <div className=\"flex items-center p-4\">\n          {isExpanded ? (\n            <div className=\"flex items-center justify-between w-full\">\n              <Link href=\"/studio\" className=\"text-xl font-bold\" key=\"dashboard-logo\">\n                <Image src={getLogo()} alt=\"DecodeMed\" width={150} height={30} />\n              </Link>\n              <button\n                onClick={handleHide}\n                className=\"p-1 rounded-md hover:bg-neutral-200 dark:hover:bg-neutral-700 focus:outline-none\"\n              >\n                <Menu />\n              </button>\n            </div>\n          ) : (\n            <div className=\"flex flex-col items-center w-full\">\n              <Link href=\"/studio\" key=\"collapsed-logo\" className=\"mb-2\">\n                <Image src={getLogo()} alt=\"DecodeMed\" width={28} height={28} />\n              </Link>\n              <button\n                onClick={handleHide}\n                className=\"p-1 rounded-md hover:bg-neutral-200 dark:hover:bg-neutral-700 focus:outline-none\"\n                key=\"toggle-sidebar-button\"\n              >\n                <Menu />\n              </button>\n            </div>\n          )}\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto\">\n          <nav className=\"space-y-2 px-2\">\n            {/* Medical School Section */}\n            <div className=\"relative\">\n              <Link\n                href=\"/dashboard\"\n                className={`flex items-center w-full p-2 rounded-md ${\n                  pathname === \"/dashboard\"\n                    ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                    : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                }`}\n                key=\"dashboard-nav\"\n              >\n                <LayoutDashboard size={20} />\n                {isExpanded && (\n                  <div className=\"flex items-center justify-between w-full\">\n                    <span className=\"ml-3\" key=\"dashboard-label\">{t('left_sidebar.dashboard')}</span>\n                  </div>\n                )}\n              </Link>\n              <button\n                className={`flex items-center w-full p-2 rounded-md`}\n                key=\"medical-school-nav\"\n              >\n                <GraduationCap size={20} />\n                {isExpanded && (\n                  <div className=\"flex items-center justify-between w-full\">\n                    <span className=\"ml-3\" key=\"medical-school-label\">{t('left_sidebar.medical_school')}</span>\n                  </div>\n                )}\n              </button>\n              \n              {/* Medical School Dropdown Menu */}\n              {isExpanded && (\n                <div className=\"pl-8 mt-1 space-y-1\">\n                  {/* Decode Studio Link (renamed from Dashboard) */}\n                  <Link\n                    href=\"/studio\"\n                    className={`flex items-center p-2 rounded-md ${\n                      pathname === \"/studio\"\n                        ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                        : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                    }`}\n                    key=\"decode-studio-nav\"\n                  >\n                    <CodeSquare size={16} />\n                    <span className=\"ml-2 text-sm\">{t('left_sidebar.decode_studio')}</span>\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            {/* Medical Research Section */}\n            <div className=\"relative\">\n              <button\n                className={`flex items-center w-full p-2 rounded-md ${\n                  pathname.includes(\"/research\")\n                    ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                    : \"\"\n                }`}\n                key=\"medical-research-nav\"\n              >\n                <Microscope size={20} />\n                {isExpanded && (\n                  <div className=\"flex items-center justify-between w-full\">\n                    <span className=\"ml-3\" key=\"medical-research-label\">{t('left_sidebar.medical_research')}</span>\n                  </div>\n                )}\n              </button>\n              \n              {/* Medical Research Dropdown Menu */}\n              {isExpanded && (\n                <div className=\"pl-8 mt-1 space-y-1\">\n                  <Link\n                    href=\"/chat\"\n                    className={`flex items-center p-2 rounded-md ${\n                      pathname === \"/chat\"\n                        ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                        : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                    }`}\n                    key=\"research-search-nav\"\n                  >\n                    <Search size={16} />\n                    <span className=\"ml-2 text-sm\">{t('left_sidebar.research.search')}</span>\n                  </Link>\n                  <Link\n                    href=\"/evidence\"\n                    className={`flex items-center p-2 rounded-md ${\n                      pathname === \"/evidence\"\n                        ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                        : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                    }`}\n                    key=\"evidence-nav\"\n                  >\n                    <BookOpen size={16} />\n                    <span className=\"ml-2 text-sm\">{t('left_sidebar.research.evidence')}</span>\n                  </Link>\n                </div>\n              )}\n            </div>\n          </nav>\n        </div>\n        \n        {/* Settings section moved to bottom */}\n        <div className=\"mt-auto  border-neutral-200 dark:border-neutral-700 pt-2 px-2 pb-4\">\n          <div className=\"relative\">\n            <button\n              onClick={toggleSettings}\n              className={`flex items-center w-full p-2 rounded-md ${\n                pathname.includes(\"/subscriptions\")\n                  ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                  : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n              }`}\n              key=\"settings-nav\"\n            >\n              <Settings size={20} />\n              {isExpanded && (\n                <div className=\"flex items-center justify-between w-full\">\n                  <span className=\"ml-3\" key=\"settings-label\">{t('left_sidebar.settings')}</span>\n                  <span className=\"text-xs\">\n                    {showSettings ? \"▲\" : \"▼\"}\n                  </span>\n                </div>\n              )}\n            </button>\n            \n            {/* Subscription Dropdown Menu */}\n            {isExpanded && showSettings && (\n              <div className=\"pl-8 mt-1 space-y-1\">\n                <Link\n                  href=\"/subscriptions/subscription-management\"\n                  className={`flex items-center p-2 rounded-md ${\n                    pathname === \"/subscriptions/subscription-management\"\n                      ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                      : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                  }`}\n                  key=\"subscription-management-nav\"\n                >\n                  <CreditCard size={16} />\n                  <span className=\"ml-2 text-sm\">{t('left_sidebar.subscription_management')}</span>\n                </Link>\n                <Link\n                  href=\"/subscriptions/pricing\"\n                  className={`flex items-center p-2 rounded-md ${\n                    pathname === \"/subscriptions/pricing\"\n                      ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                      : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                  }`}\n                  key=\"pricing-nav\"\n                >\n                  <DollarSign size={16} />\n                  <span className=\"ml-2 text-sm\">{t('left_sidebar.pricing')}</span>\n                </Link>\n                <Link\n                  href=\"/subscriptions/usage\"\n                  className={`flex items-center p-2 rounded-md ${\n                    pathname === \"/subscriptions/usage\"\n                      ? \"bg-neutral-200 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200\"\n                      : \"hover:bg-neutral-100 dark:hover:bg-neutral-700\"\n                  }`}\n                  key=\"usage-nav\"\n                >\n                  <Activity size={16} />\n                  <span className=\"ml-2 text-sm\">{t('left_sidebar.usage')}</span>\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AApBA;;;;;;;;AA4Be,SAAS,YAAY,EAClC,YAAY,YAAY,EACxB,eAAe,iBAAiB,EAChC,MAAM,EACW;;IACjB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,iBAAiB,cAAc,eAAe;IAClG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,OAAO,iBAAiB,aAAa;gBACvC,cAAc;YAChB;QACF;gCAAG;QAAC;KAAa;IAEjB,iDAAiD;IACjD,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV;QACF;QACA,4CAA4C;QAC5C,IAAI,mBAAmB;YACrB;QACF,OAAO;YACL,cAAc;QAChB;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;IACnB;IAEA,4DAA4D;IAC5D,MAAM,UAAU;QACd,IAAI,YAAY;YACd,OAAO,UAAU,SAAS,6BAA6B;QACzD,OAAO;YACL,OAAO,UAAU,SAAS,wBAAwB;QACpD;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,2EAA2E,EACrF,aAAa,SAAS,OACvB,CAAC,EAAE,UAAU,SAAS,6CAA6C,mDAAmD;kBAEvH,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAC7B,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK;oCAAW,KAAI;oCAAY,OAAO;oCAAK,QAAQ;;;;;;+BADN;;;;;0CAGvD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;;;;;;6CAIT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA+B,WAAU;0CAClD,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK;oCAAW,KAAI;oCAAY,OAAO;oCAAI,QAAQ;;;;;;+BADnC;;;;;0CAGzB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAGV,cAAA,6LAAC,qMAAA,CAAA,OAAI;;;;;+BAFD;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,wCAAwC,EAClD,aAAa,eACT,8EACA,kDACJ;;0DAGF,6LAAC,+NAAA,CAAA,kBAAe;gDAAC,MAAM;;;;;;4CACtB,4BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA8B,EAAE;mDAArB;;;;;;;;;;;uCAL3B;;;;;kDASN,6LAAC;wCACC,WAAW,CAAC,uCAAuC,CAAC;;0DAGpD,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,MAAM;;;;;;4CACpB,4BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAmC,EAAE;mDAA1B;;;;;;;;;;;uCAL3B;;;;;oCAWL,4BACC,6LAAC;wCAAI,WAAU;kDAEb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAW,CAAC,iCAAiC,EAC3C,aAAa,YACT,8EACA,kDACJ;;8DAGF,6LAAC,qNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAgB,EAAE;;;;;;;2CAH9B;;;;;;;;;;;;;;;;0CAUZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,wCAAwC,EAClD,SAAS,QAAQ,CAAC,eACd,8EACA,IACJ;;0DAGF,6LAAC,iNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;4CACjB,4BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAqC,EAAE;mDAA5B;;;;;;;;;;;uCAL3B;;;;;oCAWL,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,iCAAiC,EAC3C,aAAa,UACT,8EACA,kDACJ;;kEAGF,6LAAC,yMAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;kEACd,6LAAC;wDAAK,WAAU;kEAAgB,EAAE;;;;;;;+CAH9B;;;;;0DAKN,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,iCAAiC,EAC3C,aAAa,cACT,8EACA,kDACJ;;kEAGF,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAgB,EAAE;;;;;;;+CAH9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAYhB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAW,CAAC,wCAAwC,EAClD,SAAS,QAAQ,CAAC,oBACd,8EACA,kDACJ;;kDAGF,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACf,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA6B,EAAE;+CAApB;;;;;0DAC3B,6LAAC;gDAAK,WAAU;0DACb,eAAe,MAAM;;;;;;;;;;;;;+BAPxB;;;;;4BAcL,cAAc,8BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,iCAAiC,EAC3C,aAAa,2CACT,8EACA,kDACJ;;0DAGF,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAgB,EAAE;;;;;;;uCAH9B;;;;;kDAKN,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,iCAAiC,EAC3C,aAAa,2BACT,8EACA,kDACJ;;0DAGF,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAgB,EAAE;;;;;;;uCAH9B;;;;;kDAKN,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,iCAAiC,EAC3C,aAAa,yBACT,8EACA,kDACJ;;0DAGF,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAgB,EAAE;;;;;;;uCAH9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYtB;GA7PwB;;QAKR,4IAAA,CAAA,cAAW;QACP,4IAAA,CAAA,WAAQ;QACT,qIAAA,CAAA,cAAW;;;KAPN", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/utils/stripe.ts"], "sourcesContent": ["import { loadStripe, Stripe } from '@stripe/stripe-js';\n\n// Singleton pattern for Stripe instance\nlet stripePromise: Promise<Stripe | null>;\n\n/**\n * Gets a Stripe instance to use for client-side operations\n * Using a singleton pattern to avoid loading <PERSON><PERSON> multiple times\n */\nexport const getStripe = () => {\n  if (!stripePromise) {\n    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);\n  }\n  return stripePromise;\n};\n\n/**\n * Subscription types supported by the application\n */\nexport type SubscriptionType = 'monthly' | 'yearly' | 'none' | 'unknown';\n\n/**\n * Subscription status values from Stripe\n */\nexport type SubscriptionStatus = \n  | 'active' \n  | 'trialing' \n  | 'incomplete' \n  | 'incomplete_expired' \n  | 'past_due' \n  | 'canceled' \n  | 'unpaid' \n  | 'none';\n\n/**\n * Subscription details returned from the API\n */\nexport interface SubscriptionDetails {\n  id: string;\n  status: SubscriptionStatus;\n  current_period_end: string;\n  plan_type: SubscriptionType;\n  cancel_at_period_end: boolean;\n}\n\n/**\n * Subscription data returned from the API\n */\nexport interface SubscriptionData {\n  status: SubscriptionStatus;\n  details: SubscriptionDetails | null;\n  free_projects_count?: number;\n  free_projects_limit?: number;\n  free_projects_remaining?: number;\n}\n\n/**\n * Usage statistics for the subscription\n */\nexport interface UsageStats {\n  subscription_type: SubscriptionType;\n  status: string;\n  current_period_end: string | null;\n  will_cancel: boolean;\n  created_at: string;\n  updated_at: string;\n  search: { \n    usage: number; \n    limit: number \n  };\n  chat: { \n    usage: number; \n    limit: number \n  };\n}\n\n/**\n * Format a subscription status for display\n */\nexport const formatSubscriptionStatus = (status: string): string => {\n  switch (status) {\n    case 'active':\n      return 'Active';\n    case 'trialing':\n      return 'Trial';\n    case 'past_due':\n      return 'Past Due';\n    case 'canceled':\n      return 'Cancelled';\n    case 'unpaid':\n      return 'Unpaid';\n    case 'incomplete':\n      return 'Incomplete';\n    case 'incomplete_expired':\n      return 'Expired';\n    default:\n      return 'Free Trial';\n  }\n};\n\n/**\n * Format a subscription type for display\n */\nexport const formatSubscriptionType = (\n  type: SubscriptionType,\n): string => {\n  if (type === 'monthly') return 'Monthly';\n  if (type === 'yearly') return 'Yearly';\n  // If type is 'none', 'unknown', or any other value, it defaults to Free Trial.\n  // The detailed status of the subscription (active, trialing, etc.) \n  // is handled by formatSubscriptionStatus.\n  return 'Free Trial';\n};\n"], "names": [], "mappings": ";;;;;AAW+B;AAX/B;AAAA;;AAEA,wCAAwC;AACxC,IAAI;AAMG,MAAM,YAAY;IACvB,IAAI,CAAC,eAAe;QAClB,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD;IAC3B;IACA,OAAO;AACT;AAiEO,MAAM,2BAA2B,CAAC;IACvC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,MAAM,yBAAyB,CACpC;IAEA,IAAI,SAAS,WAAW,OAAO;IAC/B,IAAI,SAAS,UAAU,OAAO;IAC9B,+EAA+E;IAC/E,oEAAoE;IACpE,0CAA0C;IAC1C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/utils/decode_api.ts"], "sourcesContent": ["// utils/config.ts\r\n\r\n\r\nexport const API_URL = process.env.NEXT_PUBLIC_DECODE_API_URL;\r\n\r\n\r\nexport const getWebSocketUrl = (endpoint: string) => {\r\n    const isProduction = process.env.ENVIRONMENT_DECODE === 'production';\r\n    const apiUrl = process.env.NEXT_PUBLIC_DECODE_API_URL || '';\r\n    \r\n    // Remove http(s):// from API URL if present\r\n    const domain = apiUrl.replace(/^https?:\\/\\//, '');\r\n    \r\n    // Use wss for production/https, ws for development/http\r\n    const protocol = isProduction ? 'wss' : 'ws';\r\n    \r\n    return `${protocol}://${domain}${endpoint}`;\r\n  };\r\n "], "names": [], "mappings": "AAAA,kBAAkB;;;;;AAGK;AAAhB,MAAM;AAGN,MAAM,kBAAkB,CAAC;IAC5B,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK;IACxD,MAAM,SAAS,6DAA0C;IAEzD,4CAA4C;IAC5C,MAAM,SAAS,OAAO,OAAO,CAAC,gBAAgB;IAE9C,wDAAwD;IACxD,MAAM,WAAW,eAAe,QAAQ;IAExC,OAAO,GAAG,SAAS,GAAG,EAAE,SAAS,UAAU;AAC7C", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/utils/subscription-api.ts"], "sourcesContent": ["import { SubscriptionData, SubscriptionDetails, SubscriptionType, UsageStats } from './stripe';\nimport { API_URL } from './decode_api';\n\n/**\n * Makes an authenticated API request\n */\nexport const authenticatedFetch = async (\n  url: string, \n  getToken: () => Promise<string | null>,\n  options: RequestInit = {}\n) => {\n  // Get a fresh token for each request\n  const token = await getToken();\n  \n  if (!token) {\n    throw new Error('No authentication token available');\n  }\n  \n  // Create headers with authentication\n  const headers = {\n    ...options.headers,\n    'Authorization': `Bearer ${token}`,\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  };\n  \n  // Make the request\n  const response = await fetch(url, {\n    ...options,\n    headers,\n    credentials: 'include'\n  });\n  \n  // Handle common error responses\n  if (!response.ok) {\n    if (response.status === 401) {\n      throw new Error('Your session has expired. Please sign in again.');\n    }\n    \n    try {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Request failed with status ${response.status}`);\n    } catch (e) {\n      if (e instanceof Error && e.message.includes('JSON')) {\n        throw new Error(`Request failed with status ${response.status}`);\n      }\n      throw e;\n    }\n  }\n  \n  return response;\n};\n\n/**\n * Creates a checkout session for subscription purchase\n */\nexport const createCheckoutSession = async (\n  getToken: () => Promise<string | null>,\n  subscriptionType: SubscriptionType,\n): Promise<{ sessionId: string }> => {\n  const response = await authenticatedFetch(\n    `${API_URL}/api/subscription-api/create-checkout-session`,\n    getToken,\n    {\n      method: 'POST',\n      body: JSON.stringify({\n        subscriptionType,\n        successUrl: `${window.location.origin}/subscriptions/success`,\n        cancelUrl: `${window.location.origin}/subscriptions/pricing`,\n      }),\n    }\n  );\n  \n  return response.json();\n};\n\n/**\n * Fetches the current subscription status\n */\nexport const getSubscriptionStatus = async (\n  getToken: () => Promise<string | null>\n): Promise<SubscriptionData> => {\n  const response = await authenticatedFetch(\n    `${API_URL}/api/subscription-api/status`,\n    getToken,\n    { method: 'GET' }\n  );\n  \n  return response.json();\n};\n\n/**\n * Maps subscription data from API to frontend usage format\n */\nexport const mapSubscriptionToUsage = (\n  data: SubscriptionData\n): UsageStats => {\n  const freeProjectsCount = data.free_projects_count || 0;\n  const freeProjectsLimit = data.free_projects_limit || 10;\n  const isPremium = data.status === 'active' || data.status === 'trialing';\n\n  const usageStats: UsageStats = {\n    subscription_type: data.details?.plan_type === 'unknown' ? 'none' : (data.details?.plan_type || 'none'),\n    status: data.status,\n    current_period_end: data.details?.current_period_end || null,\n    will_cancel: data.details?.cancel_at_period_end || false,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    search: { \n      usage: isPremium ? 0 : freeProjectsCount,\n      limit: isPremium ? -1 : freeProjectsLimit\n    },\n    chat: { \n      usage: freeProjectsCount,\n      limit: freeProjectsLimit\n    }\n  };\n  return usageStats;\n};\n\n/**\n * Cancels a subscription\n */\nexport const cancelSubscription = async (\n  getToken: () => Promise<string | null>\n): Promise<{ success: boolean; message: string }> => {\n  const response = await authenticatedFetch(\n    `${API_URL}/api/subscription-api/cancel`,\n    getToken,\n    { method: 'POST' }\n  );\n  \n  return response.json();\n};\n\n/**\n * Resumes a canceled subscription\n */\nexport const resumeSubscription = async (\n  getToken: () => Promise<string | null>\n): Promise<{ success: boolean; message: string }> => {\n  const response = await authenticatedFetch(\n    `${API_URL}/api/subscription-api/resume`,\n    getToken,\n    { method: 'POST' }\n  );\n  \n  return response.json();\n};\n\n/**\n * Updates a subscription to a different plan\n */\nexport const updateSubscription = async (\n  getToken: () => Promise<string | null>,\n  subscriptionType: SubscriptionType\n): Promise<{ success: boolean; message: string; subscription: SubscriptionDetails }> => {\n  const response = await authenticatedFetch(\n    `${API_URL}/api/subscription-api/update`,\n    getToken,\n    {\n      method: 'POST',\n      body: JSON.stringify({ subscriptionType }),\n    }\n  );\n  \n  return response.json();\n};\n\n/**\n * Creates a Stripe Customer Portal session\n */\nexport const createCustomerPortalSession = async (\n  getToken: () => Promise<string | null>,\n  returnUrl?: string\n): Promise<{ url: string }> => {\n  const response = await authenticatedFetch(\n    `${API_URL}/api/subscription-api/create-portal-session`,\n    getToken,\n    {\n      method: 'POST',\n      body: JSON.stringify({\n        returnUrl: returnUrl || `${window.location.origin}/subscriptions/subscription-management`,\n      }),\n    }\n  );\n  \n  return response.json();\n}; "], "names": [], "mappings": ";;;;;;;;;;AACA;;AAKO,MAAM,qBAAqB,OAChC,KACA,UACA,UAAuB,CAAC,CAAC;IAEzB,qCAAqC;IACrC,MAAM,QAAQ,MAAM;IAEpB,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,qCAAqC;IACrC,MAAM,UAAU;QACd,GAAG,QAAQ,OAAO;QAClB,iBAAiB,CAAC,OAAO,EAAE,OAAO;QAClC,gBAAgB;QAChB,UAAU;IACZ;IAEA,mBAAmB;IACnB,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,GAAG,OAAO;QACV;QACA,aAAa;IACf;IAEA,gCAAgC;IAChC,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;QACpF,EAAE,OAAO,GAAG;YACV,IAAI,aAAa,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,SAAS;gBACpD,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;YACjE;YACA,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAKO,MAAM,wBAAwB,OACnC,UACA;IAEA,MAAM,WAAW,MAAM,mBACrB,GAAG,6HAAA,CAAA,UAAO,CAAC,6CAA6C,CAAC,EACzD,UACA;QACE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB;YACA,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,sBAAsB,CAAC;YAC7D,WAAW,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAC9D;IACF;IAGF,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,wBAAwB,OACnC;IAEA,MAAM,WAAW,MAAM,mBACrB,GAAG,6HAAA,CAAA,UAAO,CAAC,4BAA4B,CAAC,EACxC,UACA;QAAE,QAAQ;IAAM;IAGlB,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,yBAAyB,CACpC;IAEA,MAAM,oBAAoB,KAAK,mBAAmB,IAAI;IACtD,MAAM,oBAAoB,KAAK,mBAAmB,IAAI;IACtD,MAAM,YAAY,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK;IAE9D,MAAM,aAAyB;QAC7B,mBAAmB,KAAK,OAAO,EAAE,cAAc,YAAY,SAAU,KAAK,OAAO,EAAE,aAAa;QAChG,QAAQ,KAAK,MAAM;QACnB,oBAAoB,KAAK,OAAO,EAAE,sBAAsB;QACxD,aAAa,KAAK,OAAO,EAAE,wBAAwB;QACnD,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,QAAQ;YACN,OAAO,YAAY,IAAI;YACvB,OAAO,YAAY,CAAC,IAAI;QAC1B;QACA,MAAM;YACJ,OAAO;YACP,OAAO;QACT;IACF;IACA,OAAO;AACT;AAKO,MAAM,qBAAqB,OAChC;IAEA,MAAM,WAAW,MAAM,mBACrB,GAAG,6HAAA,CAAA,UAAO,CAAC,4BAA4B,CAAC,EACxC,UACA;QAAE,QAAQ;IAAO;IAGnB,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,qBAAqB,OAChC;IAEA,MAAM,WAAW,MAAM,mBACrB,GAAG,6HAAA,CAAA,UAAO,CAAC,4BAA4B,CAAC,EACxC,UACA;QAAE,QAAQ;IAAO;IAGnB,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,qBAAqB,OAChC,UACA;IAEA,MAAM,WAAW,MAAM,mBACrB,GAAG,6HAAA,CAAA,UAAO,CAAC,4BAA4B,CAAC,EACxC,UACA;QACE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YAAE;QAAiB;IAC1C;IAGF,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,8BAA8B,OACzC,UACA;IAEA,MAAM,WAAW,MAAM,mBACrB,GAAG,6HAAA,CAAA,UAAO,CAAC,2CAA2C,CAAC,EACvD,UACA;QACE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,WAAW,aAAa,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,sCAAsC,CAAC;QAC3F;IACF;IAGF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/subscriptions/components/CheckoutForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, ReactNode } from 'react';\r\nimport { useAuth } from '@clerk/nextjs';\r\nimport { Loader2, AlertCircle } from 'lucide-react';\r\nimport { getStripe, SubscriptionType } from '@/app/utils/stripe';\r\nimport { createCheckoutSession } from '@/app/utils/subscription-api';\r\n\r\ninterface CheckoutFormProps {\r\n  subscriptionType: SubscriptionType;\r\n  buttonText?: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport default function CheckoutForm({\r\n  subscriptionType,\r\n  buttonText = 'Subscribe',\r\n  className = '',\r\n}: CheckoutFormProps) {\r\n  const { getToken, isLoaded, isSignedIn } = useAuth();\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Handle Stripe redirection\r\n  const handleCheckout = async () => {\r\n    if (!isLoaded || !isSignedIn) {\r\n      setError('Please sign in to subscribe');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      // Create checkout session via the API\r\n      const { sessionId } = await createCheckoutSession(getToken, subscriptionType);\r\n\r\n      // Redirect to Stripe Checkout\r\n      const stripe = await getStripe();\r\n      if (!stripe) {\r\n        throw new Error('Stripe failed to initialize');\r\n      }\r\n\r\n      const { error: stripeError } = await stripe.redirectToCheckout({ sessionId });\r\n      \r\n      if (stripeError) {\r\n        throw stripeError;\r\n      }\r\n    } catch (error) {\r\n      console.error('Checkout error:', error);\r\n      setError(error instanceof Error ? error.message : 'An unexpected error occurred');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {error && (\r\n        <div className=\"mb-4 p-3 bg-red-50 text-red-600 rounded-md flex items-center\">\r\n          <AlertCircle className=\"w-5 h-5 mr-2 flex-shrink-0\" />\r\n          <span>{error}</span>\r\n        </div>\r\n      )}\r\n      \r\n      <button\r\n        onClick={handleCheckout}\r\n        disabled={loading || !isLoaded || !isSignedIn}\r\n        className={`w-full py-2 px-4 rounded-md flex items-center justify-center transition-colors ${\r\n          loading || !isLoaded || !isSignedIn\r\n            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n            : 'bg-blue-600 text-white hover:bg-blue-700'\r\n        } ${className}`}\r\n      >\r\n        {loading ? (\r\n          <>\r\n            <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\r\n            Processing...\r\n          </>\r\n        ) : (\r\n          buttonText\r\n        )}\r\n      </button>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAce,SAAS,aAAa,EACnC,gBAAgB,EAChB,aAAa,WAAW,EACxB,YAAY,EAAE,EACI;;IAClB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,4BAA4B;IAC5B,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY,CAAC,YAAY;YAC5B,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,sCAAsC;YACtC,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;YAE5D,8BAA8B;YAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;YAC7B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,OAAO,kBAAkB,CAAC;gBAAE;YAAU;YAE3E,IAAI,aAAa;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;kCAAM;;;;;;;;;;;;0BAIX,6LAAC;gBACC,SAAS;gBACT,UAAU,WAAW,CAAC,YAAY,CAAC;gBACnC,WAAW,CAAC,+EAA+E,EACzF,WAAW,CAAC,YAAY,CAAC,aACrB,iDACA,2CACL,CAAC,EAAE,WAAW;0BAEd,wBACC;;sCACE,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;mCAInD;;;;;;;;;;;;AAKV;GAvEwB;;QAKqB,qPAAA,CAAA,UAAO;;;KAL5B", "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/1.%20DECODEMED/front/app/subscriptions/pricing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>rk<PERSON>, AlertCircle, Check } from 'lucide-react';\nimport NavBar from '@/components/header/nav-bar';\nimport LeftSidebar from '@/components/left_tab/LeftTab';\nimport { useTheme } from '@/components/theme/theme-provider';\nimport CheckoutForm from '@/app/subscriptions/components/CheckoutForm';\nimport { SubscriptionType } from '@/app/utils/stripe';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useLanguage } from '@/app/providers/language-provider';\n\n\nexport default function PricingPage() {\n  const { theme } = useTheme();\n  const { t } = useLanguage();\n  const [error] = useState('');\n  const [billingCycle, setBillingCycle] = useState<SubscriptionType>('monthly');\n  const [isLeftTabVisible, setIsLeftTabVisible] = useState(true);\n\n  return (\n    <>\n      <div className=\"flex flex-col min-h-screen\">\n        {isLeftTabVisible && (\n          <LeftSidebar \n            onHide={() => setIsLeftTabVisible(false)}\n          />\n        )}\n        <div className=\"fixed top-0 left-0 right-0 z-10\">\n          <NavBar \n            isLeftTabVisible={isLeftTabVisible} \n            onShowLeftTab={() => setIsLeftTabVisible(true)} \n          />\n        </div>\n        \n        <div className={`flex flex-1 pt-12`}>\n          <main className={`flex-1 transition-all duration-300 ${isLeftTabVisible ? 'ml-64' : 'ml-0'}`}>\n            <div className={`min-h-screen ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>\n              <section className=\"py-16 md:py-32\">\n                <div className=\"mx-auto max-w-6xl px-6\">\n                  {/* Pricing Header */}\n                  <div className=\"text-center\">\n                    <h1 className=\"text-4xl font-bold tracking-tight sm:text-5xl\">\n                      {t('pricing.title')}\n                    </h1>\n                    <p className=\"mt-4 text-lg text-muted-foreground max-w-3xl mx-auto\">\n                      {t('pricing.subtitle')}\n                    </p>\n                  </div>\n\n                  {/* Billing Toggle */}\n                  <div className=\"mt-8 flex justify-center\">\n                    <div className=\"relative flex bg-muted rounded-lg p-1\">\n                      <button\n                        onClick={() => setBillingCycle('monthly')}\n                        className={`relative px-4 py-2 text-sm font-medium rounded-md transition-all ${\n                          billingCycle === 'monthly'\n                            ? 'bg-background text-foreground shadow-sm'\n                            : 'text-muted-foreground hover:text-foreground'\n                        }`}\n                      >\n                        {t('pricing.paidMonthly')}\n                      </button>\n                      <button\n                        onClick={() => setBillingCycle('yearly')}\n                        className={`relative px-4 py-2 text-sm font-medium rounded-md transition-all ${\n                          billingCycle === 'yearly'\n                            ? 'bg-background text-foreground shadow-sm'\n                            : 'text-muted-foreground hover:text-foreground'\n                        }`}\n                      >\n                        {t('pricing.paidYearly')}\n                        <span className=\"ml-2 inline-flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300\">\n                          Save 33%\n                        </span>\n                      </button>\n                    </div>\n                  </div>\n\n                  {error && (\n                    <div className=\"mt-4 max-w-md mx-auto p-3 bg-red-50 text-red-600 rounded-md flex items-center\">\n                      <AlertCircle className=\"w-5 h-5 mr-2 flex-shrink-0\" />\n                      <span>{error}</span>\n                    </div>\n                  )}\n\n\n                  <div className=\"mt-8 grid gap-6 [--color-card:var(--color-muted)] *:border-none *:shadow-none md:mt-20 md:grid-cols-2 dark:[--color-muted:var(--color-zinc-900)]\">\n                    <Card className=\"flex flex-col\">\n                      <CardHeader>\n                        <CardTitle className=\"font-medium\">{t('pricing.freePlanName')}</CardTitle>\n                        <span className=\"my-3 block text-2xl font-semibold\">$0 / mo</span>\n                        <CardDescription className=\"text-sm\">{t('pricing.freePlanDesc')}</CardDescription>\n                      </CardHeader>\n\n                      <CardContent className=\"space-y-4\">\n                        <hr className=\"border-dashed\" />\n\n                        <ul className=\"list-outside space-y-3 text-sm\">\n                          {[t('pricing.freeFeature1'), t('pricing.freeFeature3')].map((item, index) => (\n                            <li key={index} className=\"flex items-center gap-2\">\n                              <Check className=\"size-3\" />\n                              {item}\n                            </li>\n                          ))}\n                        </ul>\n                      </CardContent>\n\n                     \n                    </Card>\n\n                    <Card className=\"relative\">\n                      <span className=\"absolute inset-x-0 -top-3 mx-auto flex h-6 w-fit items-center rounded-full from-neutral-400 to-neutral-300 bg-gradient-to-br px-3 py-1 text-xs font-medium text-neutral-950 ring-1 ring-inset ring-white/20 ring-offset-1 ring-offset-neutral-950/5\">{t('pricing.popular')}</span>\n\n                      <div className=\"flex flex-col\">\n                        <CardHeader>\n                          <CardTitle className=\"font-medium\">{t('pricing.proPlanName')}</CardTitle>\n                          <div className=\"my-3\">\n                            <span className=\"block text-2xl font-semibold\">\n                              ${billingCycle === 'yearly' ? '10' : '15'} / mo\n                            </span>\n                            {billingCycle === 'yearly' && (\n                              <span className=\"text-sm text-muted-foreground line-through\">\n                                $15 / mo\n                              </span>\n                            )}\n                          </div>\n                          <CardDescription className=\"text-sm\">\n                            {billingCycle === 'yearly' ? t('pricing.proPlanDescAnnual') : t('pricing.proPlanDescMonthly')}\n                          </CardDescription>\n                        </CardHeader>\n\n                        <CardContent className=\"space-y-4\">\n                          <hr className=\"border-dashed\" />\n                          <ul className=\"list-outside space-y-3 text-sm\">\n                            {[\n                            t('pricing.proFeature1'),\n                            t('pricing.proFeature2'),\n                            t('pricing.proFeature3'),\n                            t('pricing.proFeature4'),\n                            t('pricing.proFeature5')\n                          ].map((item, index) => (\n                              <li key={index} className=\"flex items-center gap-2\">\n                                <Check className=\"size-3\" />\n                                {item}\n                              </li>\n                            ))}\n                          </ul>\n                        </CardContent>\n\n                        <CardFooter>\n                          <CheckoutForm\n                            subscriptionType={billingCycle}\n                            buttonText={\n                              <div className=\"flex items-center space-x-2\">\n                                <Sparkles className=\"w-5 h-5\" />\n                                <span>\n                                  {billingCycle === 'yearly' ? t('pricing.subscribeYearly') : t('pricing.subscribeMonthly')}\n                                </span>\n                              </div>\n                            }\n                            className=\"w-full bg-neutral-900 hover:bg-neutral-800 text-white dark:bg-neutral-800 dark:hover:bg-neutral-700\"\n                          />\n                        </CardFooter>\n                      </div>\n                    </Card>\n                  </div>\n                </div>\n              </section>\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAVA;;;;;;;;;AAae,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;gBACZ,kCACC,6LAAC,qIAAA,CAAA,UAAW;oBACV,QAAQ,IAAM,oBAAoB;;;;;;8BAGtC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,sIAAA,CAAA,UAAM;wBACL,kBAAkB;wBAClB,eAAe,IAAM,oBAAoB;;;;;;;;;;;8BAI7C,6LAAC;oBAAI,WAAW,CAAC,iBAAiB,CAAC;8BACjC,cAAA,6LAAC;wBAAK,WAAW,CAAC,mCAAmC,EAAE,mBAAmB,UAAU,QAAQ;kCAC1F,cAAA,6LAAC;4BAAI,WAAW,CAAC,aAAa,EAAE,UAAU,SAAS,wBAAwB,YAAY;sCACrF,cAAA,6LAAC;gCAAQ,WAAU;0CACjB,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,EAAE;;;;;;8DAEL,6LAAC;oDAAE,WAAU;8DACV,EAAE;;;;;;;;;;;;sDAKP,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAW,CAAC,iEAAiE,EAC3E,iBAAiB,YACb,4CACA,+CACJ;kEAED,EAAE;;;;;;kEAEL,6LAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAW,CAAC,iEAAiE,EAC3E,iBAAiB,WACb,4CACA,+CACJ;;4DAED,EAAE;0EACH,6LAAC;gEAAK,WAAU;0EAA+I;;;;;;;;;;;;;;;;;;;;;;;wCAOpK,uBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;8DAAM;;;;;;;;;;;;sDAKX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4HAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,6LAAC,4HAAA,CAAA,aAAU;;8EACT,6LAAC,4HAAA,CAAA,YAAS;oEAAC,WAAU;8EAAe,EAAE;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,6LAAC,4HAAA,CAAA,kBAAe;oEAAC,WAAU;8EAAW,EAAE;;;;;;;;;;;;sEAG1C,6LAAC,4HAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6LAAC;oEAAG,WAAU;;;;;;8EAEd,6LAAC;oEAAG,WAAU;8EACX;wEAAC,EAAE;wEAAyB,EAAE;qEAAwB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjE,6LAAC;4EAAe,WAAU;;8FACxB,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB;;2EAFM;;;;;;;;;;;;;;;;;;;;;;8DAWjB,6LAAC,4HAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,6LAAC;4DAAK,WAAU;sEAAuP,EAAE;;;;;;sEAEzQ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,4HAAA,CAAA,aAAU;;sFACT,6LAAC,4HAAA,CAAA,YAAS;4EAAC,WAAU;sFAAe,EAAE;;;;;;sFACtC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;;wFAA+B;wFAC3C,iBAAiB,WAAW,OAAO;wFAAK;;;;;;;gFAE3C,iBAAiB,0BAChB,6LAAC;oFAAK,WAAU;8FAA6C;;;;;;;;;;;;sFAKjE,6LAAC,4HAAA,CAAA,kBAAe;4EAAC,WAAU;sFACxB,iBAAiB,WAAW,EAAE,+BAA+B,EAAE;;;;;;;;;;;;8EAIpE,6LAAC,4HAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,6LAAC;4EAAG,WAAU;;;;;;sFACd,6LAAC;4EAAG,WAAU;sFACX;gFACD,EAAE;gFACF,EAAE;gFACF,EAAE;gFACF,EAAE;gFACF,EAAE;6EACH,CAAC,GAAG,CAAC,CAAC,MAAM,sBACT,6LAAC;oFAAe,WAAU;;sGACxB,6LAAC,uMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;wFAChB;;mFAFM;;;;;;;;;;;;;;;;8EAQf,6LAAC,4HAAA,CAAA,aAAU;8EACT,cAAA,6LAAC,sJAAA,CAAA,UAAY;wEACX,kBAAkB;wEAClB,0BACE,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,6LAAC;8FACE,iBAAiB,WAAW,EAAE,6BAA6B,EAAE;;;;;;;;;;;;wEAIpE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AActC;GAlKwB;;QACJ,4IAAA,CAAA,WAAQ;QACZ,4IAAA,CAAA,cAAW;;;KAFH", "debugId": null}}]}