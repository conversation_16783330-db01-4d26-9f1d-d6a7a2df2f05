package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"decodemed/ai/mindmap"
	"decodemed/api/handlers/async"
	"decodemed/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GenerateMindmapRequest represents the request body for generating a mindmap
type GenerateMindmapRequest struct {
	ProjectID uint   `json:"project_id" binding:"required"`
	UnitID    uint   `json:"unit_id" binding:"required"`
	Prompt    string `json:"prompt"`
}

// HandleAsyncMindmapGeneration initiates async mindmap generation
func HandleAsyncMindmapGeneration(db *gorm.DB) gin.HandlerFunc {
	// Create mindmap generator
	generator, err := mindmap.NewGenerator()
	if err != nil {
		return func(c *gin.Context) {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize mindmap generator"})
		}
	}

	return func(c *gin.Context) {
		var req GenerateMindmapRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		userID, _ := c.Get("user_id")

		// Verify project belongs to user
		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", req.ProjectID, userID).
			Preload("Units").First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Verify unit belongs to project
		var unit models.Unit
		if err := db.Where("id = ? AND project_id = ?", req.UnitID, req.ProjectID).
			First(&unit).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found"})
			return
		}

		// Create job ID and initial status
		jobID := uuid.New().String()
		jobStatus := &async.JobStatus{
			Status:    "pending",
			Progress:  0,
			CreatedAt: time.Now(),
		}

		async.JobMutex.Lock()
		async.JobStatuses[jobID] = jobStatus
		async.JobMutex.Unlock()

		// Start async processing
		go func() {
			defer func() {
				async.JobMutex.Lock()
				delete(async.JobStatuses, jobID)
				async.JobMutex.Unlock()
			}()

			jobStatus.Status = "processing"

			// Delete existing mindmap if it exists
			if err := db.Where("unit_id = ?", req.UnitID).Delete(&models.Mindmap{}).Error; err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to delete existing mindmap"
				return
			}

			// Use the specific unit's content
			content := fmt.Sprintf("Unit: %s\n%s\n\n", unit.Title, unit.Content)

			// Generate mindmap using AI
			mindmapData, err := generator.GenerateFromContent(context.Background(), content, req.Prompt)
			if err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = fmt.Sprintf("Failed to generate mindmap: %v", err)
				return
			}

			// Create and save mindmap
			mindmap := models.Mindmap{
				UnitID: req.UnitID,
				Data:   mindmapData,
			}

			if err := db.Create(&mindmap).Error; err != nil {
				jobStatus.Status = "failed"
				jobStatus.Error = "Failed to save mindmap"
				return
			}

			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Result = mindmap
		}()

		c.JSON(http.StatusAccepted, gin.H{
			"message": "Mindmap generation started",
			"job_id":  jobID,
		})
	}
}

// GetMindmapStatus returns the status of an async mindmap generation job
func GetMindmapStatus(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("jobId")

		async.JobMutex.RLock()
		status, exists := async.JobStatuses[jobID]
		async.JobMutex.RUnlock()

		if !exists {
			c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
			return
		}

		c.JSON(http.StatusOK, status)
	}
}

// GetMindmap returns a specific mindmap
func GetMindmap(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		projectID := c.Param("projectId")
		unitID := c.Param("unitId")
		userID, _ := c.Get("user_id")

		// First verify project belongs to user and unit belongs to project
		var unit models.Unit
		if err := db.Joins("JOIN projects ON units.project_id = projects.id").
			Where("projects.id = ? AND projects.user_id = ? AND units.id = ?", projectID, userID, unitID).
			First(&unit).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Unit not found or you don't have access to it"})
			return
		}

		// Now that we've verified access rights, get the mindmap by unit_id
		var mindmap models.Mindmap
		if err := db.Where("unit_id = ?", unitID).
			First(&mindmap).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Mindmap not found"})
			return
		}

		// Parse the stored JSON data to validate it
		var reactFlowData interface{}
		if err := json.Unmarshal([]byte(mindmap.Data), &reactFlowData); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid mindmap data"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": reactFlowData,
		})
	}
}

// HandleAllMindmapsGeneration initiates async generation of mindmaps for all units in a project
func HandleAllMindmapsGeneration(db *gorm.DB) gin.HandlerFunc {
	// Create mindmap generator
	generator, err := mindmap.NewGenerator()
	if err != nil {
		return func(c *gin.Context) {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize mindmap generator"})
		}
	}

	return func(c *gin.Context) {
		// Get project_id from the query parameters
		projectIDStr := c.Query("project_id")
		if projectIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
			return
		}

		projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid project_id"})
			return
		}

		userID, _ := c.Get("user_id")

		// Verify project belongs to user
		var project models.Project
		if err := db.Where("id = ? AND user_id = ?", projectID, userID).
			Preload("Units").First(&project).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}

		// Create a job ID for the batch operation
		jobID := uuid.New().String()
		jobStatus := &async.JobStatus{
			Status:    "pending",
			Progress:  0,
			CreatedAt: time.Now(),
		}

		async.JobMutex.Lock()
		async.JobStatuses[jobID] = jobStatus
		async.JobMutex.Unlock()

		// Start async processing for all units
		go func() {
			defer func() {
				async.JobMutex.Lock()
				delete(async.JobStatuses, jobID)
				async.JobMutex.Unlock()
			}()

			jobStatus.Status = "processing"
			totalUnits := len(project.Units)

			if totalUnits == 0 {
				jobStatus.Status = "completed"
				jobStatus.Progress = 100
				jobStatus.Error = "No units found in the project"
				return
			}

			var errorUnits []uint
			var successUnits []uint

			// Process each unit
			for i, unit := range project.Units {
				unitProgress := float64(i) / float64(totalUnits) * 100
				jobStatus.Progress = int(unitProgress)
				jobStatus.Error = fmt.Sprintf("Processing unit %d of %d", i+1, totalUnits)

				// Delete existing mindmap if it exists
				if err := db.Where("unit_id = ?", unit.ID).Delete(&models.Mindmap{}).Error; err != nil {
					errorUnits = append(errorUnits, unit.ID)
					continue
				}

				// Use the specific unit's content
				content := fmt.Sprintf("Unit: %s\n%s\n\n", unit.Title, unit.Content)

				// Generate mindmap using AI
				mindmapData, err := generator.GenerateFromContent(context.Background(), content, "")
				if err != nil {
					errorUnits = append(errorUnits, unit.ID)
					continue
				}

				// Create and save mindmap
				mindmap := models.Mindmap{
					UnitID: unit.ID,
					Data:   mindmapData,
				}

				if err := db.Create(&mindmap).Error; err != nil {
					errorUnits = append(errorUnits, unit.ID)
					continue
				}

				successUnits = append(successUnits, unit.ID)
			}

			jobStatus.Status = "completed"
			jobStatus.Progress = 100
			jobStatus.Result = gin.H{
				"total_units":   totalUnits,
				"success_units": len(successUnits),
				"error_units":   len(errorUnits),
				"success_ids":   successUnits,
				"error_ids":     errorUnits,
			}
		}()

		c.JSON(http.StatusAccepted, gin.H{
			"message":     "Batch mindmap generation started",
			"job_id":      jobID,
			"total_units": len(project.Units),
		})
	}
}
