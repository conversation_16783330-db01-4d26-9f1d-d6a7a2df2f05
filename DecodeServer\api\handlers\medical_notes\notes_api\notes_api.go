package notes_api

import (
	"encoding/json"
	"net/http"
	"os"
	"path/filepath"

	"decodemed/models"

	"github.com/gin-gonic/gin"
)

// GetNotes handles GET request to fetch all medical notes
func GetNotes(c *gin.Context) {
	// TODO: Implement database integration
	// For now, we'll read from the uploads directory
	notes := []models.MedicalNote{}

	// Read all JSON files from the uploads directory
	files, err := filepath.Glob("uploads/*.json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read notes"})
		return
	}

	for _, file := range files {
		// Read and parse each JSON file
		data, err := os.ReadFile(file)
		if err != nil {
			continue // Skip files that can't be read
		}

		var note models.MedicalNote
		if err := json.Unmarshal(data, &note); err != nil {
			continue // Skip files that can't be parsed
		}

		notes = append(notes, note)
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, notes)
}

// GetNote handles GET request to fetch a specific medical note
func GetNote(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Note ID is required"})
		return
	}

	// TODO: Implement database integration
	// For now, we'll read from the uploads directory
	filePath := filepath.Join("uploads", id+".json")

	data, err := os.ReadFile(filePath)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Note not found"})
		return
	}

	var note models.MedicalNote
	if err := json.Unmarshal(data, &note); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse note"})
		return
	}

	c.JSON(http.StatusOK, note)
}

// DeleteNote handles DELETE request to remove a medical note
func DeleteNote(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Note ID is required"})
		return
	}

	// TODO: Implement database integration
	// For now, we'll delete the file from the uploads directory
	filePath := filepath.Join("uploads", id+".json")

	if err := os.Remove(filePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete note"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Note deleted successfully"})
}
