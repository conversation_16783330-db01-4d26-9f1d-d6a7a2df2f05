package handlers

import (
	"errors"
	"log"
	"net/http"
	"os"

	"decodemed/models"

	"github.com/clerk/clerk-sdk-go/v2"
	clerkhttp "github.com/clerk/clerk-sdk-go/v2/http"
	"github.com/clerk/clerk-sdk-go/v2/user"
	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

// -------- MIDDLEWARE --------
// -------- AuthMiddleware authenticates requests using Clerk SDK--------
func AuthMiddleware() gin.HandlerFunc {
	// Initialize Clerk client
	clerk.SetKey(os.Getenv("CLERK_SECRET_KEY"))

	return func(c *gin.Context) {
		// Skip auth for OPTIONS requests (CORS preflight)
		if c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// Use Clerk's middleware to verify the session
		handler := clerkhttp.WithHeaderAuthorization()(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract claims from the request context
			claims, ok := clerk.SessionClaimsFromContext(r.Context())
			if !ok {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session"})
				c.Abort()
				return
			}

			// Store claims in Gin context
			c.Set("clerk_claims", claims)

			// Continue processing the database logic
			db, exists := c.Get("db")
			if !exists {
				log.Printf("Database not available in context")
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
				c.Abort()
				return
			}

			gormDB, ok := db.(*gorm.DB)
			if !ok {
				log.Printf("Invalid database instance type")
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid database instance"})
				c.Abort()
				return
			}

			// Get or create user
			var dbUser models.User
			result := gormDB.Where("clerk_id = ?", claims.Subject).First(&dbUser)
			if result.Error != nil {
				if errors.Is(result.Error, gorm.ErrRecordNotFound) {
					// User not found, proceed with creation
					log.Printf("User with clerk_id %s not found in database, attempting to create.", claims.Subject)

					// Get user details from Clerk
					clerkUser, err := user.Get(c.Request.Context(), claims.Subject)
					if err != nil {
						log.Printf("Failed to get user %s from Clerk: %v", claims.Subject, err)
						c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user details"})
						c.Abort()
						return
					}
					if len(clerkUser.EmailAddresses) == 0 {
						log.Printf("User %s fetched from Clerk has no email addresses.", claims.Subject)
						c.JSON(http.StatusInternalServerError, gin.H{"error": "User has no email address"})
						c.Abort()
						return
					}

					// Create new user
					dbUser = models.User{
						ClerkID: claims.Subject,
						Email:   clerkUser.EmailAddresses[0].EmailAddress,
						// Initialize FreeProjectsCount explicitly if needed, though default is 0
						// FreeProjectsCount: 0,
					}
					createResult := gormDB.Create(&dbUser)
					if createResult.Error != nil {
						// Check if the error is a duplicate key violation (PostgreSQL specific)
						var pgErr *pgconn.PgError
						if errors.As(createResult.Error, &pgErr) && pgErr.Code == "23505" {
							log.Printf("Race condition detected: User %s already created. Re-fetching.", claims.Subject)
							// Attempt to fetch the user again, as it was likely created by a concurrent request
							retryResult := gormDB.Where("clerk_id = ?", claims.Subject).First(&dbUser)
							if retryResult.Error != nil {
								log.Printf("Failed to re-fetch user %s after duplicate key error: %v", claims.Subject, retryResult.Error)
								c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve user after creation conflict"})
								c.Abort()
								return
							}
							log.Printf("Successfully re-fetched user %s after race condition.", claims.Subject)
						} else {
							// Different creation error
							log.Printf("Failed to create user %s: %v", claims.Subject, createResult.Error)
							c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
							c.Abort()
							return
						}
					} else {
						log.Printf("Created new user: ID=%d, ClerkID=%s, Email=%s", dbUser.ID, dbUser.ClerkID, dbUser.Email)
					}
				} else {
					// Different error during initial fetch
					log.Printf("Database error fetching user %s: %v", claims.Subject, result.Error)
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error fetching user"})
					c.Abort()
					return
				}
			}

			// Set user information in context (ensure dbUser is populated)
			if dbUser.ID == 0 {
				log.Printf("User object is not properly populated for clerk_id %s after middleware logic", claims.Subject)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to resolve user identity"})
				c.Abort()
				return
			}
			c.Set("user_id", dbUser.ID)
			c.Set("user_email", dbUser.Email)
			c.Set("clerk_id", dbUser.ClerkID)
		}))

		// Call the Clerk middleware with the current request
		handler.ServeHTTP(c.Writer, c.Request)

		// If we got here without aborting, continue to the next handler
		c.Next()
	}
}

// -------- HANDLERS --------
// -------- RegisterHandler handles user registration with Clerk--------
func RegisterHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verify the token
		claims, ok := clerk.SessionClaimsFromContext(c.Request.Context())
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session"})
			return
		}

		// Get user details from Clerk
		clerkUser, err := user.Get(c.Request.Context(), claims.Subject)
		if err != nil {
			log.Printf("Failed to get user from Clerk: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user details"})
			return
		}

		// Create user in our database
		dbUser := models.User{
			ClerkID: claims.Subject,
			Email:   clerkUser.EmailAddresses[0].EmailAddress,
		}
		if err := db.Create(&dbUser).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message": "User registered successfully",
			"user": gin.H{
				"id":       dbUser.ID,
				"clerk_id": dbUser.ClerkID,
				"email":    dbUser.Email,
			},
		})
	}
}

// -------- LoginHandler is no longer needed as Clerk handles authentication--------
// -------- But we'll keep a simplified version for compatibility--------
func LoginHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, ok := clerk.SessionClaimsFromContext(c.Request.Context())
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session"})
			return
		}

		var dbUser models.User
		if err := db.Where("clerk_id = ?", claims.Subject).First(&dbUser).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"user": gin.H{
				"id":       dbUser.ID,
				"clerk_id": dbUser.ClerkID,
				"email":    dbUser.Email,
			},
		})
	}
}
