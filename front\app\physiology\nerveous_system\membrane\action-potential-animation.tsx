"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Play, Pause, RotateCcw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ActionPotentialGraph from "./action-potential-graph"
import NeuronMembrane from "./neuron-membrane"
import PartInfoPanel from "./PartInfoPanel"
import NeuronPhysiologyAnimation from "./neuron-physiology-animation"

export default function ActionPotentialAnimation() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [progress, setProgress] = useState(0)
  const [speed, setSpeed] = useState(1)
  const animationRef = useRef<number | null>(null)
  const lastTimeRef = useRef<number | null>(null)

  // Animation loop
  const animate = (time: number) => {
    if (lastTimeRef.current === null) {
      lastTimeRef.current = time
    }

    const deltaTime = time - lastTimeRef.current
    lastTimeRef.current = time

    setProgress((prev) => {
      const newProgress = prev + deltaTime * 0.01 * speed
      return newProgress > 100 ? 0 : newProgress
    })

    animationRef.current = requestAnimationFrame(animate)
  }

  // Start/stop animation
  useEffect(() => {
    if (isPlaying) {
      lastTimeRef.current = null
      animationRef.current = requestAnimationFrame(animate)
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying, speed])

  const togglePlay = () => setIsPlaying(!isPlaying)

  const resetAnimation = () => {
    setIsPlaying(false)
    setProgress(0)
  }

  return (
    <div className="relative bg-[hsl(240_10%_3.9%)] rounded-lg shadow-lg p-4 md:p-6 border border-gray-800">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Neuron Membrane Physiology</h1>
        <p className="text-gray-400">Understanding Action Potential and Membrane Dynamics</p>
      </div>

      <div className="max-w-7xl mx-auto">
        <NeuronPhysiologyAnimation />
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="flex-none w-100 pl-4">
          <ActionPotentialGraph progress={progress} />
        </div>
        <div className="flex-[4] flex justify-center items-center">
          <NeuronMembrane progress={progress} />
        </div >

        <div className="flex-none w-90">
          <PartInfoPanel progress={progress} />
        </div>

      </div>

      <div className="mt-6 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={togglePlay}
              aria-label={isPlaying ? "Pause animation" : "Play animation"}
              className="border-gray-700 hover:bg-gray-800"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={resetAnimation}
              aria-label="Reset animation"
              className="border-gray-700 hover:bg-gray-800"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-300">Speed:</span>
            <div className="w-32">
              <input
                type="range"
                value={speed}
                min={0.5}
                max={2}
                step={0.1}
                onChange={(e) => setSpeed(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
            <span className="text-sm w-8 text-gray-300">{speed.toFixed(1)}x</span>
          </div>
        </div>

        <div className="h-2 w-full bg-gray-800 rounded-full overflow-hidden">
          <motion.div className="h-full bg-gray-400" style={{ width: `${progress}%` }} />
        </div>
      </div>
    </div>
  )
}
