import React from 'react';
import { History } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';

interface SearchHistoryProps {
  isHistoryVisible: boolean;
  toggleHistoryVisibility: () => void;
  searchHistory: Array<{ id: number; question: string; created_at: string }>;
  setQuestion: (question: string) => void;
}

const SearchHistory: React.FC<SearchHistoryProps> = ({
  searchHistory,
  setQuestion,
}) => {
  const { theme } = useTheme();

  // Function to organize history items by time period
  const organizeHistoryByTime = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const organized = {
      today: [] as typeof searchHistory,
      yesterday: [] as typeof searchHistory,
      lastWeek: [] as typeof searchHistory,
      lastMonth: [] as typeof searchHistory,
      older: [] as typeof searchHistory,
    };

    searchHistory.forEach(item => {
      const itemDate = new Date(item.created_at);
      const itemDay = new Date(itemDate.getFullYear(), itemDate.getMonth(), itemDate.getDate());

      if (itemDay.getTime() === today.getTime()) {
        organized.today.push(item);
      } else if (itemDay.getTime() === yesterday.getTime()) {
        organized.yesterday.push(item);
      } else if (itemDay >= lastWeek && itemDay < yesterday) {
        organized.lastWeek.push(item);
      } else if (itemDay >= lastMonth && itemDay < lastWeek) {
        organized.lastMonth.push(item);
      } else {
        organized.older.push(item);
      }
    });

    return organized;
  };

  const organizedHistory = organizeHistoryByTime();

  // Helper function to render history items
  const renderHistoryItems = (items: typeof searchHistory, title: string) => {
    if (items.length === 0) return null;

    return (
      <div className="mb-4">
        <h3 className={`text-xs font-semibold ${
          theme === 'dark' ? 'text-neutral-400' : 'text-gray-500'
        } mb-2`}>{title}</h3>
        <ul className="space-y-2">
          {items.map((item) => (
            <li key={item.id} className={`${
              theme === 'dark' ? 'border-b border-neutral-800' : 'border-b border-gray-100'
            } pb-2`}>
              <button
                onClick={() => setQuestion(item.question)}
                className={`text-left ${
                  theme === 'dark' 
                    ? 'hover:bg-neutral-800 text-neutral-300' 
                    : 'hover:bg-slate-200 text-gray-800'
                } hover:rounded-lg p-1 w-full text-sm line-clamp-2`}
                title={item.question}
              >
                {item.question}
              </button>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`flex flex-col z-10 ${
      theme === 'dark' 
        ? 'bg-neutral-900 shadow-md border-l border-neutral-800' 
        : 'bg-white shadow-md border-l border-gray-200'
    } h-full w-64`}>
      <div className={`p-4 ${
        theme === 'dark' ? 'border-b border-neutral-800' : 'border-b border-gray-200'
      }`}>
        <h2 className={`text-lg font-medium flex items-center ${
          theme === 'dark' ? 'text-neutral-200' : 'text-gray-900'
        }`}>
          <History size={20} className="mr-2" />
          History
        </h2>
      </div>

      {/* History Content */}
      <div className="flex-grow overflow-y-auto">
        <div className="p-4">
          {renderHistoryItems(organizedHistory.today, 'Today')}
          {renderHistoryItems(organizedHistory.yesterday, 'Yesterday')}
          {renderHistoryItems(organizedHistory.lastWeek, 'Last Week')}
          {renderHistoryItems(organizedHistory.lastMonth, 'Last Month')}
          {renderHistoryItems(organizedHistory.older, 'Older')}
        </div>
      </div>
    </div>
  );
};

export default SearchHistory;

