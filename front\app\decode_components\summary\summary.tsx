"use client";

import React, { useState, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import {  ChevronLeft, ChevronRight} from "lucide-react";
import { useTheme } from "@/components/theme/theme-provider";
import { useAuth } from "@clerk/nextjs";
import YouTubeSummary from "./summary_components/YouTubeSummary";
import { API_URL } from "@/app/utils/decode_api";

interface NotesAppProps {
  projectId?: string;
}

interface Project {
  id?: number;
  ID?: number;
  name?: string;
  Name?: string;
  description?: string;
  Description?: string;
  created_at?: string;
  CreatedAt?: string;
  updated_at?: string;
  UpdatedAt?: string;
  unit_count?: number;
  file_url?: string;
  FileURL?: string;
  units_generated?: boolean;
  youtube_units_generated?: boolean;
  type?: string;
}

interface Unit {
  id: number;
  ID?: number;
  title: string;
  Title?: string;
  content: string;
  Content?: string;
  description?: string;
  Description?: string;
  order: number;
  Order?: number;
  created_at: string;
  CreatedAt?: string;
  createdAt?: string;
  project_id: number;
  ProjectID?: number;
  type?: string;
  Type?: string;
  youtube_summary?: string;
  YoutubeSummary?: string;
  summary?: {
    id: number;
    content: string;
    created_at: string;
    updated_at: string;
  } | null;
  Summary?: {
    id: number;
    content: string;
    created_at: string;
    updated_at: string;
  } | null;
}

interface SummaryProps {
  projectId?: string;
}

export default function Summary({ projectId }: SummaryProps) {
  return (
    <NotesApp projectId={projectId} />
  );
}

function NotesApp({ projectId }: NotesAppProps) {
  const { getToken } = useAuth();
  const { theme } = useTheme();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedUnit, setSelectedUnit] = useState<Unit | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [carouselIndex, setCarouselIndex] = useState(0);
  const [isUpdatingFromCarousel, setIsUpdatingFromCarousel] = useState(false);
  
  
  
  // Enhanced function to render markdown content with proper formatting
  const renderMarkdownContent = (content: string) => {
    // Check if content exists
    if (!content) return "No content available for this unit.";
    
    // Process standalone source text blocks in square brackets to display as blockquote
    // Matches lines containing only [...] potentially with surrounding whitespace
    let processedContent = content.replace(
      /^(\s*)\[(.*?)\](\s*)$/gm,
      (match, leadingSpace, sourceText, trailingSpace) =>
        `${leadingSpace}\n\n> ${sourceText}\n\n${trailingSpace}` // Convert to blockquote markdown
    );
    
    // Ensure proper spacing between subtitles and bullet points
    // Add extra newlines before subtitle sections to prevent them from merging with bullet points
    processedContent = processedContent.replace(
      /\*\*(.*?)\*\*\n *-/g, // Adjusted to handle optional space before '-'
      '**$1**\n\n -'
    );
    
    // Ensure subtitles that are on their own line have consistent spacing before and after
    processedContent = processedContent.replace(
      /(?:^|\n)\s*\*\*(.*?)\*\*\s*(?:\n|$)/g, // Match only standalone titles more precisely
      '\n\n**$1**\n\n'
    );
    
    // Remove any triple or more newlines that might have been created, leaving max double newlines
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n');
    
    // Trim leading/trailing whitespace that might result from replacements
    processedContent = processedContent.trim();

    return processedContent;
  };
  
  // Use the projectId from props if available
  useEffect(() => {
    if (projectId) {
      console.log("Summary component: Loading project with ID:", projectId);
      const fetchAndSelectProject = async () => {
        try {
          setIsLoading(true);
          const token = await getToken();
          if (!token) return;
          
          // Make sure projectId is not undefined before making the request
          if (!projectId) {
            console.error("Project ID is undefined");
            return;
          }
          
          console.log("Fetching project with ID:", projectId);
          const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });
          
          if (!response.ok) {
            console.error(`Failed to fetch project: ${response.status}`);
            throw new Error(`Failed to fetch project: ${response.status}`);
          }
          
          const data = await response.json();
          if (data.project) {
            console.log("Project data received:", data.project);
            // Set the selected project
            setSelectedProject(data.project);
            // Fetch units for this project - make sure we have a valid number ID
            const projectIdNumber = data.project.id || data.project.ID;
            if (projectIdNumber !== undefined && !isNaN(Number(projectIdNumber))) {
              // This will fetch all units at once and prepare them for navigation
              await fetchProjectUnits(Number(projectIdNumber));
            } else {
              console.error("Invalid project ID:", projectIdNumber);
              setIsLoading(false);
            }
          } else {
            console.error("Project data missing in response:", data);
            setIsLoading(false);
          }
        } catch (error) {
          console.error("Error fetching specific project:", error);
          setIsLoading(false);
        }
      };
      
      fetchAndSelectProject();
    }
  }, [projectId, getToken]);

  // No need to fetch all projects on mount since we removed the projects dropdown

  // Fetch units when project is selected (if needed)
  useEffect(() => {
    if (selectedProject && selectedProject.id && !projectId) {
      fetchProjectUnits(selectedProject.id);
    }
  }, [selectedProject, projectId]);

  // Fetch units for a specific project
  const fetchProjectUnits = async (projectId: number) => {
    // Guard against undefined or invalid projectId
    if (!projectId || isNaN(projectId)) {
      console.error('Invalid project ID provided to fetchProjectUnits:', projectId);
      return;
    }
    
    try {
      console.log('Fetching units for project:', projectId);
      setIsLoading(true);
      const token = await getToken();
      const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('Failed to fetch project units');
      const data = await response.json();
      console.log('Project units response:', data);

      // Handle different response formats
      let unitsToSet: Unit[] = [];
      if (data.project?.units && Array.isArray(data.project.units)) {
        unitsToSet = data.project.units;
      } else if (data.units && Array.isArray(data.units)) {
        unitsToSet = data.units;
      } else if (data.project?.Units && Array.isArray(data.project.Units)) {
        unitsToSet = data.project.Units;
      } else if (Array.isArray(data)) {
        unitsToSet = data;
      }

      console.log('Raw units data:', unitsToSet);

      // Update the filtering logic for units
      const filteredUnits = unitsToSet.filter(unit => {
        // Normalize the unit type field
        const unitType = (unit.type || unit.Type || '').toLowerCase();
        const isYouTubeUnit = unitType === 'youtube';
        
        // Check for required fields based on unit type
        const hasRequiredContent = isYouTubeUnit 
          ? (unit.youtube_summary !== undefined && unit.youtube_summary !== null) || 
            (unit.YoutubeSummary !== undefined && unit.YoutubeSummary !== null)
          : (unit.content !== undefined && unit.content !== null) || 
            (unit.Content !== undefined && unit.Content !== null);
        
        // Ensure the unit has an ID and title
        const hasId = (unit.id !== undefined && unit.id !== null) || 
                      (unit.ID !== undefined && unit.ID !== null);
        const hasTitle = (unit.title !== undefined && unit.title !== null) || 
                         (unit.Title !== undefined && unit.Title !== null);
        
        const isValid = hasId && hasTitle && hasRequiredContent;
        
        if (!isValid) {
          console.log(`Filtering out unit ${unit.id || unit.ID} - ${unit.title || unit.Title}:`);
          console.log(`  Type: ${unitType}`);
          console.log(`  Has ID: ${hasId}`);
          console.log(`  Has title: ${hasTitle}`);
          console.log(`  Has content: ${!isYouTubeUnit && ((unit.content !== undefined && unit.content !== null) || 
                                                         (unit.Content !== undefined && unit.Content !== null))}`);
          console.log(`  Has youtube_summary: ${isYouTubeUnit && ((unit.youtube_summary !== undefined && unit.youtube_summary !== null) || 
                                                                (unit.YoutubeSummary !== undefined && unit.YoutubeSummary !== null))}`);
        }
        
        return isValid;
      });

      

      // Update the mapping logic for units
      const mappedUnits = filteredUnits.map(unit => {
        // Normalize the unit type field
        const unitType = (unit.type || unit.Type || '').toLowerCase();
        const isYouTubeUnit = unitType === 'youtube';
        
        // Get the appropriate content based on unit type
        const unitContent = isYouTubeUnit 
          ? (unit.youtube_summary || unit.YoutubeSummary || '')
          : (unit.content || unit.Content || '');
        
      
        
        return {
          id: unit.id !== undefined ? unit.id : (unit.ID !== undefined ? unit.ID : 0),
          title: unit.title || unit.Title || '',
          content: unitContent,
          order: unit.order !== undefined ? unit.order : (unit.Order !== undefined ? unit.Order : 0),
          created_at: unit.created_at || unit.CreatedAt || unit.createdAt || '',
          project_id: unit.project_id || unit.ProjectID || 0,
          type: unitType || 'document',
          youtube_summary: unit.youtube_summary || unit.YoutubeSummary || ''
        };
      });

      // Sort units by order field to display them in the correct sequence
      const sortedUnits = [...mappedUnits].sort((a, b) => {
        // First, try to extract unit numbers from titles (e.g., "Unit 1", "Unit 2")
        const extractUnitNumber = (title: string): number | null => {
          const match = /Unit\s+(\d+)/i.exec(title);
          return match ? parseInt(match[1], 10) : null;
        };

        const aUnitNum = extractUnitNumber(a.title);
        const bUnitNum = extractUnitNumber(b.title);

        // If both titles have unit numbers, sort by those numbers
        if (aUnitNum !== null && bUnitNum !== null) {
          return aUnitNum - bUnitNum;
        }

        // If only one has a unit number, prioritize it
        if (aUnitNum !== null) return -1;
        if (bUnitNum !== null) return 1;

        // Fall back to order field if available
        if (a.order !== undefined && b.order !== undefined) {
          return a.order - b.order;
        }
        
        // Last resort: sort by ID
        return (a.id || 0) - (b.id || 0);
      });
      
      console.log('Setting normalized and sorted units:', sortedUnits);
      setUnits(sortedUnits);
      
      // Update the initial unit selection
      if (sortedUnits.length > 0 && !selectedUnit) {
        console.log('Selecting first unit:', sortedUnits[0]);
        const firstUnit = sortedUnits[0];
        
        // Set the carousel index to 0
        setCarouselIndex(0);
        
        // Update selected unit
        setSelectedUnit(firstUnit);
        
        // Fetch the content for this unit
        fetchUnitContent(firstUnit.id);
      } else if (sortedUnits.length === 0) {
        console.log('No units available for this project');
        setSelectedUnit(null);
      }
    } catch (error) {
      console.error('Error fetching project units:', error);
      setUnits([]);
      setSelectedUnit(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Update the fetchUnitContent function to handle both regular and YouTube units
  const fetchUnitContent = async (unitId: number) => {
    if (!unitId) {
      console.log('fetchUnitContent called without unitId');
      return;
    }
    
    try {
      setIsLoading(true);
      const token = await getToken();
      const response = await fetch(`${API_URL}/api/decode/units/${unitId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        console.error(`Failed to fetch unit content: ${response.status} ${response.statusText}`);
        throw new Error('Failed to fetch unit content');
      }
      
      const data = await response.json();
      
      // Handle different field naming conventions from the backend
      const unitData = {
        id: data.id || data.ID || 0,
        title: data.title || data.Title || '',
        content: data.content || data.Content || '',
        order: data.order !== undefined ? data.order : (data.Order !== undefined ? data.Order : 0),
        created_at: data.created_at || data.CreatedAt || data.createdAt || '',
        project_id: data.project_id || data.ProjectID || 0,
        type: data.type || data.Type || 'document',
        youtube_summary: data.youtube_summary || data.YoutubeSummary || ''
      };
      
      const isYouTubeUnit = (unitData.type || '').toLowerCase() === 'youtube';
      console.log(`Unit type: ${isYouTubeUnit ? 'YouTube' : 'Regular'}`);
      
      // Set the selected unit with all necessary data
      setSelectedUnit({
        ...unitData,
        // For YouTube units, use youtube_summary as the content if available
        content: isYouTubeUnit ? (unitData.youtube_summary || unitData.content || '') : (unitData.content || '')
      });
      
      console.log('Updated selected unit:', {
        id: unitData.id,
        title: unitData.title,
        type: unitData.type,
        hasYoutubeSummary: !!unitData.youtube_summary,
        contentLength: isYouTubeUnit ? 
          (unitData.youtube_summary ? unitData.youtube_summary.length : 0) : 
          (unitData.content ? unitData.content.length : 0)
      });
      
    } catch (error) {
      console.error('Error fetching unit content:', error);
      
      // Fallback to cached units if fetch fails
      const cachedUnit = units.find(unit => unit.id === unitId);
      if (cachedUnit) {
        console.log('Using cached unit data as fallback');
        const isYoutubeUnit = (cachedUnit.type || '').toLowerCase() === 'youtube';
        
        setSelectedUnit({
          ...cachedUnit,
          content: isYoutubeUnit ? (cachedUnit.youtube_summary || cachedUnit.content || '') : (cachedUnit.content || '')
        });
        console.log('Set cached unit as selected unit');
      } else {
        console.error('No cached unit found for fallback');
      }
    } finally {
      setIsLoading(false);
    }
  };

  

  // Update carousel index when selectedUnit changes through other means
  useEffect(() => {
    // Skip if the update is coming from the carousel effect or if no selected unit
    if (!selectedUnit || units.length === 0 || isUpdatingFromCarousel) return;
    
    // Find index of selected unit in the units array
    const index = units.findIndex(unit => {
      // Safely convert IDs to strings for comparison, handling undefined/null cases
      const unitId = unit.id !== undefined && unit.id !== null ? unit.id.toString() : '';
      const selectedId = selectedUnit.id !== undefined && selectedUnit.id !== null ? selectedUnit.id.toString() : '';
      return unitId === selectedId;
    });
    
    // Only update carousel index if it's different from current index
    if (index !== -1 && index !== carouselIndex) {
      setCarouselIndex(index);
    }
  }, [selectedUnit?.id, units, carouselIndex, isUpdatingFromCarousel]);

  // Update selected unit when carousel index changes
  useEffect(() => {
    if (units.length > 0 && carouselIndex >= 0 && carouselIndex < units.length) {
      setIsUpdatingFromCarousel(true);
      // Set the new selected unit
      setSelectedUnit(units[carouselIndex]);
      // Clear the flag after a short delay
      const timer = setTimeout(() => {
        setIsUpdatingFromCarousel(false);
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [carouselIndex, units]);

  // Update the carousel navigation function
  const navigateCarousel = (direction: 'prev' | 'next') => {
    if (units.length === 0 || isLoading) return;
    
    // Set a brief loading state for visual feedback
    setIsLoading(true);
    
    let newIndex = carouselIndex;
    if (direction === 'prev') {
      newIndex = carouselIndex > 0 ? carouselIndex - 1 : units.length - 1;
    } else {
      newIndex = carouselIndex < units.length - 1 ? carouselIndex + 1 : 0;
    }
    
    // Set the new carousel index
    setCarouselIndex(newIndex);
    
    // Get the unit from the cached units
    const unit = units[newIndex];
    
    if (!unit || unit.id === undefined) {
      console.error('Invalid unit at index', newIndex);
      setIsLoading(false);
      return;
    }
    
    // Fetch the latest content for this unit
    fetchUnitContent(unit.id);
  };

  // Ensure carouselIndex stays within bounds when units change
  useEffect(() => {
    if (units.length > 0) {
      // If carouselIndex is out of bounds, reset to 0
      if (carouselIndex >= units.length) {
        setCarouselIndex(0);
        
        // Also update selected unit to match the new index
        const unit = units[0];
        setSelectedUnit(unit);
        
        // Check if this is a YouTube unit
        const isYoutubeUnit = unit.type === 'youtube';
        
        // Set the content based on unit type
        if (isYoutubeUnit && unit.youtube_summary) {
          console.log('Setting YouTube summary content after carousel reset:', unit.youtube_summary);
        } else {
          console.log('Setting regular content after carousel reset:', unit.content);
        }
      }
    }
  }, [units, carouselIndex]);


  
  return (
    <div className="flex h-full w-full bg-white dark:bg-[hsl(0_0%_7.0%)] overflow-hidden">
      <div className="flex-1 flex flex-col overflow-hidden max-w-full">
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-auto">
            {/* Content panel - cleaner padding */}
            <div className="flex-1 overflow-auto p-5 md:p-6">
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-10 w-10 border-2 border-gray-300 dark:border-gray-600 border-t-gray-600 dark:border-t-gray-300"></div>
                </div>
              ) : selectedUnit ? (
                <>
                  {/* Check if this is a YouTube unit */}
                  {(selectedUnit.type || '').toLowerCase() === 'youtube' ? (
                    <YouTubeSummary 
                      summary={selectedUnit.youtube_summary || ''} 
                      theme={theme}
                    />
                  ) : (
                    <>
                    
                      <div className="prose prose-slate dark:prose-invert max-w-none">
                        <ReactMarkdown
                          components={{
                            h1: ({children}) => <h1 className="text-4xl font-medium mb-4">{children}</h1>,
                            h2: ({children}) => <h2 className="text-lg font-medium mb-3 mt-6">{children}</h2>,
                            strong: ({children}) => (
                              <span className="font-semibold text-lg block mt-6 mb-3 pl-0">{children}</span>
                            ),
                            ul: ({children}) => <ul className="list-disc pl-6 mb-4 mt-2">{children}</ul>,
                            li: ({children}) => <li className="mb-2">{children}</li>,
                            p: ({children}) => <p className="mb-4 text-gray-800 dark:text-gray-200">{children}</p>,
                            blockquote: ({children}) => (
                              <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 py-1 my-4 text-sm text-gray-600 dark:text-gray-400 italic">
                                {children}
                              </blockquote>
                            ),
                          }}
                        >
                          {renderMarkdownContent(selectedUnit.content || '')}
                        </ReactMarkdown>
                      </div>
                    </>
                  )}
                </>
              ) : (
                <div className="flex items-center justify-center h-full p-2 text-sm text-gray-500 dark:text-gray-400">
                  {units.length > 0 ? 'Please select a unit' : 'No units available for this project'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Units carousel - minimalistic bottom navigation */}
        {selectedProject && (
          <div className="w-full flex-shrink-0 py-2 px-3 rounded-3xl mx-1 mb-1 mr-1 border-gray-100 dark:border-gray-800 bg-white dark:bg-[#1e1e1e] sticky  bottom-0 z-10 shadow-sm">
            {units.length > 0 && carouselIndex < units.length && (
              <div className="flex items-center justify-between">
                {/* Left navigation button */}
                <button 
                  onClick={() => navigateCarousel('prev')} 
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    theme === 'dark' 
                      ? `text-gray-400 hover:text-gray-200 hover:bg-gray-800 ${isLoading ? 'opacity-50' : ''}` 
                      : `text-gray-500 hover:text-gray-700 hover:bg-gray-100 ${isLoading ? 'opacity-50' : ''}`
                  }`}
                  disabled={units.length <= 1 || isLoading}
                  aria-label="Previous unit"
                >
                  <ChevronLeft size={18} />
                </button>
                
                {/* Center - unit info with markdown support */}
                <div className="flex flex-col items-center">
                  <div className={`text-sm font-medium truncate max-w-[200px] sm:max-w-[300px] ${
                    isLoading
                      ? 'text-gray-400 dark:text-gray-500'
                      : 'text-gray-700 dark:text-gray-300'
                  }`}>
                    {isLoading ? 'Loading...' : (
                      // Remove any markdown formatting from the title for the carousel
                      units[carouselIndex].title.replace(/\*\*/g, '').replace(/##/g, '')
                    )}
                  </div>
                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-0.5">
                    {isLoading ? '' : `${carouselIndex + 1} / ${units.length}`}
                  </div>
                </div>
                
                {/* Right navigation button */}
                <button 
                  onClick={() => navigateCarousel('next')} 
                  className={`p-2 rounded-full transition-colors duration-200 ${
                    theme === 'dark' 
                      ? `text-gray-400 hover:text-gray-200 hover:bg-gray-800 ${isLoading ? 'opacity-50' : ''}` 
                      : `text-gray-500 hover:text-gray-700 hover:bg-gray-100 ${isLoading ? 'opacity-50' : ''}`
                  }`}
                  disabled={units.length <= 1 || isLoading}
                  aria-label="Next unit"
                >
                  <ChevronRight size={18} />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}