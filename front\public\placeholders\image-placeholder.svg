<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Image Placeholder</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="imageGradient">
            <stop stop-color="#F0F9FF" offset="0%"></stop>
            <stop stop-color="#E0F2FE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Image background -->
        <rect fill="url(#imageGradient)" x="20" y="20" width="160" height="160" rx="8" stroke="#BAE6FD" stroke-width="2"></rect>
        
        <!-- Image icon -->
        <g transform="translate(70, 60)">
            <rect fill="#0EA5E9" x="0" y="0" width="60" height="40" rx="4"></rect>
            <circle fill="#FFFFFF" cx="20" cy="15" r="8"></circle>
            <path d="M0,40 L20,25 L30,30 L60,10 L60,40 Z" fill="#0284C7"></path>
        </g>
        
        <!-- Mountain icon -->
        <path d="M50,120 L80,90 L110,110 L150,70 L150,140 L50,140 Z" fill="#0EA5E9" opacity="0.7"></path>
        
        <!-- Image text -->
        <text font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0369A1" text-anchor="middle" x="100" y="170">
            Image
        </text>
    </g>
</svg>
