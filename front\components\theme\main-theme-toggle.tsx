"use client"

import { Moon, Sun } from "lucide-react"
import { useMainTheme } from "./main-theme-provider"

export function MainThemeToggle() {
  const { theme, toggleTheme } = useMainTheme()

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded-md transition-colors flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-800"
      aria-label="Toggle theme"
    >
      {theme === 'light' ? (
        <Moon className="h-5 w-5" />
      ) : (
        <Sun className="h-5 w-5" />
      )}
    </button>
  )
} 