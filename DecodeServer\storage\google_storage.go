package storage

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"github.com/google/uuid"
	"google.golang.org/api/option"
)

// GoogleCloudStorage implements cloud storage functionality
type GoogleCloudStorage struct {
	client              *storage.Client
	bucketName          string
	projectID           string
	bucket              *storage.BucketHandle
	bucketURL           string
	privateKey          []byte
	serviceAccountEmail string
	converter           FileConverter
}

// *****************  CREATES NEW GOOGLE CLOUD INSTANCE ********************
// ---- NewGoogleCloudStorage creates a new instance of GoogleCloudStorage-----------
func NewGoogleCloudStorage(ctx context.Context, bucketName, projectID, credentialsPath string) (*GoogleCloudStorage, error) {
	var client *storage.Client
	var privateKey []byte
	var serviceAccountEmail string
	var err error

	if credentialsPath != "" {
		var jsonKey []byte

		// Check if credentialsPath is base64 encoded content
		if strings.HasPrefix(strings.TrimSpace(credentialsPath), "eyJ") ||
			strings.Contains(credentialsPath, "+") ||
			strings.Contains(credentialsPath, "/") ||
			strings.Contains(credentialsPath, "=") {
			// It looks like base64 encoded content, try to decode it
			decoded, err := base64.StdEncoding.DecodeString(credentialsPath)
			if err != nil {
				return nil, fmt.Errorf("failed to decode base64 credentials: %v", err)
			}
			jsonKey = decoded
			log.Printf("Successfully decoded base64 credentials")
		} else {
			// It's a file path, read the file
			jsonKey, err = os.ReadFile(credentialsPath)
			if err != nil {
				return nil, fmt.Errorf("failed to read credentials file: %v", err)
			}
		}

		// Parse the credentials to extract email
		var creds struct {
			ClientEmail string `json:"client_email"`
			PrivateKey  string `json:"private_key"`
		}
		err = json.Unmarshal(jsonKey, &creds)
		if err != nil {
			return nil, fmt.Errorf("failed to parse credentials: %v", err)
		}

		// Save the private key and email
		privateKey = []byte(creds.PrivateKey)
		serviceAccountEmail = creds.ClientEmail

		// Create client with the credentials JSON
		client, err = storage.NewClient(ctx, option.WithCredentialsJSON(jsonKey))
		if err != nil {
			return nil, fmt.Errorf("failed to create storage client with credentials: %v", err)
		}
	} else {
		// Use default credentials
		client, err = storage.NewClient(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to create storage client with default credentials: %v", err)
		}
	}

	// If service account email wasn't in the credentials file, try environment
	if serviceAccountEmail == "" {
		serviceAccountEmail = os.Getenv("GCS_SERVICE_ACCOUNT_EMAIL")
	}

	bucket := client.Bucket(bucketName)
	bucketURL := fmt.Sprintf("https://storage.googleapis.com/%s", bucketName)

	// Create a new PDF converter
	converter := NewGotenbergConverter()

	return &GoogleCloudStorage{
		client:              client,
		bucketName:          bucketName,
		projectID:           projectID,
		bucket:              bucket,
		bucketURL:           bucketURL,
		privateKey:          privateKey,
		serviceAccountEmail: serviceAccountEmail,
		converter:           converter,
	}, nil
}

// ****************************  1. UPLOADS FILE TO GOOGLE CLOUD STORAGE *****************************
// ----------- UploadFile uploads a file to Google Cloud Storage ------------
func (g *GoogleCloudStorage) UploadFile(ctx context.Context, file *multipart.FileHeader, directory string) (string, error) {
	// Check if conversion to PDF is needed and supported
	pdfBytes, pdfFilename, err := g.converter.ConvertToPDF(file)
	if err != nil {
		log.Printf("Error converting file to PDF: %v", err)
		// Continue with original file if conversion failed
	}

	// Generate a unique filename
	ext := filepath.Ext(pdfFilename)
	objectName := fmt.Sprintf("%s/%s%s", directory, uuid.New().String(), ext)

	// Get a handle on the bucket
	bucket := g.client.Bucket(g.bucketName)

	// Create an object writer
	obj := bucket.Object(objectName)
	writer := obj.NewWriter(ctx)
	defer writer.Close()

	// Set content type based on file extension
	contentType := "application/octet-stream" // default
	switch ext {
	case ".pdf":
		contentType = "application/pdf"
	case ".docx":
		contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	case ".txt":
		contentType = "text/plain"
	}
	writer.ContentType = contentType

	// If we have PDF bytes from conversion, write them directly
	if pdfBytes != nil {
		if _, err = writer.Write(pdfBytes); err != nil {
			return "", fmt.Errorf("failed to upload converted PDF: %v", err)
		}
	} else {
		// Otherwise, open and copy the original file
		src, err := file.Open()
		if err != nil {
			return "", fmt.Errorf("failed to open file: %v", err)
		}
		defer src.Close()

		// Copy the file to GCS
		if _, err = io.Copy(writer, src); err != nil {
			return "", fmt.Errorf("failed to upload file: %v", err)
		}
	}

	// Get the public URL
	url := fmt.Sprintf("https://storage.googleapis.com/%s/%s", g.bucketName, objectName)
	return url, nil
}

// ------------ UploadFileFromPath uploads a file from a local path to Google Cloud Storage ------------
func (g *GoogleCloudStorage) UploadFileFromPath(ctx context.Context, filePath, directory string) (string, error) {
	// Convert to PDF if needed
	pdfPath, err := g.converter.ConvertFileToPDF(filePath)
	if err != nil {
		log.Printf("Error converting file to PDF: %v", err)
		// Continue with original file if conversion failed
		pdfPath = filePath
	}

	// Use the converted PDF path or original path
	fileToUpload := pdfPath

	// Open the source file
	src, err := os.Open(fileToUpload)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer src.Close()

	// Generate a unique filename
	ext := filepath.Ext(fileToUpload)
	objectName := fmt.Sprintf("%s/%s%s", directory, uuid.New().String(), ext)

	// Get a handle on the bucket
	bucket := g.client.Bucket(g.bucketName)

	// Create an object writer
	obj := bucket.Object(objectName)
	writer := obj.NewWriter(ctx)
	defer writer.Close()

	// Set content type based on file extension
	contentType := "application/octet-stream" // default
	switch ext {
	case ".pdf":
		contentType = "application/pdf"
	case ".docx":
		contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	case ".txt":
		contentType = "text/plain"
	}
	writer.ContentType = contentType

	// Copy the file to GCS
	if _, err = io.Copy(writer, src); err != nil {
		return "", fmt.Errorf("failed to upload file: %v", err)
	}

	// Clean up temporary PDF file if it was created
	if pdfPath != filePath && fileToUpload == pdfPath {
		if err := os.Remove(pdfPath); err != nil {
			log.Printf("Warning: failed to remove temporary PDF file: %v", err)
		}
	}

	// Get the public URL
	url := fmt.Sprintf("https://storage.googleapis.com/%s/%s", g.bucketName, objectName)
	return url, nil
}

// *****************  2. GET A SIGNED URL FOR ACCESSING A FILE FOR A LIMITED TIME ********************
// ------------ GetSignedURL generates a signed URL for accessing a file for a limited time ------------
func (g *GoogleCloudStorage) GetSignedURL(ctx context.Context, objectName string, expires time.Duration) (string, error) {
	if g.serviceAccountEmail == "" {
		return "", fmt.Errorf("storage: missing required GoogleAccessID")
	}

	if len(g.privateKey) == 0 {
		return "", fmt.Errorf("storage: missing required PrivateKey")
	}

	opts := &storage.SignedURLOptions{
		GoogleAccessID: g.serviceAccountEmail,
		PrivateKey:     g.privateKey,
		Method:         http.MethodGet,
		Expires:        time.Now().Add(expires),
	}

	url, err := storage.SignedURL(g.bucketName, objectName, opts)
	if err != nil {
		return "", fmt.Errorf("failed to generate signed URL: %v", err)
	}

	return url, nil
}

// *****************  3. DELETE A FILE FROM GOOGLE CLOUD STORAGE ********************
// ------------ DeleteFile deletes a file from Google Cloud Storage ------------
func (g *GoogleCloudStorage) DeleteFile(ctx context.Context, objectName string) error {
	bucket := g.client.Bucket(g.bucketName)
	obj := bucket.Object(objectName)

	if err := obj.Delete(ctx); err != nil {
		return fmt.Errorf("failed to delete file: %v", err)
	}

	return nil
}

// *****************  4. DOWNLOAD A FILE FROM GOOGLE CLOUD STORAGE TO A LOCAL PATH ********************
// ------------ DownloadFile downloads a file from Google Cloud Storage to a local path ------------
func (gcs *GoogleCloudStorage) DownloadFile(ctx context.Context, fileURL string, destPath string) error {
	// Extract the object name from the URL
	objectName := ExtractObjectNameFromURL(fileURL)
	if objectName == "" {
		return fmt.Errorf("invalid file URL format: %s", fileURL)
	}

	// Get the object handle
	obj := gcs.bucket.Object(objectName)

	// Create the destination file
	destFile, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %v", err)
	}
	defer destFile.Close()

	// Download the object
	reader, err := obj.NewReader(ctx)
	if err != nil {
		return fmt.Errorf("failed to create object reader: %v", err)
	}
	defer reader.Close()

	// Copy the content to the destination file
	if _, err := io.Copy(destFile, reader); err != nil {
		return fmt.Errorf("failed to copy object content: %v", err)
	}

	return nil
}

// ------------ ExtractObjectNameFromURL extracts the object name from a GCS URL ------------
func ExtractObjectNameFromURL(url string) string {
	// Extract the object name from a GCS URL
	// Format: https://storage.googleapis.com/bucket-name/object-name
	parts := strings.Split(url, "storage.googleapis.com/")
	if len(parts) < 2 {
		return ""
	}

	// Split by the bucket name to get the object name
	objectPath := parts[1]
	bucketName := os.Getenv("GCS_BUCKET_NAME")

	// Remove the bucket name if it's at the beginning of the path
	objectName := strings.TrimPrefix(objectPath, bucketName+"/")

	return objectName
}

// *****************  5. CLOSES THE STORAGE CLIENT ********************
// ------------ Close closes the storage client ------------
func (g *GoogleCloudStorage) Close() error {
	return g.client.Close()
}

// *****************  6. GENERATES A THUMBNAIL FOR A FILE AND RETURNS THE THUMBNAIL URL ********************
// ------------ GenerateThumbnail generates a thumbnail for a file and returns the thumbnail URL ------------
// This is now an asynchronous operation that returns immediately with a pending URL
// and processes the thumbnail in the background
func (g *GoogleCloudStorage) GenerateThumbnail(ctx context.Context, objectName string) (string, error) {
	// Thumbnail path uses the 'thumbnails' directory with the same object name but ensures proper extension
	// Extract the base path without extension
	basePath := strings.TrimSuffix(objectName, filepath.Ext(objectName))
	thumbnailObjectName := "thumbnails/" + basePath + ".png"

	// Check if thumbnail already exists
	_, err := g.bucket.Object(thumbnailObjectName).Attrs(ctx)
	if err == nil {
		// Thumbnail already exists, return its URL
		signedURL, err := g.GetSignedURL(ctx, thumbnailObjectName, 24*time.Hour)
		if err != nil {
			return "", fmt.Errorf("failed to get signed URL for existing thumbnail: %v", err)
		}
		return signedURL, nil
	}

	// Check if the original file exists
	_, err = g.bucket.Object(objectName).Attrs(ctx)
	if err != nil {
		return "", fmt.Errorf("original file does not exist: %v", err)
	}

	// Create a placeholder thumbnail to indicate processing is in progress
	placeholderPath := "thumbnails/processing_placeholder.png"

	// Check if placeholder exists in bucket
	_, plErr := g.bucket.Object(placeholderPath).Attrs(ctx)
	if plErr != nil {
		// If placeholder doesn't exist, create it
		if err := g.createPlaceholderImage(ctx, placeholderPath); err != nil {
			log.Printf("Warning: Failed to create placeholder image: %v", err)
		}
	}

	// Return the placeholder URL while we process in the background
	placeholderURL, err := g.GetSignedURL(ctx, placeholderPath, 24*time.Hour)
	if err != nil {
		log.Printf("Warning: Failed to get signed URL for placeholder: %v", err)
		// Fallback to direct URL if signed URL fails
		placeholderURL = fmt.Sprintf("%s/%s", g.bucketURL, placeholderPath)
	}

	// Start asynchronous thumbnail generation
	go func() {
		backgroundCtx := context.Background()

		// Download file to temporary location
		tempDir := os.TempDir()
		tempFilePath := filepath.Join(tempDir, filepath.Base(objectName))
		destThumbnailPath := filepath.Join(tempDir, "thumb_"+filepath.Base(objectName))

		// Clean up temporary files when done
		defer os.Remove(tempFilePath)
		defer os.Remove(destThumbnailPath)

		// Download the file
		reader, err := g.bucket.Object(objectName).NewReader(backgroundCtx)
		if err != nil {
			log.Printf("Error: Failed to create reader for thumbnail generation: %v", err)
			return
		}
		defer reader.Close()

		// Create the temp file
		tempFile, err := os.Create(tempFilePath)
		if err != nil {
			log.Printf("Error: Failed to create temp file for thumbnail generation: %v", err)
			return
		}

		// Copy the content to the temp file
		if _, err := io.Copy(tempFile, reader); err != nil {
			tempFile.Close()
			log.Printf("Error: Failed to download file for thumbnail generation: %v", err)
			return
		}
		tempFile.Close()

		// Detect file type and generate thumbnail accordingly
		ext := strings.ToLower(filepath.Ext(objectName))
		var thumbnailExt string
		var thumbnailGenerated bool = false

		switch ext {
		case ".pdf":
			// Generate a thumbnail from the first page of the PDF
			thumbnailExt = ".png"
			destThumbnailPath = strings.TrimSuffix(destThumbnailPath, filepath.Ext(destThumbnailPath)) + thumbnailExt
			// Ensure thumbnail object name has .png extension
			thumbnailObjectName = strings.TrimSuffix(thumbnailObjectName, filepath.Ext(thumbnailObjectName)) + thumbnailExt

			// Use pdftoppm if available (from poppler-utils)
			_, ppmErr := exec.LookPath("pdftoppm")
			if ppmErr == nil {
				tempPngBase := filepath.Join(tempDir, "thumbnail")
				ppmCmd := exec.Command("pdftoppm",
					"-png",
					"-singlefile",
					"-f", "1", // First page
					"-r", "50", // 50 DPI resolution
					tempFilePath,
					tempPngBase)

				if err := ppmCmd.Run(); err == nil {
					// The output will be tempPngBase.png
					generatedFile := tempPngBase + ".png"
					if _, err := os.Stat(generatedFile); err == nil {
						// Copy the file to our destination
						input, err := os.ReadFile(generatedFile)
						if err == nil {
							err = os.WriteFile(destThumbnailPath, input, 0644)
							if err == nil {
								log.Printf("Successfully generated thumbnail with pdftoppm")
								thumbnailGenerated = true
							}
						}
					}
				}
			}

		case ".jpg", ".jpeg", ".png", ".gif":
			// Use ImageMagick's convert to resize images
			cmd := exec.Command("convert", tempFilePath, "-resize", "1800x>", "-gravity", "North", "-crop", "1800x600+0+0", "+repage", "-density", "300", "-quality", "100", destThumbnailPath)
			if err := cmd.Run(); err != nil {
				log.Printf("Error: Failed to generate image thumbnail: %v", err)
				// Try a simpler conversion if the first one fails
				simpleCmd := exec.Command("convert", tempFilePath, "-resize", "800x600>", destThumbnailPath)
				if err := simpleCmd.Run(); err != nil {
					log.Printf("Error: Failed to generate simple image thumbnail: %v", err)
				} else {
					thumbnailGenerated = true
				}
			} else {
				thumbnailGenerated = true
			}
			thumbnailExt = ext
			// For consistency, ensure all thumbnails have .png extension
			thumbnailObjectName = strings.TrimSuffix(thumbnailObjectName, filepath.Ext(thumbnailObjectName)) + ".png"

		default:
			// For unsupported file types, use a generic placeholder
			if err := g.copyPlaceholderToThumbnail(backgroundCtx, thumbnailObjectName); err != nil {
				log.Printf("Error: Failed to copy placeholder for unsupported file type: %v", err)
			}
			return
		}

		// Check if thumbnail was created
		if !thumbnailGenerated || os.IsNotExist(func() error {
			_, err := os.Stat(destThumbnailPath)
			return err
		}()) {
			log.Printf("Error: Thumbnail generation failed, output file not found")
			if err := g.copyPlaceholderToThumbnail(backgroundCtx, thumbnailObjectName); err != nil {
				log.Printf("Error: Failed to copy placeholder after generation failure: %v", err)
			}
			return
		}

		// Upload the thumbnail to storage
		file, err := os.Open(destThumbnailPath)
		if err != nil {
			log.Printf("Error: Failed to open thumbnail file: %v", err)
			return
		}
		defer file.Close()

		// Create writer for the thumbnail object
		writer := g.bucket.Object(thumbnailObjectName).NewWriter(backgroundCtx)
		writer.ContentType = "image/png"
		writer.CacheControl = "public, max-age=86400" // 1 day cache

		// Copy the thumbnail to the bucket
		if _, err := io.Copy(writer, file); err != nil {
			log.Printf("Error: Failed to upload thumbnail: %v", err)
			writer.Close()
			return
		}

		if err := writer.Close(); err != nil {
			log.Printf("Error: Failed to finalize thumbnail upload: %v", err)
			return
		}

		log.Printf("Successfully generated and uploaded thumbnail for %s", objectName)
	}()

	// Return the placeholder URL immediately while processing happens in background
	return placeholderURL, nil
}

// *****************  7. GET THE URL OF A THUMBNAIL FOR A GIVEN FILE ********************
// GetThumbnailURL returns the URL of a thumbnail for a given file
func (g *GoogleCloudStorage) GetThumbnailURL(ctx context.Context, objectName string) (string, error) {
	if objectName == "" {
		return "", fmt.Errorf("empty object name provided")
	}

	// First check if the thumbnail exists with .png extension
	basePath := strings.TrimSuffix(objectName, filepath.Ext(objectName))
	thumbnailObjectName := "thumbnails/" + basePath + ".png"

	// Try to get attributes to see if the thumbnail exists
	_, err := g.bucket.Object(thumbnailObjectName).Attrs(ctx)
	if err == nil {
		// Thumbnail exists, return a signed URL
		signedURL, err := g.GetSignedURL(ctx, thumbnailObjectName, 24*time.Hour)
		if err != nil {
			return "", fmt.Errorf("failed to get signed URL for existing thumbnail: %v", err)
		}
		return signedURL, nil
	}

	// Check if the original file exists before attempting to generate a thumbnail
	_, err = g.bucket.Object(objectName).Attrs(ctx)
	if err != nil {
		return "", fmt.Errorf("original file does not exist: %v", err)
	}

	// If not, generate a new thumbnail (this is now asynchronous)
	return g.GenerateThumbnail(ctx, objectName)
}

// *****************  8. CONVERTS A FILE TO PDF WITHOUT UPLOADING ********************
// ConvertToPDF explicitly converts a file to PDF without uploading to storage
func (g *GoogleCloudStorage) ConvertToPDF(ctx context.Context, file *multipart.FileHeader) (string, error) {
	// Check if file is already a PDF
	if isPDF(file.Filename) {
		return "", fmt.Errorf("file is already a PDF")
	}

	// Check if conversion is supported
	if !isConvertibleFormat(file.Filename) {
		return "", fmt.Errorf("file format not supported for conversion: %s", filepath.Ext(file.Filename))
	}

	// Convert to PDF
	pdfBytes, pdfFilename, err := g.converter.ConvertToPDF(file)
	if err != nil {
		return "", fmt.Errorf("conversion failed: %v", err)
	}

	// Create a temporary file for the PDF output
	tempDir := os.TempDir()
	pdfPath := filepath.Join(tempDir, pdfFilename)

	if err := os.WriteFile(pdfPath, pdfBytes, 0644); err != nil {
		return "", fmt.Errorf("failed to write PDF file: %v", err)
	}

	return pdfPath, nil
}

// *****************  9. HELPER FUNCTIONS ********************
// Note: isPDF and isConvertibleFormat are defined in converter.go

// Helper function to create a placeholder image
func (g *GoogleCloudStorage) createPlaceholderImage(ctx context.Context, placeholderPath string) error {
	// Create a simple 1x1 transparent PNG
	placeholderData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
		0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
		0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
		0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
		0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41,
		0x54, 0x08, 0xD7, 0x63, 0x60, 0x00, 0x00, 0x00,
		0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33, 0x00,
		0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
		0x42, 0x60, 0x82,
	}

	writer := g.bucket.Object(placeholderPath).NewWriter(ctx)
	writer.ContentType = "image/png"
	writer.CacheControl = "public, max-age=86400"

	if _, err := writer.Write(placeholderData); err != nil {
		writer.Close()
		return fmt.Errorf("failed to write placeholder image: %v", err)
	}

	if err := writer.Close(); err != nil {
		return fmt.Errorf("failed to close placeholder image writer: %v", err)
	}

	return nil
}

// Helper function to copy placeholder to thumbnail location
func (g *GoogleCloudStorage) copyPlaceholderToThumbnail(ctx context.Context, thumbnailObjectName string) error {
	placeholderPath := "thumbnails/processing_placeholder.png"
	src := g.bucket.Object(placeholderPath)
	dst := g.bucket.Object(thumbnailObjectName)

	if _, err := dst.CopierFrom(src).Run(ctx); err != nil {
		return fmt.Errorf("failed to copy placeholder: %v", err)
	}

	return nil
}

// Helper function to determine content type from file extension
func getContentType(ext string) string {
	switch strings.ToLower(ext) {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	default:
		return "application/octet-stream"
	}
}
