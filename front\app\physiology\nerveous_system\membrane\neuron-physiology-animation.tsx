"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import NeuronSvgComponent from "@/app/physiology/nerveous_system/neuron/neuron_image"

export default function NeuronPhysiologyAnimation() {
  const [neuronState, setNeuronState] = useState("resting") // "resting" or "firing"
  const [axonProgress, setAxonProgress] = useState(0) // 0-100 for propagation
  const [voltage, setVoltage] = useState(-70); // Resting potential in mV
  const threshold = -55; // Threshold for action potential in mV
  const animationFrameId = useRef<number | null>(null)
  const lastFrameTime = useRef<number | null>(null)
  const animationDuration = 5000; // 5 seconds for a full propagation cycle in ms

  // Animation loop for action potential propagation along axon
  const animatePropagation = (currentTime: number) => {
    if (lastFrameTime.current === null) {
      lastFrameTime.current = currentTime
    }

    const deltaTime = currentTime - lastFrameTime.current
    lastFrameTime.current = currentTime

    if (neuronState === "firing") {
      setAxonProgress((prevProgress) => {
        let newProgress = prevProgress + (deltaTime / animationDuration) * 100
        if (newProgress > 100) {
          // Reset for continuous looping demonstration
          newProgress = 0;
        }
        return newProgress
      })
    } else {
      // If resting, progress should be 0
      setAxonProgress(0);
    }

    animationFrameId.current = requestAnimationFrame(animatePropagation)
  }

  useEffect(() => {
    if (neuronState === "firing") {
      lastFrameTime.current = null; // Reset time on start of a new propagation
      animationFrameId.current = requestAnimationFrame(animatePropagation)
    } else {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
      setAxonProgress(0); // Ensure progress resets when not firing
    }

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [neuronState])

  const handleStimulate = () => {
    // Simulate a brief depolarization that might trigger an action potential
    setVoltage((prevVoltage) => {
      const newVoltage = prevVoltage + 20; // Increase voltage temporarily
      if (newVoltage >= threshold) {
        setNeuronState("firing");
      }
      return newVoltage;
    });
  };

  const handleReset = () => {
    setNeuronState("resting")
    setAxonProgress(0)
    setVoltage(-70); // Reset voltage to resting potential
  }

  return (
    <div className="relative bg-[hsl(240_10%_3.9%)] rounded-lg shadow-lg p-4 md:p-6 border border-gray-800 mb-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Full Neuron Physiology Simulation</h1>
        <p className="text-gray-400">Explore the complete journey of a neural impulse.</p>
      </div>

      <div className="flex flex-col items-center justify-center space-y-8">
        {/* Neuron Structure */}
        <div className="relative w-full aspect-w-16 aspect-h-9 bg-gray-950 rounded-lg flex items-center justify-center p-4">
          <NeuronSvgComponent className="w-full h-full object-contain" neuronState={neuronState} axonProgress={axonProgress} />

          {/* Action Potential Propagation */}
          {/* Removed old AP and Na+ visual indicators */}

        </div>

        <p className="text-gray-300 text-lg">Neuron State: <span className="font-semibold text-white">
          {neuronState === "resting" ? "Resting (Ready for Impulse)" : "Firing (Impulse Propagating)"}
        </span></p>

        <div className="flex flex-col items-center space-y-4 w-full max-w-sm">
          {/* Voltage Control */}
          <div className="w-full">
            <label htmlFor="voltage-slider" className="text-gray-400 text-sm block mb-2">Membrane Voltage: {voltage}mV</label>
            <input
              id="voltage-slider"
              type="range"
              min="-90"
              max="30"
              step="1"
              value={voltage}
              onChange={(e) => {
                const newVoltage = parseInt(e.target.value);
                setVoltage(newVoltage);
                if (newVoltage >= threshold && neuronState === "resting") {
                  setNeuronState("firing");
                } else if (newVoltage < threshold && neuronState === "firing") {
                  // This ensures that if voltage drops below threshold, neuron resets
                  setNeuronState("resting");
                  setAxonProgress(0);
                }
              }}
              className="w-full h-2 rounded-lg appearance-none cursor-pointer bg-gray-700 accent-blue-500"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>-90mV (Resting)</span>
              <span>{threshold}mV (Threshold)</span>
              <span>+30mV (Peak)</span>
            </div>
          </div>

          <button
            onClick={neuronState === "resting" ? handleStimulate : handleReset}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg shadow-md transition-colors duration-200"
          >
            {neuronState === "resting" ? "Stimulate Neuron" : "Reset Simulation"}
          </button>
        </div>

        {/* Progress bar below the button */}
        <div className="w-full max-w-4xl bg-gray-800 rounded-full h-2 mt-4">
          <motion.div
            className="h-full bg-purple-500 rounded-full"
            style={{ width: `${axonProgress}%` }}
          />
        </div>
      </div>
    </div>
  )
} 