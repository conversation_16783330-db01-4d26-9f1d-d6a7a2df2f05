# Generated by Django 5.1.3 on 2024-12-05 03:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0006_usersubscription_platform_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='usersubscription',
            name='platform',
        ),
        migrations.RemoveField(
            model_name='usersubscription',
            name='revenuecat_customer_id',
        ),
        migrations.AlterField(
            model_name='usersubscription',
            name='subscription_type',
            field=models.CharField(choices=[('monthly', 'Monthly Subscription'), ('yearly', 'Yearly Subscription')], default='none', max_length=50),
        ),
    ]
