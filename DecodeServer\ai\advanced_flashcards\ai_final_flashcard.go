package advanced_flashcards

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"strings"
	"time"
)

// Flashcard represents a flashcard with a question, answer, and optional image
type Flashcard struct {
	Question    string `json:"question"`
	Answer      string `json:"answer"`
	ImageBase64 string `json:"image_base64,omitempty"`
}

// FlashcardsResponse contains generated flashcards and metadata
type FlashcardsResponse struct {
	Flashcards []Flashcard `json:"flashcards"`
	CreatedAt  string      `json:"created_at"`
	ID         string      `json:"id"`
	DocumentID string      `json:"document_id"`
}

// DocumentOverview represents the structure of the input JSON file
type DocumentOverview struct {
	ID        string         `json:"id"`
	CreatedAt string         `json:"created_at"`
	Pages     []DocumentPage `json:"pages"`
}

// DocumentPage represents a page in the document
type DocumentPage struct {
	Content string `json:"content"`
	Images  []struct {
		Base64Data string `json:"base64_data"`
	} `json:"images"`
}

// GenerateFlashcardsFromFile processes a local JSON file and returns flashcards
func GenerateFlashcardsFromFile(filePath string) (*FlashcardsResponse, error) {
	// Read the JSON file
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %v", err)
	}

	// Parse the document overview
	var overview DocumentOverview
	if err := json.Unmarshal(data, &overview); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Generate flashcards from the content
	var flashcards []Flashcard

	// Create a sample flashcard (you would replace this with actual AI processing)
	flashcards = []Flashcard{
		{
			Question: "What is the main topic of this medical document?",
			Answer:   "Based on the content analysis, this document discusses medical concepts and findings.",
		},
	}

	// Add content-based flashcards
	for _, page := range overview.Pages {
		if len(page.Content) > 0 {
			// Create a flashcard from the page content
			card := Flashcard{
				Question: fmt.Sprintf("What are the key points discussed in this section?"),
				Answer:   summarizeContent(page.Content),
			}

			// If the page has images, add the first one to the flashcard
			if len(page.Images) > 0 {
				card.ImageBase64 = page.Images[0].Base64Data
			}

			flashcards = append(flashcards, card)
		}
	}

	// Create response with metadata
	response := &FlashcardsResponse{
		Flashcards: flashcards,
		CreatedAt:  time.Now().Format(time.RFC3339),
		ID:         fmt.Sprintf("flashcards-%d", time.Now().UnixNano()),
		DocumentID: overview.ID,
	}

	return response, nil
}

// summarizeContent creates a simple summary of the content
func summarizeContent(content string) string {
	// Split content into sentences
	sentences := strings.Split(content, ".")

	// Take up to first 3 sentences
	summary := []string{}
	for i := 0; i < len(sentences) && i < 3; i++ {
		if trimmed := strings.TrimSpace(sentences[i]); len(trimmed) > 0 {
			summary = append(summary, trimmed)
		}
	}

	return strings.Join(summary, ". ") + "."
}

// SaveFlashcardsToFile saves the flashcards response to a JSON file in the specified directory
func (resp *FlashcardsResponse) SaveFlashcardsToFile(directory string) (string, error) {
	// Create the directory if it doesn't exist
	if err := os.MkdirAll(directory, 0755); err != nil {
		return "", fmt.Errorf("failed to create directory: %v", err)
	}

	// Create a filename based on the ID and timestamp
	filename := fmt.Sprintf("flashcards_%s.json", resp.ID)
	filepath := path.Join(directory, filename)

	// Marshal the response to JSON
	jsonData, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal flashcards to JSON: %v", err)
	}

	// Write the file
	if err := os.WriteFile(filepath, jsonData, 0644); err != nil {
		return "", fmt.Errorf("failed to write file: %v", err)
	}

	return filepath, nil
}
