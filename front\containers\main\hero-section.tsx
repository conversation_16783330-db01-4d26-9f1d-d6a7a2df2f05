'use client'
import React from 'react'
import Link from 'next/link'
import { ArrowRight, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TextEffect } from '@/components/motion-primitives/text-effect'
import { AnimatedGroup } from '@/components/motion-primitives/animated-group'
import { HeroHeader } from '@/containers/main/hero5-header'
import { MainThemeProvider } from '@/components/theme/main-theme-provider'
import { useMainTheme } from '@/components/theme/main-theme-provider'
import { useLanguage } from '@/app/providers/language-provider'
import { SignUpButton } from "@clerk/nextjs";

const transitionVariants = {
    item: {
        hidden: {
            opacity: 0,
            filter: 'blur(12px)',
            y: 12,
        },
        visible: {
            opacity: 1,
            filter: 'blur(0px)',
            y: 0,
            transition: {
                type: 'spring',
                bounce: 0.3,
                duration: 1.5,
            },
        },
    },
}

export default function HeroSection() {
    return (
        <MainThemeProvider>
            <HeroContent />
        </MainThemeProvider>
    )
}

function HeroContent() {
    const { theme } = useMainTheme()
    const { t } = useLanguage()
    const isDarkMode = theme === 'dark'
    
    return (
        <>
            <HeroHeader />
            <main className="overflow-hidden dark:bg-[hsl(240_10%_3.9%)]">
                {/* Diagonal light */}
                <div
                    aria-hidden
                    className="absolute inset-0 isolate opacity-40 hidden lg:block">
                    <div className="absolute left-0 top-0 w-[560px] h-[1280px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.04)_0,hsla(0,0%,55%,.01)_50%,hsla(0,0%,45%,0)_80%)]" />
                    <div className="absolute left-0 top-0 w-[240px] h-[1280px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.03)_0,hsla(0,0%,45%,.01)_80%,transparent_100%)] translate-x-[5%] -translate-y-[50%]" />
                    <div className="absolute left-0 top-0 w-[240px] h-[1280px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.02)_0,hsla(0,0%,45%,.01)_80%,transparent_100%)]" />
                </div>
                <section>
                    <div className="relative pt-20 sm:pt-24 md:pt-36">
                        {/*------------------- Background-------------------- */}
                        <AnimatedGroup
                            variants={{
                                container: {
                                    visible: {
                                        transition: {
                                            delayChildren: 1,
                                        },
                                    },
                                },
                                item: {
                                    hidden: {
                                        opacity: 0,
                                        y: 20,
                                    },
                                    visible: {
                                        opacity: 1,
                                        y: 0,
                                        transition: {
                                            type: 'spring',
                                            bounce: 0.3,
                                            duration: 2,
                                        },
                                    },
                                },
                            }}
                            className="absolute inset-0 -z-20">
                            <div className="absolute inset-0 bg-gradient-to-b from-background/10 to-background/20" />
                        </AnimatedGroup>
                        {/* ------------Background----------------*/}


                        {/* Background for enhanced visibility */}
                        <div className="absolute inset-0 -z-10 size-full [background:radial-gradient(125%_125%_at_50%_100%,transparent_0%,var(--color-background)_75%)]"></div>
                        <div className="mx-auto max-w-7xl px-4 sm:px-6">
                            <div className="text-center sm:mx-auto lg:mr-auto lg:mt-0">
                                <AnimatedGroup variants={transitionVariants}>
                                    <Link
                                        href="#link"
                                        className="hover:bg-background dark:hover:border-t-border bg-muted group mx-auto flex w-fit items-center gap-2 sm:gap-4 rounded-full border p-1 pl-2 sm:pl-4 shadow-md shadow-zinc-950/5 transition-colors duration-300 dark:border-t-white/5 dark:shadow-zinc-950">
                                        <span className="text-gray-800 text-xs sm:text-sm dark:text-gray-200">{t('hero.aiEvidenceSearch')}</span>
                                        <span className="border-gray-300 dark:border-background block h-4 w-0.5 border-l bg-gray-400  dark:bg-neutral-700"></span>

                                        <div className="bg-background group-hover:bg-muted size-5 sm:size-6 overflow-hidden rounded-full duration-500">
                                            <div className="flex w-10 sm:w-12 -translate-x-1/2 duration-500 ease-in-out group-hover:translate-x-0">
                                                <span className="flex size-5 sm:size-6">
                                                    <ArrowRight className="m-auto size-2 sm:size-3 text-gray-800 dark:text-gray-200" />
                                                </span>
                                                <span className="flex size-5 sm:size-6">
                                                    <ArrowRight className="m-auto size-2 sm:size-3 text-gray-800 dark:text-gray-200" />
                                                </span>
                                            </div>
                                        </div>
                                    </Link>
                                </AnimatedGroup>

                                <TextEffect
                                    preset="fade-in-blur"
                                    speedSegment={0.3}
                                    as="h1"
                                    className="mt-6 sm:mt-8 text-balance text-gray-900 dark:text-gray-100 text-4xl sm:text-5xl md:text-6xl lg:text-7xl lg:mt-16 xl:text-[5.25rem]">
                                    {t('hero.title')}
                                </TextEffect>
                                <TextEffect
                                    per="line"
                                    preset="fade-in-blur"
                                    speedSegment={0.3}
                                    delay={0.5}
                                    as="p"
                                    className="mx-auto mt-4 sm:mt-6 md:mt-8 max-w-2xl text-balance text-gray-700 dark:text-gray-300 text-base sm:text-lg">
                                    {t('hero.subtitle')}
                                </TextEffect>

                                <AnimatedGroup
                                    variants={{
                                        container: {
                                            visible: {
                                                transition: {
                                                    staggerChildren: 0.05,
                                                    delayChildren: 0.75,
                                                },
                                            },
                                        },
                                        ...transitionVariants,
                                    }}
                                    className="mt-8 sm:mt-10 md:mt-12 flex flex-col items-center justify-center gap-2 md:flex-row">
                                    <div
                                        key={1}
                                        className="bg-foreground/10 rounded-[calc(var(--radius-xl)+0.125rem)] border p-0.5 w-full max-w-xs sm:w-auto">
                                        <SignUpButton>
                                            <Button
                                                size="lg"
                                                className="rounded-xl px-4 sm:px-5 text-sm sm:text-base w-full sm:w-auto">
                                                <span className="text-nowrap">{t('hero.tryForFree')}</span>
                                            </Button>
                                        </SignUpButton>
                                    </div>
                                  
                                </AnimatedGroup>
                            </div>
                        </div>

                        <AnimatedGroup
                            variants={{
                                container: {
                                    visible: {
                                        transition: {
                                            staggerChildren: 0.05,
                                            delayChildren: 0.75,
                                        },
                                    },
                                },
                                ...transitionVariants,
                            }}>
                            <div className="relative mt-8 overflow-hidden px-2 sm:mt-12 md:mt-20">
                                
                                <div className="inset-shadow-2xs opacity-90 dark:ring-[hsl(240_10%_3.9%)] dark:inset-shadow-white/20 bg-background dark:bg-[hsl(240_10%_3.9%)] relative mx-auto max-w-6xl overflow-hidden rounded-lg sm:rounded-2xl border">
                                    <video 
                                        className="aspect-16/9 w-full rounded-lg object-cover"
                                        src="/Flashcards demo.mp4"
                                        autoPlay
                                        loop
                                        muted
                                        playsInline
                                        controls={false}
                                    />
                                </div>
                            </div>
                        </AnimatedGroup>
                    </div>
                </section>
                {/*-------------------CUSTOMERS SECTION --------------------------*/}
                <section className="bg-white dark:bg-[hsl(240_10%_3.9%)] mt-16 sm:mt-20 md:mt-28">
                    <div className="group relative m-auto max-w-5xl px-4 sm:px-6">
                        <div className="absolute inset-0 z-10 flex scale-95 items-center justify-center opacity-0 duration-500 group-hover:scale-100 group-hover:opacity-100">
                            <Link
                                href="/"
                                className="block text-sm text-gray-900 dark:text-gray-100 duration-150 hover:opacity-75">
                                <span>{t('hero.trustedBy')}</span>

                                <ChevronRight className="ml-1 inline-block size-3" />
                            </Link>
                        </div>
                        <h2 className="text-center text-xl sm:text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 mb-6 sm:mb-8">{t('hero.trustedByTitle')}</h2>
                        <div className="group-hover:blur-xs mx-auto mt-4 sm:mt-6 grid max-w-3xl grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 sm:gap-x-6 md:gap-x-8 gap-y-8 sm:gap-y-10 transition-all duration-500 group-hover:opacity-50">
                            <div className="flex flex-col items-center transition-transform hover:scale-105">
                                <div className={`rounded-full p-3 sm:p-4 mb-3 sm:mb-4 ${isDarkMode ? 'bg-primary/20' : 'bg-primary/10'}`}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-6 w-6 sm:h-8 sm:w-8">
                                        <path d="M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                                        <path d="M8 7V3m8 4V3"></path>
                                        <path d="M12 12v3"></path>
                                        <path d="M9 13h6"></path>
                                    </svg>
                                </div>
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-gray-100">{t('hero.medicalStudentsTitle')}</h3>
                                <p className="text-center text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base">{t('hero.medicalStudentsDesc')}</p>
                            </div>
                            
                            <div className="flex flex-col items-center transition-transform hover:scale-105">
                                <div className={`rounded-full p-3 sm:p-4 mb-3 sm:mb-4 ${isDarkMode ? 'bg-primary/20' : 'bg-primary/10'}`}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-6 w-6 sm:h-8 sm:w-8">
                                        <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path>
                                        <path d="M12 5 9.04 7.96a2.17 2.17 0 0 0 0 3.08v0c.82.82 2.13.85 3 .07l2.07-1.9a2.82 2.82 0 0 1 3.79 0l2.96 2.66"></path>
                                        <path d="m18 15-2-2"></path>
                                        <path d="m15 18-2-2"></path>
                                    </svg>
                                </div>
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-gray-100">{t('hero.healthcareProfessionalsTitle')}</h3>
                                <p className="text-center text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base">{t('hero.healthcareProfessionalsDesc')}</p>
                            </div>
                            
                            <div className="flex flex-col items-center transition-transform hover:scale-105">
                                <div className={`rounded-full p-3 sm:p-4 mb-3 sm:mb-4 ${isDarkMode ? 'bg-primary/20' : 'bg-primary/10'}`}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-6 w-6 sm:h-8 sm:w-8">
                                        <path d="M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3"></path>
                                        <path d="M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4"></path>
                                        <circle cx="20" cy="10" r="2"></circle>
                                    </svg>
                                </div>
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-gray-100">{t('hero.doctorsTitle')}</h3>
                                <p className="text-center text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base">{t('hero.doctorsDesc')}</p>
                            </div>
                        </div>
                    </div>
                   
                </section>
            </main>
        </>
    )
}
