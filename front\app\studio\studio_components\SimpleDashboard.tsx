'use client';

import React, { useState } from 'react';
import { Folder, FolderPlus, Loader2 } from 'lucide-react';

export interface Project {
  id: number;
  name: string;
  description?: string;
  created_at: string;
}

export interface MedSpace {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  projects?: Project[];
  isExpanded?: boolean;
  thumbnailStatus?: 'loading' | 'ready' | 'error';
}

export default function SimpleDashboard() {
  const [medSpaces, setMedSpaces] = useState<MedSpace[]>([]);
  const [isCreatingMedSpace, setIsCreatingMedSpace] = useState(false);
  const [newMedSpaceName, setNewMedSpaceName] = useState('');
  const [newMedSpaceDescription, setNewMedSpaceDescription] = useState('');
  const [loading, setLoading] = useState(false);
  
  const createMedSpace = async () => {
    if (!newMedSpaceName.trim()) {
      console.error('MedSpace name is required');
      return;
    }
    
    try {
      setLoading(true);
      
      // Mock API call - in a real app, this would fetch from the backend
      setTimeout(() => {
        const newMedSpace: MedSpace = {
          id: Date.now(),
          name: newMedSpaceName,
          description: newMedSpaceDescription,
          created_at: new Date().toISOString(),
          projects: []
        };
        
        setMedSpaces(prev => [...prev, newMedSpace]);
        
        // Reset form
        setNewMedSpaceName('');
        setNewMedSpaceDescription('');
        setIsCreatingMedSpace(false);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error creating medspace:', error);
      setLoading(false);
    }
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">MedSpaces</h1>
      
      <button
        onClick={() => setIsCreatingMedSpace(true)}
        className="p-2 rounded-full bg-blue-500 text-white"
      >
        <FolderPlus size={18} />
      </button>
      
      {isCreatingMedSpace && (
        <div className="mt-4 p-4 border rounded">
          <h3 className="text-lg font-medium mb-3">Create New MedSpace</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium">Name</label>
              <input
                type="text"
                value={newMedSpaceName}
                onChange={(e) => setNewMedSpaceName(e.target.value)}
                className="mt-1 block w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium">Description</label>
              <textarea
                value={newMedSpaceDescription}
                onChange={(e) => setNewMedSpaceDescription(e.target.value)}
                className="mt-1 block w-full p-2 border rounded"
                rows={3}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setIsCreatingMedSpace(false);
                  setNewMedSpaceName('');
                  setNewMedSpaceDescription('');
                }}
                className="px-3 py-1 rounded bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={createMedSpace}
                disabled={!newMedSpaceName.trim() || loading}
                className="px-3 py-1 rounded bg-blue-500 text-white disabled:bg-gray-300"
              >
                {loading ? (
                  <span className="flex items-center">
                    <Loader2 size={16} className="animate-spin mr-2" />
                    Creating...
                  </span>
                ) : 'Create MedSpace'}
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="mt-4">
        {medSpaces.length > 0 ? (
          <ul className="space-y-2">
            {medSpaces.map(medSpace => (
              <li key={medSpace.id} className="p-3 border rounded flex items-center">
                <Folder size={20} className="mr-3 text-blue-500" />
                <span className="font-medium">{medSpace.name}</span>
              </li>
            ))}
          </ul>
        ) : (
          <div className="p-4 text-gray-500 border rounded">
            No MedSpaces found. Create one to better organize your projects!
          </div>
        )}
      </div>
    </div>
  );
} 