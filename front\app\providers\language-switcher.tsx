"use client"

import { useLanguage } from "@/app/providers/language-provider"

import { useState } from 'react'
import { ChevronDown } from 'lucide-react'
import type { Locale } from '@/app/utils/types/language'

export function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)

  const languages = {
    en: { name: 'English', flag: '🇺🇸' },
    es: { name: 'Español', flag: '🇪🇸' },
    pt: { name: 'Português', flag: '🇧🇷' }
  }

  const currentLanguage = languages[language as keyof typeof languages]

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 text-base font-medium text-gray-300 hover:text-white hover:bg-[#111827] px-3 py-2 rounded-xl transition-all duration-300"
        aria-label="Change language"
      >
        {currentLanguage.flag} {currentLanguage.name}
        <ChevronDown className="w-4 h-4" />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-[#111827] rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
          {Object.entries(languages).map(([code, { name, flag }]) => (
            <button
              key={code}
              onClick={() => {
                setLanguage(code as Locale)
                setIsOpen(false)
              }}
              className={`w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                language === code ? 'bg-gray-100 dark:bg-gray-700' : ''
              }`}
            >
              {flag} {name}
            </button>
          ))}
        </div>
      )}
    </div>
  )
} 