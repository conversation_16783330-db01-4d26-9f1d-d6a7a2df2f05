"use client"

import { createContext, useContext, useEffect, useState } from "react"

type Theme = "dark" | "light"

type MainThemeProviderProps = {
  children: React.ReactNode
}

type MainThemeProviderState = {
  theme: Theme
  toggleTheme: () => void
}

const MainThemeProviderContext = createContext<MainThemeProviderState | undefined>(undefined)

export function MainThemeProvider({
  children,
}: MainThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>("light")
  const [mounted, setMounted] = useState(false)

  // Auto-detect user's preferred color scheme on initial load
  useEffect(() => {
    setMounted(true)
    // Check if user prefers dark mode
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
    setTheme(prefersDark ? "dark" : "light")
    
    // Listen for changes to color scheme preference
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    const handleChange = (e: MediaQueryListEvent) => {
      setTheme(e.matches ? "dark" : "light")
    }
    
    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  // Apply the theme to the document when it changes
  useEffect(() => {
    if (!mounted) return
    
    const root = window.document.documentElement
    root.classList.remove("light", "dark")
    root.classList.add(theme)
    
    if (theme === 'dark') {
      // Apply our specific dark background for main pages
      const darkBg = "hsl(240 10% 3.9%)"
      root.style.setProperty('--main-background', darkBg)
      root.style.setProperty('--main-text', 'hsl(0 0% 95%)')
    } else {
      // Light mode background
      const lightBg = "#ffffff"
      root.style.setProperty('--main-background', lightBg)
      root.style.setProperty('--main-text', 'hsl(0 0% 10%)')
    }
  }, [theme, mounted])

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light")
  }

  // Prevent flash of incorrect theme
  if (!mounted) {
    return null
  }

  return (
    <MainThemeProviderContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </MainThemeProviderContext.Provider>
  )
}

export const useMainTheme = () => {
  const context = useContext(MainThemeProviderContext)
  if (context === undefined) {
    throw new Error("useMainTheme must be used within a MainThemeProvider")
  }
  return context
} 