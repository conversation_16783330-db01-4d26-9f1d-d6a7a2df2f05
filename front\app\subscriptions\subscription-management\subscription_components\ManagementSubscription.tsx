import { CreditCard, Activity, Sparkles } from 'lucide-react';

interface ManagementSubscriptionProps {
  usage: {
    subscription_type: 'monthly' | 'yearly' | 'none';
    status: string;
    current_period_end: string | null;
    will_cancel: boolean;
    created_at: string;
    updated_at: string;
    search: { usage: number; limit: number };
    chat: { usage: number; limit: number };
  };
}

const getSubscriptionPlanDisplay = (type: 'monthly' | 'yearly' | 'none', status: string) => {
  if (status !== 'active' && status !== 'trialing') return 'Free Trial';
  if (type === 'monthly') return 'Monthly';
  if (type === 'yearly') return 'Yearly';
  return 'Free Trial';
};

const ManagementSubscription = ({ usage }: ManagementSubscriptionProps) => {

  const getSubscriptionStatusDisplay = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'trialing':
        return 'Trial';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Cancelled';
      case 'unpaid':
        return 'Unpaid';
      case 'incomplete':
        return 'Incomplete';
      case 'incomplete_expired':
        return 'Expired';
      default:
        return 'Free Trial';
    }
  };

  const formatDate = (date: string | null) => {
    if (!date) return 'N/A';
    const parsedDate = new Date(date);
    return parsedDate.toLocaleDateString();
  };

  return (
    <div className="bg-[#091225] rounded-lg overflow-hidden">
      <div className="px-6 py-8 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#1b263e] to-[#091225] opacity-5"></div>
        <div className="relative z-10">
          <div className="flex items-center space-x-2 mb-6">
            <CreditCard className="w-6 h-6 text-purple-400" />
            <h3 className="text-2xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-300 via-purple-200 to-purple-300">
              Subscription Management
            </h3>
          </div>
          
          <div className="space-y-4">
            {/* Subscription Type */}
            <div className="flex items-center justify-between p-4 bg-[#1b263e] rounded-lg">
              <div className="flex items-center space-x-3">
                <CreditCard className="w-5 h-5 text-purple-400" />
                <span className="text-gray-200">Subscription Type</span>
              </div>
              <div className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-purple-400" />
                <span className="text-gray-200">
                  {getSubscriptionPlanDisplay(usage.subscription_type, usage.status)}
                </span>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center justify-between p-4 bg-[#1b263e] rounded-lg">
              <div className="flex items-center space-x-3">
                <Activity className="w-5 h-5 text-purple-400" />
                <span className="text-gray-200">Status</span>
              </div>
              <div className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-purple-400" />
                <span className={`capitalize ${
                  usage.status === 'active' || usage.status === 'trialing' ? 'text-green-400' :
                  usage.status === 'past_due' ? 'text-yellow-400' :
                  'text-red-400'
                }`}>
                  {getSubscriptionStatusDisplay(usage.status)}
                  {usage.will_cancel && " (Cancelling)"}
                </span>
              </div>
            </div>

            {/* Period End section */}
            {((usage.status === 'active' || usage.status === 'trialing') || usage.will_cancel) && (
              <div className="flex items-center justify-between p-4 bg-[#1b263e] rounded-lg">
                <div className="flex items-center space-x-3">
                  <Activity className="w-5 h-5 text-purple-400" />
                  <span className="text-gray-200">
                    {usage.will_cancel ? 'Subscription End Date' : 'Current Period End'}
                  </span>
                </div>
                <span className="text-gray-200">
                  {formatDate(usage.current_period_end)}
                </span>
              </div>
            )}

            {/* Cancellation notice */}
            {usage.will_cancel && (
              <div className="p-4 bg-[#1b263e] rounded-lg border border-yellow-400/20">
                <div className="text-yellow-400 flex items-center justify-center space-x-2">
                  <span>
                    Your subscription has been cancelled but will remain active until {formatDate(usage.current_period_end)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManagementSubscription;
