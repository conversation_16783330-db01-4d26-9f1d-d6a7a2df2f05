package async

import (
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// JobStatus represents the status of an async job
type JobStatus struct {
	Status    string      `json:"status"` // "pending", "processing", "completed", "failed"
	Progress  int         `json:"progress"`
	Result    interface{} `json:"result,omitempty"`
	Error     string      `json:"error,omitempty"`
	CreatedAt time.Time   `json:"created_at"`
}

var (
	// Global job status tracking
	JobStatuses = make(map[string]*JobStatus)
	JobMutex    sync.RWMutex

	// WebSocket connections for updates
	QuizConnections      = make(map[string]*websocket.Conn)
	MindmapConnections   = make(map[string]*websocket.Conn)
	FlashcardConnections = make(map[string]*websocket.Conn)
	ConnectionsMutex     sync.RWMutex

	// WebSocket configuration
	Upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true // In production, configure this properly
		},
	}
)

// BroadcastQuizUpdate sends a message to all connections subscribed to a project
func BroadcastQuizUpdate(projectID uint, status string, progress int) {
	message := map[string]interface{}{
		"type": "quiz_update",
		"payload": map[string]interface{}{
			"project_id": projectID,
			"status":     status,
			"progress":   progress,
		},
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		return
	}

	ConnectionsMutex.RLock()
	defer ConnectionsMutex.RUnlock()

	for _, conn := range QuizConnections {
		// Ignore errors - connections may close while sending
		_ = conn.WriteMessage(websocket.TextMessage, messageBytes)
	}
}

// BroadcastMindmapUpdate sends a message to all connections about mindmap generation progress
func BroadcastMindmapUpdate(projectID uint, status string, progress int) {
	message := map[string]interface{}{
		"type": "mindmap_update",
		"payload": map[string]interface{}{
			"project_id": projectID,
			"status":     status,
			"progress":   progress,
		},
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		return
	}

	ConnectionsMutex.RLock()
	defer ConnectionsMutex.RUnlock()

	for _, conn := range MindmapConnections {
		// Ignore errors - connections may close while sending
		_ = conn.WriteMessage(websocket.TextMessage, messageBytes)
	}
}

// BroadcastFlashcardUpdate sends a message to all connections about flashcard generation progress
func BroadcastFlashcardUpdate(projectID uint, status string, progress int) {
	message := map[string]interface{}{
		"type": "flashcard_update",
		"payload": map[string]interface{}{
			"project_id": projectID,
			"status":     status,
			"progress":   progress,
		},
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		return
	}

	ConnectionsMutex.RLock()
	defer ConnectionsMutex.RUnlock()

	for _, conn := range FlashcardConnections {
		// Ignore errors - connections may close while sending
		_ = conn.WriteMessage(websocket.TextMessage, messageBytes)
	}
}
