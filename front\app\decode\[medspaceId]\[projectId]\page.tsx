"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTheme } from '@/components/theme/theme-provider';
import NavBar from '@/components/header/nav-bar';
import LeftSidebar from '@/components/left_tab/LeftTab';
import { useAuth } from '@clerk/nextjs';
import dynamic from 'next/dynamic';
import { ChevronLeft, ChevronRight, FileText, Map, BookOpen, Loader2, Brain } from 'lucide-react';
import { useParams } from 'next/navigation';
import { API_URL } from '@/app/utils/decode_api';

// Dynamically import ViewFile and components to avoid SSR issues
const ViewFile = dynamic(() => import('@/app/decode_components/ViewFile/ViewFile'), { ssr: false });
const Summary = dynamic(() => import('@/app/decode_components/summary/summary'), { ssr: false });
const Mindmap = dynamic(() => import('@/app/decode_components/mindmap/mindmap'), { ssr: false });
const Quizzes = dynamic(() => import('@/app/decode_components/quizzes/quizzes'), { ssr: false });
const Flashcards = dynamic(() => import('@/app/decode_components/flashcards/flashcards'), { ssr: false });

// Component types for right panel
type RightPanelView = 'summary' | 'mindmap' | 'quizzes' | 'flashcards';

export default function DecodePage() {
  const { theme } = useTheme();
  const { medspaceId, projectId } = useParams();
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [viewFileVisible, setViewFileVisible] = useState(true);
  const [splitRatio, setSplitRatio] = useState(50); // 50% for each panel initially
  const [rightPanelView, setRightPanelView] = useState<RightPanelView>('summary'); // Default to summary
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isGeneratingUnits, setIsGeneratingUnits] = useState(false);
  const [generationJobId, setGenerationJobId] = useState<string | null>(null);
  const generationInitiatedRef = useRef<Set<string>>(new Set()); // Track projects where generation has been initiated
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isGeneratingFlashcards, setIsGeneratingFlashcards] = useState(false);
  const flashcardGenerationInitiatedRef = useRef<Set<string>>(new Set()); // Track projects where flashcard generation has been initiated
  const { getToken } = useAuth();
  
  // Normalize and validate projectId and medspaceId
  // Convert projectId from string | string[] to string
  const validProjectId = projectId && typeof projectId === 'string' && projectId !== 'undefined' ? projectId : null;
  // Convert medspaceId from string | string[] to string
  const validMedspaceId = medspaceId && typeof medspaceId === 'string' && medspaceId !== 'undefined' ? medspaceId : null;
  
  // Define refs for functions to avoid circular dependencies
  const handleMouseMoveRef = useRef<((e: MouseEvent) => void) | undefined>(undefined);
  const handleMouseUpRef = useRef<(() => void) | undefined>(undefined);
  
  // Define handleMouseMove with the ref
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !mainContentRef.current) return;
    
    const container = mainContentRef.current.getBoundingClientRect();
    const offsetX = e.clientX - container.left;
    const containerWidth = container.width;
    
    // Calculate percentage (clamped between 20% and 80%)
    const newRatio = Math.max(20, Math.min(80, (offsetX / containerWidth) * 100));
    setSplitRatio(newRatio);
  }, [isDragging, mainContentRef]);
  
  // Store the current version in a ref
  handleMouseMoveRef.current = handleMouseMove;
  
  // Define handleMouseUp with the ref
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleMouseMoveRef.current!);
    document.removeEventListener('mouseup', handleMouseUpRef.current!);
  }, []);
  
  // Store the current version in a ref
  handleMouseUpRef.current = handleMouseUp;
  

  
  // Cleanup event listeners
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMoveRef.current!);
      document.removeEventListener('mouseup', handleMouseUpRef.current!);
    };
  }, []);
  
  // Define functions with proper dependency resolution
  
  // Function to check if units exist after some time
  const checkIfUnitsExist = useCallback(async (projectId: string, token: string) => {
    try {
      const unitsResponse = await fetch(`${API_URL}/api/decode/units?project_id=${projectId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (unitsResponse.ok) {
        const unitsData = await unitsResponse.json();
        if (unitsData.units && unitsData.units.length > 0) {
          console.log(`Units are now available for project: ${unitsData.units.length} units`);
          setIsGeneratingUnits(false);
          return;
        }
      }
      
      // If units still don't exist, check again in 10 seconds
      setTimeout(() => {
        checkIfUnitsExist(projectId, token);
      }, 10000);
    } catch (error) {
      console.error("Error checking if units exist:", error);
      // If there's an error, stop checking
      setIsGeneratingUnits(false);
    }
  }, [setIsGeneratingUnits]);
  
  // Function to poll for generation status
  const pollGenerationStatus = useCallback(async (jobId: string, token: string) => {
    try {
      const interval = setInterval(async () => {
        const statusResponse = await fetch(`${API_URL}/api/decode/units/status/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!statusResponse.ok) {
          console.error("Failed to get unit generation status");
          clearInterval(interval);
          setIsGeneratingUnits(false);
          return;
        }
        
        const statusData = await statusResponse.json();
        console.log("Unit generation status:", statusData);
        
        if (statusData.status === 'completed') {
          clearInterval(interval);
          setIsGeneratingUnits(false);
          setGenerationJobId(null);
        } else if (statusData.status === 'failed') {
          clearInterval(interval);
          setIsGeneratingUnits(false);
          setGenerationJobId(null);
          console.error("Unit generation failed:", statusData.error);
        }
      }, 3000); // Poll every 3 seconds
      
      // Clean up interval when component unmounts
      return () => clearInterval(interval);
    } catch (error) {
      console.error("Error polling unit generation status:", error);
      setIsGeneratingUnits(false);
    }
  }, [setIsGeneratingUnits, setGenerationJobId]);
  
  // Function to generate YouTube units
  const generateYouTubeUnits = useCallback(async (projectId: string, token: string, projectData: { project?: { youtube_units_generated?: boolean } }) => {
    try {
      console.log("Generating YouTube units for project:", projectId);
      
      // First, check if project already has YouTube units generated
      if (projectData.project?.youtube_units_generated) {
        console.log("Project already has YouTube units generated according to local data");
        setIsGeneratingUnits(false);
        return;
      }
      
      const response = await fetch(`${API_URL}/api/decode/youtube/generate-units`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ project_id: parseInt(projectId) })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate YouTube units: ${response.status}`);
      }
      
      const result = await response.json();
      console.log("YouTube units generation result:", result);
      
      // If units were already generated according to backend
      if (result.already_generated) {
        console.log("Backend indicates YouTube units were already generated");
        setIsGeneratingUnits(false);
        return;
      }
      
      // If generation is already in progress
      if (result.already_in_progress) {
        console.log("Backend indicates YouTube unit generation is already in progress");
        
        // If we don't have a job ID but generation is in progress,
        // we can still show the loading indicator but can't poll for status
        setIsGeneratingUnits(true);
        
        // In 10 seconds, try checking if units are now available
        setTimeout(() => {
          checkIfUnitsExist(projectId, token);
        }, 10000);
        
        return;
      }
      
      setIsGeneratingUnits(false);
    } catch (error) {
      console.error("Error generating YouTube units:", error);
      setIsGeneratingUnits(false);
    }
  }, [setIsGeneratingUnits, checkIfUnitsExist]);
  
  // Function to generate document units
  const generateDocumentUnits = useCallback(async (projectId: string, token: string) => {
    try {
      console.log("Generating document units for project:", projectId);
      
      // Create FormData for the request
      const formData = new FormData();
      formData.append('project_id', projectId);
      
      const response = await fetch(`${API_URL}/api/decode/units/generate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate document units: ${response.status}`);
      }
      
      const result = await response.json();
      
      // If units were already generated according to backend
      if (result.already_generated) {
        console.log("Backend indicates units were already generated");
        setIsGeneratingUnits(false);
        return;
      }
      
      // If generation is already in progress
      if (result.already_in_progress) {
        console.log("Backend indicates unit generation is already in progress");
        
        // If we don't have a job ID but generation is in progress,
        // we can still show the loading indicator but can't poll for status
        setIsGeneratingUnits(true);
        
        // In 10 seconds, try checking if units are now available
        setTimeout(() => {
          checkIfUnitsExist(projectId, token);
        }, 10000);
        
        return;
      }
      
      // If there's a job ID, poll for status
      if (result.job_id) {
        setGenerationJobId(result.job_id);
        pollGenerationStatus(result.job_id, token);
      } else {
        setIsGeneratingUnits(false);
      }
    } catch (error) {
      console.error("Error generating document units:", error);
      setIsGeneratingUnits(false);
    }
  }, [setIsGeneratingUnits, setGenerationJobId, pollGenerationStatus, checkIfUnitsExist]);
  
  // Function to check if units exist for the project and generate them if not
  const checkAndGenerateUnits = useCallback(async (projectId: string) => {
    try {
      // Add this project to our "initiated" set immediately to prevent duplicate calls
      generationInitiatedRef.current.add(projectId);
      
      const token = await getToken();
      if (!token) return;
      
      // Set initial loading state
      setIsGeneratingUnits(true);
      
      // First fetch project details to check if units have been generated
      const projectResponse = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      if (!projectResponse.ok) {
        console.error(`Failed to fetch project: ${projectResponse.status}`);
        setIsGeneratingUnits(false);
        return;
      }
      
      const projectData = await projectResponse.json();
      console.log("Project data for unit generation check:", projectData);
      
      // Check if units have already been generated using the flag from backend
      if (projectData.project?.units_generated || 
         (projectData.project?.type?.toLowerCase() === 'youtube' && 
          projectData.project?.youtube_units_generated)) {
        console.log("Units have already been generated for this project");
        setIsGeneratingUnits(false);
        return;
      }
      
      // Double-check if there are already units for this project, even if the flag isn't set
      const unitsResponse = await fetch(`${API_URL}/api/decode/units?project_id=${projectId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (unitsResponse.ok) {
        const unitsData = await unitsResponse.json();
        if (unitsData.units && unitsData.units.length > 0) {
          console.log(`Project already has ${unitsData.units.length} units. Skipping generation.`);
          setIsGeneratingUnits(false);
          return;
        }
      }
      
      // Determine the type of project and call the appropriate endpoint for generation
      const projectType = projectData.project?.type || projectData.project?.Type || 'document';
      
      if (projectType.toLowerCase() === 'youtube') {
        // Call the YouTube unit generation endpoint
        await generateYouTubeUnits(projectId, token, projectData);
      } else {
        // Call the document unit generation endpoint
        await generateDocumentUnits(projectId, token);
      }
    } catch (error) {
      console.error("Error checking or generating units:", error);
      setIsGeneratingUnits(false);
    }
  }, [getToken, generateYouTubeUnits, generateDocumentUnits, setIsGeneratingUnits]);
  
  // Function to check if flashcards have been generated and generate them if not
  const checkAndGenerateFlashcards = useCallback(async (projectId: string) => {
    try {
      // Add this project to our "initiated" set immediately to prevent duplicate calls
      flashcardGenerationInitiatedRef.current.add(projectId);
      
      const token = await getToken();
      if (!token) return;
      
      // Set initial loading state (but don't show a UI indicator since this happens in background)
      setIsGeneratingFlashcards(true);
      
      // First fetch project details to check if flashcards have been generated
      const projectResponse = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      if (!projectResponse.ok) {
        console.error(`Failed to fetch project: ${projectResponse.status}`);
        setIsGeneratingFlashcards(false);
        return;
      }
      
      const projectData = await projectResponse.json();
      
      // Check if flashcards have already been generated using the flag from backend
      if (projectData.project?.flashcards_generated) {
        console.log("Flashcards have already been generated for this project");
        setIsGeneratingFlashcards(false);
        return;
      }
      
      // Check if there are already units for this project (needed for flashcards)
      const unitsResponse = await fetch(`${API_URL}/api/decode/units?project_id=${projectId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!unitsResponse.ok) {
        console.error(`Failed to fetch units: ${unitsResponse.status}`);
        setIsGeneratingFlashcards(false);
        return;
      }
      
      const unitsData = await unitsResponse.json();
      let units = [];
      
      // Handle different response formats
      if (Array.isArray(unitsData)) {
        units = unitsData;
      } else if (unitsData && typeof unitsData === 'object' && Array.isArray(unitsData.units)) {
        units = unitsData.units;
      }
      
      if (units.length === 0) {
        console.log("No units found for flashcard generation");
        setIsGeneratingFlashcards(false);
        return;
      }
      
      // Generate flashcards for each unit
      console.log(`Generating flashcards for ${units.length} units`);
      
      for (const unit of units) {
        const unitId = unit.ID || unit.id;
        const unitType = (unit.Type || unit.type || '').toLowerCase();
        
        // Skip if unit doesn't have valid ID or type
        if (!unitId || !unitType) {
          console.warn('Skipping unit with invalid ID or type:', unit);
          continue;
        }
        
        // Skip if unit doesn't have appropriate content
        const hasValidContent = unitType === 'youtube' ? 
          !!(unit.YoutubeSummary || unit.youtube_summary) : 
          !!(unit.Content || unit.content);
        
        if (!hasValidContent) {
          console.warn('Skipping unit without appropriate content:', unitId, unitType);
          continue;
        }
        
        try {
          // Generate flashcards for this unit
          const flashcardResponse = await fetch(`${API_URL}/api/decode/flashcards/generate`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              unit_id: unitId,
              type: unitType
            })
          });
          
          if (!flashcardResponse.ok) {
            console.error(`Failed to generate flashcards for unit ${unitId}: ${flashcardResponse.status}`);
            continue; // Continue with other units even if one fails
          }
          
          console.log(`Generated flashcards for unit ${unitId}`);
        } catch (err) {
          console.error(`Error generating flashcards for unit ${unitId}:`, err);
          // Continue with other units even if one fails
        }
      }
      
      // Update project to mark flashcards as generated
      const updateResponse = await fetch(`${API_URL}/api/decode/projects/${projectId}/update-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          flashcards_generated: true
        }),
      });
      
      if (!updateResponse.ok) {
        console.error(`Failed to update project status: ${updateResponse.status}`);
      } else {
        console.log("Project marked as having flashcards generated");
      }
      
      setIsGeneratingFlashcards(false);
    } catch (error) {
      console.error("Error checking or generating flashcards:", error);
      setIsGeneratingFlashcards(false);
    }
  }, [getToken, setIsGeneratingFlashcards]);
  
  // Check if units exist for the project and generate them if needed
  useEffect(() => {
    if (validProjectId && validMedspaceId) {
      // Only proceed if we haven't already initiated generation for this project in this session
      if (!generationInitiatedRef.current.has(validProjectId)) {
        checkAndGenerateUnits(validProjectId);
      }
    }
  }, [validProjectId, validMedspaceId, checkAndGenerateUnits]);
  
  // Check if flashcards need to be generated after confirming units exist
  useEffect(() => {
    // Only attempt to generate flashcards if units generation is not in progress
    if (validProjectId && !isGeneratingUnits && !flashcardGenerationInitiatedRef.current.has(validProjectId)) {
      checkAndGenerateFlashcards(validProjectId);
    }
  }, [validProjectId, isGeneratingUnits, checkAndGenerateFlashcards]);
  
  // Log parameters for debugging
  useEffect(() => {
    console.log('DecodePage parameters:', {
      medspaceId: validMedspaceId,
      projectId: validProjectId
    });
  }, [validMedspaceId, validProjectId]);

  // Toggle functions
  const toggleSidebar = () => setSidebarExpanded((prev) => !prev);
  const toggleHistoryVisibility = () => console.log("History visibility toggle requested");
  const toggleFilterVisibility = () => console.log("Filter visibility toggle requested");
  const toggleViewFile = () => setViewFileVisible((prev) => !prev);
  const handleNewChat = () => {
    console.log("New chat initiated");
    // Add logic for new chat functionality
  };

  //------------ Function to render the current right panel component ------------
  const renderRightPanelContent = () => {
    if (isGeneratingUnits) {
      return (
        <div className={`w-full h-full flex flex-col items-center justify-center space-y-4 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-gray-300' : 'bg-white text-gray-700'}`}>
          <Loader2 className="w-12 h-12 animate-spin text-primary" />
          <div className="text-lg font-medium">Generating units...</div>
          {generationJobId && (
            <div className={`text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-500'}`}>Waiting for units to be generated...</div>
          )}
        </div>
      );
    }
    
    switch (rightPanelView) {
      case 'summary':
        return <Summary projectId={validProjectId as string} />;
      case 'mindmap':
        return <Mindmap projectId={validProjectId as string} />;
      case 'quizzes':
        return <Quizzes projectId={validProjectId as string} />;
      case 'flashcards':
        return <Flashcards projectId={validProjectId as string} />;
      default:
        return <Summary projectId={validProjectId as string} />;
    }
  };

  //------ Render the main page ------
  return (
    <div className={`h-screen flex flex-col ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] text-white' : 'bg-white text-gray-900'}`}>
      {/* Left sidebar - positioned on top of navbar when visible */}
      {sidebarVisible && (
        <LeftSidebar 
          isExpanded={sidebarExpanded} 
          toggleSidebar={toggleSidebar}
          onHide={() => setSidebarVisible(false)}
          
        />
      )}
      
      {/* Top navigation bar - positioned with z-index below the sidebar */}
      <div className="fixed top-0 left-0 right-0 z-10">
        <NavBar 
          handleNewChat={handleNewChat}
          toggleHistoryVisibility={toggleHistoryVisibility}
          toggleFilterVisibility={toggleFilterVisibility}
          isLeftTabVisible={sidebarVisible}
          onShowLeftTab={() => setSidebarVisible(true)}
        />
      </div>
      
      {/* Main content area - with top padding for navbar and left margin for sidebar */}
      <div className="flex flex-1 overflow-hidden pt-12">
        <div 
          ref={mainContentRef} 
          className={`flex-1 flex overflow-hidden relative transition-all duration-300 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`} 
          style={{ marginLeft: sidebarVisible ? (sidebarExpanded ? '16rem' : '4rem') : '0' }}
        >
          {/* Left panel - ViewFile */}
          {viewFileVisible && (
            <div className={`overflow-auto relative transition-all duration-300 ease-in-out ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`} style={{ width: `${splitRatio}%` }}>
              <ViewFile projectId={validProjectId as string} medspaceId={validMedspaceId as string} />
            </div>
          )}
          
          {/* Fixed divider with resize handle */}
          {/*viewFileVisible && (
            <div 
              className="absolute top-0 left-0 h-full flex items-center justify-center z-10"
              style={{ 
                left: `calc(${splitRatio}% - 3px)`,
                width: '6px', 
                cursor: 'col-resize',
                pointerEvents: 'none' // Make the container transparent to mouse events
              }}
            >
              <div 
                className={`h-10 w-2 flex items-center justify-center rounded-md ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)] hover:bg-gray-800 border border--700' : 'bg-gray-200 hover:bg-gray-300'}`}
                style={{ pointerEvents: 'auto' }} // Re-enable mouse events for the handle
                title="Drag to resize panels"
                onMouseDown={handleMouseDown}
              >
                <GripVertical size={16} className="text-gray-500" />
              </div>
            </div>
          )*/}
          
          {/* -----------------Right panel - Dynamic content (Summary/Mindmap/Quizzes) with navigation ----------------- */}
          <div 
            className={`flex flex-col overflow-hidden transition-all  duration-300 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-transparent'}`}
            style={{ width: viewFileVisible ? `${100 - splitRatio}%` : '100%' }}
          >
            {/* Horizontal navigation buttons */}
            <div className={`flex justify-center items-center mb-1 mx-1 p-1  ${theme === 'dark' ? 'bg-[#1e1e1e]' : 'bg-gray-100'} rounded-2xl`}>
              {/* View file button */}
              <button
                onClick={toggleViewFile}
                className={`p-3 rounded-md ${
                  theme === 'dark' 
                    ? 'hover:text-gray-300 text-gray-400' 
                    : 'hover:text-gray-700 text-gray-500'
                }`}
                title={viewFileVisible ? "Hide file view" : "Show file view"}
              >
                {viewFileVisible ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
              </button>
              
              {/* Navigation buttons for switching views */}
              <div className="flex space-x-2">
                <button
                  onClick={() => setRightPanelView('summary')}
                  className={`flex items-center px-6 py-2 text-sm font-medium rounded-2xl ${
                    rightPanelView === 'summary'
                      ? 'bg-gray-100 dark:bg-[#111111] text-gray-900 dark:text-white '
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <FileText size={16} className="mr-2" />
                  <span>Summary</span>
                </button>
                <button
                  onClick={() => setRightPanelView('mindmap')}
                  className={`flex items-center px-6 py-2 text-sm font-medium rounded-2xl ${
                    rightPanelView === 'mindmap'
                      ? 'bg-gray-100 dark:bg-[#111111] text-gray-900 dark:text-white rounded-2xl'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Map size={16} className="mr-2" />
                  <span>Mindmap</span>
                </button>
                <button
                  onClick={() => setRightPanelView('quizzes')}
                  className={`flex items-center px-6 py-2 text-sm font-medium rounded-2xl ${
                    rightPanelView === 'quizzes'
                      ? 'bg-gray-100 dark:bg-[#111111] text-gray-900 dark:text-white rounded-2xl'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <BookOpen size={16} className="mr-2" />
                  <span>Quizzes</span>
                </button>
                <button
                  onClick={() => setRightPanelView('flashcards')}
                  className={`flex items-center px-6 py-2 text-sm font-medium rounded-2xl ${
                    rightPanelView === 'flashcards'
                      ? 'bg-gray-100 dark:bg-[#111111] text-gray-900 dark:text-white rounded-2xl'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Brain size={16} className="mr-2" />
                  <span>Flashcards</span>
                </button>
              </div>
            </div>
            
            {/* Dynamic content area */}
            <div className={`flex-1 flex justify-center items-center overflow-auto w-full h-full mt-2 ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'}`}>
              {renderRightPanelContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}