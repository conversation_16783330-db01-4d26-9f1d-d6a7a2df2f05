'use client';

import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/app/providers/language-provider';
import { useTheme } from '@/components/theme/theme-provider';
import { CheckCircle, Loader2, FileIcon, X, Youtube, File, FileText, MoreVertical, Pencil, FolderPlus, Trash2, ChevronUp, ChevronDown } from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import Image from 'next/image';
import MedSpacesSection, { MedSpace } from './studio_components/MedSpacesSection';
import { useRouter } from 'next/navigation';

import MainLayout from '@/components/NavLayout';
import { API_URL } from '../utils/decode_api';

export interface Project {
  id: number;
  name: string;
  created_at: string;
  unit_count?: number;
  fileURL?: string;
  thumbnailURL?: string;
  thumbnailStatus?: 'loading' | 'ready' | 'error';
  userID?: number;
  med_space_id?: number | null;
  medSpaceID?: number | null;
  isYoutube?: boolean;
}

// Define interfaces for raw data from API
interface RawProject {
  id?: number;
  ID?: number;
  name?: string;
  Name?: string;
  created_at?: string;
  CreatedAt?: string;
  createdAt?: string;
  file_url?: string;
  FileURL?: string;
  thumbnail_url?: string;
  ThumbnailURL?: string;
  med_space_id?: number | null;
  MedSpaceID?: number | null;
  is_youtube?: boolean;
  IsYouTube?: boolean;
  type?: string;
  Type?: string;
}

interface RawMedSpace {
  id?: number;
  ID?: number;
  name?: string;
  Name?: string;
  description?: string;
  Description?: string;
  created_at?: string;
  CreatedAt?: string;
  createdAt?: string;
  projects?: RawProject[];
  Projects?: RawProject[];
}

const DashboardContent = () => {
  const { t } = useLanguage();
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('document'); // State for tabs
  const [numUnits, setNumUnits] = useState<number>(6); // State for number of units (default 6)
 
  const { theme } = useTheme();
  const router = useRouter(); // Single router instance for the component
  
  // Add Clerk auth hook to get the token
  const { getToken } = useAuth();
  const [clerkToken, setClerkToken] = useState<string | null>(null);
  
  // Project related states
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [projectName, setProjectName] = useState('');
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [newProjectError, setNewProjectError] = useState('');
  
  // File upload states
  const [fileUploading, setFileUploading] = useState(false);
  const [fileUploadSuccess, setFileUploadSuccess] = useState(false);
  const [redirectingToProject, setRedirectingToProject] = useState(false);
  
  // Add state for project thumbnails with processing status
  const [projectThumbnails, setProjectThumbnails] = useState<Record<number, string>>({});
  const [thumbnailStatus, setThumbnailStatus] = useState<Record<number, string>>({});
  const [thumbnailPollingJobs, setThumbnailPollingJobs] = useState<Record<number, NodeJS.Timeout>>({});
  
  // Cleanup thumbnail polling on unmount
  useEffect(() => {
    return () => {
      // Clear any thumbnail polling jobs when component unmounts
      Object.values(thumbnailPollingJobs).forEach(timeout => {
        clearTimeout(timeout);
      });
    };
  }, [thumbnailPollingJobs]);
  


  // Load the Clerk token when component mounts
  useEffect(() => {
    const loadClerkToken = async () => {
      try {
        const token = await getToken();
        if (token) {
          setClerkToken(token);
        }
      } catch (error) {
        console.error('Error getting Clerk token:', error);
      }
    };
    
    loadClerkToken();
  }, [getToken]);

  // Function to get the active token (prefer Clerk token, fall back to prop token)
  const getActiveToken = async () => {
    // First try to get a fresh token from Clerk
    try {
      const freshToken = await getToken();
      if (freshToken) return freshToken;
    } catch (error) {
      console.error('Error getting fresh Clerk token:', error);
    }
    
    // Fall back to cached token or prop token
    return clerkToken || '';
  }

  const [youtubeUrl, setYoutubeUrl] = useState('');

 

  // Function to validate YouTube URL (keep existing logic)
  const validateYouTubeUrl = (url: string): boolean => {
    // Simple validation for YouTube URLs
    return url.trim().length > 0 && (
      url.includes('youtube.com/watch') || 
      url.includes('youtu.be/')
    );
  };

  // Combined function to handle either file or YouTube URL
  const handleProjectSource = async (source: { type: 'file', file: File } | { type: 'youtube', url: string }) => {
    setNewProjectError('');
    
    if (source.type === 'file') {
      const file = source.file;
      
      // Check file size (limit to 50MB)
      if (file.size > 50 * 1024 * 1024) {
        setNewProjectError('File size too large. Maximum file size is 50MB.');
        return;
      }

      // Check file type
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      if (!['pdf', 'docx', 'txt'].includes(fileExt || '')) {
        setNewProjectError('Invalid file type. Only PDF, DOCX, and TXT are supported.');
        return;
      }

      setFile(file);
      setFileUploadSuccess(false);
      setFileUploading(true);

      // Simulate upload completion (in a real app this would be the actual file upload)
      setTimeout(() => {
        setFileUploading(false);
        setFileUploadSuccess(true);
        createProjectWithFile(file);
      }, 1000);
    } else {
      // YouTube URL handling
      if (!validateYouTubeUrl(source.url)) {
        setNewProjectError('Please enter a valid YouTube URL.');
        return;
      }
      
      setLoading(true);
      try {
        await createYouTubeProject(source.url);
      } catch (error) {
        console.error('Error creating YouTube project:', error);
        setNewProjectError('Failed to create YouTube project.');
      } finally {
        setLoading(false);
      }
    }
  };

 

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    const loadData = async () => {
      try {
        const [projectsData, medSpacesData] = await Promise.all([
          fetchProjects(),
          fetchMedSpaces()
        ]);
        
        console.log('Initial data loaded:', { projects: projectsData, medSpaces: medSpacesData });
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };
    
    loadData();
  }, []);

  // Add useEffect for initial project selection
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // If there are projects but none selected, select the first one
        if (projects.length > 0 && !selectedProject) {
          handleProjectClick(projects[0]);
        }
      } catch (error: unknown) {
        console.error('Error loading initial data:', error instanceof Error ? error.message : 'Unknown error');
      }
    };

    loadInitialData();
  }, [projects]); // Run when projects array changes

  // Add image placeholder paths - updated to use SVGs
  const placeholderImages = {
    loading: '/placeholders/loading-placeholder.svg',
    error: '/placeholders/error-placeholder.svg',
    default: '/placeholders/document-placeholder.svg',
    pdf: '/placeholders/pdf-placeholder.svg',
    doc: '/placeholders/doc-placeholder.svg',
    image: '/placeholders/image-placeholder.svg',
  };
  
  // Helper function to get placeholder based on file type
  const getPlaceholder = (fileName: string = '') => {
    if (!fileName) return placeholderImages.default;
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    if (ext === 'pdf') return placeholderImages.pdf;
    if (['doc', 'docx'].includes(ext || '')) return placeholderImages.doc;
    if (['jpg', 'jpeg', 'png', 'gif'].includes(ext || '')) return placeholderImages.image;
    
    return placeholderImages.default;
  };

  // Preload an image to improve display performance
  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img');
      img.onload = () => resolve();
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
      
      // Add timeout to avoid hanging if image takes too long
      const timeout = setTimeout(() => {
        reject(new Error(`Image load timed out: ${src}`));
      }, 5000); // 5 second timeout
      
      img.onload = () => {
        clearTimeout(timeout);
        resolve();
      };
      
      img.src = src;
    });
  };

  // Helper function to fetch a single thumbnail with polling for processing thumbnails
  const fetchThumbnail = async (project: Project) => {
    try {
      // Set loading state for this thumbnail
      setProjects(prevProjects => 
        prevProjects.map(p => 
          p.id === project.id 
            ? { ...p, thumbnailStatus: 'loading' }
            : p
        )
      );

      const activeToken = await getActiveToken();
      const response = await fetch(`${API_URL}/api/decode/thumbnail/${project.id}`, {
        headers: {
          'Authorization': `Bearer ${activeToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch thumbnail');
      }

      // If we got a thumbnail URL, update the project
      if (data.thumbnail_url) {
        // Preload the image
        try {
          await preloadImage(data.thumbnail_url);
        } catch (preloadError) {
          console.error('Error preloading thumbnail:', preloadError);
        }

        // Update the project with the new thumbnail URL and status
        setProjects(prevProjects => 
          prevProjects.map(p => 
            p.id === project.id 
              ? { 
                  ...p, 
                  thumbnailURL: data.thumbnail_url,
                  thumbnailStatus: 'ready'
                }
              : p
          )
        );
      } else if (data.is_processing) {
        // If still processing, set up polling
        setTimeout(() => {
          fetchThumbnail(project);
        }, 2000); // Poll every 2 seconds
      } else {
        // If no thumbnail URL and not processing, set error state
        setProjects(prevProjects => 
          prevProjects.map(p => 
            p.id === project.id 
              ? { ...p, thumbnailStatus: 'error' }
              : p
          )
        );
      }
    } catch (error) {
      console.error('Error fetching thumbnail:', error);
      // Set error state for this thumbnail
      setProjects(prevProjects => 
        prevProjects.map(p => 
          p.id === project.id 
            ? { ...p, thumbnailStatus: 'error' }
            : p
        )
      );
    }
  };

  // New function to fetch all thumbnails at once for maximum speed
  const fetchAllThumbnailsAtOnce = async (projects: Project[]) => {
    if (!projects.length) return;
    
    try {
      console.log('Fetching all thumbnails in a single batch...');
      const activeToken = await getActiveToken();
      
      // Mark all thumbnails as loading first for UI feedback
      projects.forEach(project => {
        if (project.id) {
          setThumbnailStatus(prev => ({
            ...prev,
            [project.id]: 'loading'
          }));
        }
      });
      
      // Create list of all project IDs
      const projectIds = projects.map(p => p.id).filter(Boolean);
      
      // Make a single API request for all thumbnails
      const response = await fetch(`${API_URL}/api/decode/thumbnails/batch`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${activeToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ project_ids: projectIds }),
        cache: 'no-store',
      });
      
      if (!response.ok) {
        // Try to get more detailed error information
        const errorText = await response.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
          throw new Error(errorJson.error || 'Unknown error');
        } catch {
          // If we can't parse JSON, try to get the text
          try {
            const errorText = await response.text();
            throw new Error(errorText);
          } catch (textError) {
            throw new Error(`Failed to read error response: ${textError}`);
          }
        }
      }
      
      const data = await response.json();
      
      // If we have results, process them
      if (data.thumbnails && Array.isArray(data.thumbnails)) {
        // Process all thumbnails asynchronously in parallel
        await Promise.all(data.thumbnails.map(async (item: { 
          project_id?: number; 
          projectId?: number; 
          thumbnail_url?: string; 
          thumbnailUrl?: string;
          is_processing?: boolean;
          isProcessing?: boolean;
        }) => {
          const projectId = item.project_id || item.projectId;
          const thumbnailUrl = item.thumbnail_url || item.thumbnailUrl;
          const isProcessing = item.is_processing || item.isProcessing;
          
          if (!projectId || !thumbnailUrl) return;
          
          // Try to preload the image
          try {
            await preloadImage(thumbnailUrl);
            
            // Update the thumbnail URL
            setProjectThumbnails(prev => ({
              ...prev,
              [projectId]: thumbnailUrl
            }));
            
            // If not processing, mark as ready
            if (!isProcessing) {
              setThumbnailStatus(prev => ({
                ...prev,
                [projectId]: 'ready'
              }));
            } else {
              // Set up polling for this processing thumbnail
              const timeoutId = setTimeout(() => {
                const matchingProject = projects.find(p => p.id === projectId);
                if (matchingProject) fetchThumbnail(matchingProject);
              }, 3000);
              
              // Save the timeout ID for cleanup
              setThumbnailPollingJobs(prev => ({
                ...prev,
                [projectId]: timeoutId
              }));
            }
          } catch {
            // Still update URL even if preloading fails
            setProjectThumbnails(prev => ({
              ...prev,
              [projectId]: thumbnailUrl
            }));
            
            // But mark status according to processing state
            setThumbnailStatus(prev => ({
              ...prev,
              [projectId]: isProcessing ? 'loading' : 'ready'
            }));
          }
        }));
      } else {
        // If the batch API doesn't return the expected format, fall back to individual requests
        console.warn('Batch API response format unexpected, falling back to individual requests');
        return fetchThumbnails(projects);
      }
    } catch (error) {
      console.error('Error in batch thumbnail fetching:', error);
      // Fall back to the original method on error
      return fetchThumbnails(projects);
    }
  };

  // Fallback to individual fetches if the server doesn't support batch fetching
  const fetchThumbnails = async (projects: Project[]) => {
    // For maximum speed, fetch all thumbnails in parallel
    const concurrencyLimit = 8; // Increased from 3 to 8 for better performance
    
    // Prioritize selected/visible projects first
    let prioritizedProjects = [...projects];
    
    // Move the selected project to the front of the array if it exists
    if (selectedProject) {
      prioritizedProjects = [
        ...prioritizedProjects.filter(p => p.id === selectedProject.id),
        ...prioritizedProjects.filter(p => p.id !== selectedProject.id)
      ];
    }
    
    // Process all thumbnails at once with a higher concurrency limit
    const projectChunks = [];
    for (let i = 0; i < prioritizedProjects.length; i += concurrencyLimit) {
      projectChunks.push(prioritizedProjects.slice(i, i + concurrencyLimit));
    }
    
    for (const chunk of projectChunks) {
      // Process this chunk in parallel
      await Promise.all(
        chunk.map(project => {
          if (project.id) {
            return fetchThumbnail(project);
          }
          return Promise.resolve();
        })
      );
    }
  };

  // Update fetchProjects to use the new batch thumbnail fetch
  const fetchProjects = async (medSpaceId?: number) => {
    try {
      console.log('Fetching projects...');
      const activeToken = await getActiveToken();
      
      let url = `${API_URL}/api/decode/projects`;
      
      // If a medSpaceId is provided, add it as a query parameter
      if (medSpaceId) {
        url += `?med_space_id=${medSpaceId}`;
      } else if (activeMedSpace) {
        // If we have an active MedSpace but no specific ID was provided,
        // use the active MedSpace's ID
        url += `?med_space_id=${activeMedSpace.id}`;
      } else if (!isShowingAllProjects) {
        // If we're not showing all projects, fetch only projects not in any MedSpace
        url += '?show_all=false';
      } else {
        // If we're showing all projects, make sure to include them all
        url += '?show_all=true';
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
      });
      
      console.log('Response status:', response.status);
      
      if (!response.ok) {
        // Try to read the error message from the response body
        const errorText = await response.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
          throw new Error(errorJson.error || `Failed to fetch projects: ${response.status} ${response.statusText}`);
        } catch {
          console.error('Failed to parse error response');
          throw new Error(errorText);
        }
      }
      
      let data;
      try {
        data = await response.json();
        
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError);
        const rawText = await response.text();
        console.log('Raw response text:', rawText);
        throw new Error('Invalid JSON response from server');
      }
      
      // Handle different response formats
      let normalizedProjects: Project[] = [];
      
      if (Array.isArray(data)) {
       
        normalizedProjects = data.map(normalizeProject).filter((p: Project | null): p is Project => p !== null);
      } else if (data && Array.isArray(data.projects)) {
       
        normalizedProjects = data.projects.map(normalizeProject).filter((p: Project | null): p is Project => p !== null);
      } else if (data && data.project) {
        
        const project = normalizeProject(data.project);
        if (project) normalizedProjects = [project];
      }
      
      // Update state with fetched projects
      setProjects(normalizedProjects);
      
      // Begin fetching thumbnails for these projects using the new batch method
      fetchAllThumbnailsAtOnce(normalizedProjects);
      
      return normalizedProjects;
    } catch (error) {
      console.error('Error fetching projects:', error);
      return [];
    }
  };

  // Helper function to normalize project data
  const normalizeProject = (project: RawProject): Project | null => {
    if (!project) return null;
    
    // Extract ID, handling both lowercase and uppercase field names
    const id = project.id !== undefined ? project.id : (project.ID !== undefined ? project.ID : null);
    if (id === null) return null;
    
    // Extract other fields with similar fallback logic
    const name = project.name || project.Name || '';
    const created_at = project.created_at || project.CreatedAt || project.createdAt || '';
    const fileURL = project.file_url || project.FileURL || '';
    const thumbnailURL = project.thumbnail_url || project.ThumbnailURL || '';
    const medSpaceID = project.med_space_id !== undefined ? project.med_space_id : 
                      (project.MedSpaceID !== undefined ? project.MedSpaceID : null);
    
    // Determine if this is a YouTube project
    const isYoutube = project.is_youtube ?? project.IsYouTube ?? (project.type === 'youtube' || project.Type === 'youtube') ?? false;
    
    return {
      id,
      name,
      created_at,
      fileURL,
      thumbnailURL,
      med_space_id: medSpaceID,
      isYoutube
    };
  };

  const createProjectWithFile = async (uploadedFile: File) => {
    setNewProjectError('');
    setLoading(true);
    setFileUploading(true);
    
    try {
      const activeToken = await getActiveToken();
      if (!activeToken) {
        throw new Error('Authentication token not found');
      }

      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('num_units', numUnits.toString());

      const response = await fetch(`${API_URL}/api/decode/projects`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${activeToken}`,
        },
        body: formData,
      });
      
      if (!response.ok) {
        // Try to read the error message from the response body
        const errorText = await response.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
          setNewProjectError(errorJson.error || `Failed to create project: ${response.status} ${response.statusText}`);
        } catch {
          setNewProjectError(`Failed to create project: ${response.status} ${response.statusText}. Details: ${errorText}`);
        }
        console.error('Failed to create project:', errorText);
        setFileUploading(false);
        setFileUploadSuccess(false);
        return;
      }
      
      const data = await response.json();
      console.log('Project created:', data);
      
      // If the project was created successfully, add it to our list and reset the form
      if (data && data.project) {
        const newProject = normalizeProject(data.project);
        if (newProject) {
          setProjects(prevProjects => [...prevProjects, newProject]);
          
          // If we're in a MedSpace, refresh MedSpaces to update the projects list
          if (activeMedSpace) {
            await fetchMedSpaces();
          }
          
          // Set redirecting state to show loading indicator
          setRedirectingToProject(true);
          
          // Reset form before navigation to prevent "Please select a file" message
          setFile(null);
          setFileUploading(false);
          setFileUploadSuccess(false);
          setNewProjectError('');
          
          // Navigate to the decode page with the new project
          const medspaceId = newProject.med_space_id || (activeMedSpace ? activeMedSpace.id : 0);
          
          // Slight delay to ensure state updates before navigation
          setTimeout(() => {
            router.push(`/decode/${medspaceId}/${newProject.id}`);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error creating project:', error);
      setNewProjectError('An unexpected error occurred. Please try again.');
      setFileUploading(false);
      setFileUploadSuccess(false);
    } finally {
      setLoading(false);
    }
  };
  
  // Create project from YouTube URL
  const createYouTubeProject = async (youtubeUrl: string) => {
    try {
      setLoading(true);
      const activeToken = await getActiveToken();
      
      // Validate YouTube URL format
      const ytRegex = /^(https?:\/\/)?(www\.)?(?:youtube\.com\/(?:.*?[?&]v=|v\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
      if (!ytRegex.test(youtubeUrl)) {
        setNewProjectError('Please enter a valid YouTube URL.');
        setLoading(false);
        return;
      }

      const requestBody = {
        youtube_url: youtubeUrl,
        med_space_id: activeMedSpace ? activeMedSpace.id : null
      };

      const response = await fetch(`${API_URL}/api/decode/youtube/projects`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${activeToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        // Try to read the error message from the response body
        const errorText = await response.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
          setNewProjectError(errorJson.error || `Failed to create YouTube project: ${response.status} ${response.statusText}`);
        } catch {
          setNewProjectError(`Failed to create YouTube project: ${response.status} ${response.statusText}. Details: ${errorText}`);
        }
        console.error('Failed to create YouTube project:', errorText);
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('YouTube project created:', data);
      
      // If the project was created successfully, add it to our list and reset the form
      if (data && data.project) {
        const newProject = normalizeProject(data.project);
        if (newProject) {
          setProjects(prevProjects => [...prevProjects, newProject]);
          
          // If we're in a MedSpace, refresh MedSpaces to update the projects list
          if (activeMedSpace) {
            await fetchMedSpaces();
          }
          
          // Set redirecting state to show loading indicator
          setRedirectingToProject(true);
          
          // Reset form before navigation to prevent state issues
          setYoutubeUrl('');
          setNewProjectError('');
          
          // Navigate to the decode page with the new project
          const medspaceId = newProject.med_space_id || (activeMedSpace ? activeMedSpace.id : 0);
          
          // Slight delay to ensure state updates before navigation
          setTimeout(() => {
            router.push(`/decode/${medspaceId}/${newProject.id}`);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error creating project:', error);
      setNewProjectError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateProject = async () => {
    if (!editingProject) {
      return;
    }
    
    setLoading(true);
    
    try {
      const activeToken = await getActiveToken();
      
      const formData = new FormData();
      if (projectName) {
        formData.append('name', projectName);
      }
      
      if (file) {
        formData.append('file', file);
      }
      
      // Update the project
      const response = await fetch(`${API_URL}/api/decode/projects/${editingProject.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${activeToken}`,
        },
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update project: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Reset form state
      setProjectName('');
      setFile(null);
      setEditingProject(null);
      
      // Create a normalized project object
      const updatedProject: Project = {
        id: data.project.id || data.project.ID,
        name: data.project.name || data.project.Name || '',
        created_at: data.project.created_at || data.project.CreatedAt || data.project.createdAt || new Date().toISOString(),
        unit_count: data.project.unit_count || data.project.UnitCount || 0,
        fileURL: data.project.file_url || data.project.FileURL || '',
        thumbnailURL: data.project.thumbnail_url || data.project.ThumbnailURL || '',
        med_space_id: data.project.med_space_id || data.project.MedSpaceID || null,
        medSpaceID: data.project.med_space_id || data.project.MedSpaceID || null,
        isYoutube: data.project.is_youtube ?? data.project.IsYouTube ?? false,
      };
      
      // Update project in state
      setProjects(projects.map(p => p.id === updatedProject.id ? updatedProject : p));
      
      // If this was the selected project, update it
      if (selectedProject && selectedProject.id === updatedProject.id) {
        setSelectedProject(updatedProject);
      }
      
      // Force thumbnail refresh if the file was updated
      if (file) {
        // First set the thumbnail status to loading
        setThumbnailStatus(prev => ({
          ...prev,
          [updatedProject.id]: 'loading'
        }));
        
        // Clear any existing thumbnail
        setProjectThumbnails(prev => {
          const newThumbnails = { ...prev };
          delete newThumbnails[updatedProject.id];
          return newThumbnails;
        });
        
        // Then fetch the thumbnail
        setTimeout(() => {
          fetchThumbnail(updatedProject);
        }, 1000); // Small delay to allow backend processing
      }
      
    } catch (error: unknown) {
      console.error('Error updating project:', error instanceof Error ? error.message : error);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectDelete = async (projectId: number) => {
    // Show confirmation dialog
    if (!confirm("Are you sure you want to delete this project? This action cannot be undone.")) {
      return; // User cancelled the deletion
    }
    
    try {
      setLoading(true);
      const activeToken = await getActiveToken();
      const response = await fetch(`${API_URL}/api/decode/projects/${projectId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to delete project:', errorData);
        alert(`Failed to delete project: ${errorData.error || response.statusText}`);
        return;
      }

      // Remove project from state
      setProjects(projects.filter(p => p.id !== projectId));
      if (selectedProject?.id === projectId) {
        setSelectedProject(null);
      }
      
      // Show success message
      alert("Project deleted successfully!");
    } catch (error: unknown) {
      console.error('Error deleting project:', error instanceof Error ? error.message : 'Unknown error');
      alert('An unexpected error occurred while deleting the project.');
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (project: Project) => {
    setSelectedProject(project);
    
    
    // Removed automatic navigation to decode page
    // Now the user can select a project without being redirected
  };

 

  const handleProjectDropdownClick = (project: Project) => {
    setProjectWithOpenMenu(projectWithOpenMenu === project.id ? null : project.id);
  };

 

 

  const [medSpaces, setMedSpaces] = useState<MedSpace[]>([]);
  const [isCreatingMedSpace, setIsCreatingMedSpace] = useState(false);
  const [newMedSpaceName, setNewMedSpaceName] = useState('');
  const [newMedSpaceDescription, setNewMedSpaceDescription] = useState('');
  const [activeMedSpace, setActiveMedSpace] = useState<MedSpace | null>(null);
  const [isShowingAllProjects, setIsShowingAllProjects] = useState(true);
  const [editingMedSpaceId, setEditingMedSpaceId] = useState<number | null>(null);
  const [medSpaceWithOpenMenu, setMedSpaceWithOpenMenu] = useState<number | null>(null);
  const [confirmationMedSpaceId, setConfirmationMedSpaceId] = useState<number | null>(null);
  const [projectsWithOpenMedSpaceDropdown, setProjectsWithOpenMedSpaceDropdown] = useState<Record<number, boolean>>({});

  const normalizeMedSpace = (medSpace: RawMedSpace): MedSpace | null => {
    if (!medSpace || (!medSpace.id && !medSpace.ID)) return null;
    
    // Ensure we have a valid ID number
    const id = medSpace.id ?? medSpace.ID;
    if (id === undefined) return null;
    
    return {
      id: id,
      name: medSpace.name || medSpace.Name || 'Unnamed MedSpace',
      description: medSpace.description || medSpace.Description || '',
      created_at: medSpace.created_at || medSpace.CreatedAt || medSpace.createdAt || new Date().toISOString(),
      projects: (medSpace.projects || medSpace.Projects || [])
        .map(normalizeProject)
        .filter((p: Project | null): p is Project => p !== null),
      isExpanded: false
    };
  };

  const fetchMedSpaces = async () => {
    try {
      console.log('Fetching medspaces...');
      const activeToken = await getActiveToken();
      
      const response = await fetch(`${API_URL}/api/decode/medspaces`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
      });
      
      console.log('MedSpaces response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
          throw new Error(errorJson.error || errorText);
        } catch {
          console.error('Failed to parse error response');
          throw new Error(errorText);
        }
      }
      
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError);
        const rawText = await response.text();
        console.log('Raw response text:', rawText);
        throw new Error('Invalid JSON response from server');
      }
      
      // Handle different response formats
      let normalizedMedSpaces: MedSpace[] = [];
      
      if (Array.isArray(data)) {
        console.log('MedSpaces data is an array:', data);
        normalizedMedSpaces = data.map(normalizeMedSpace).filter((ms: MedSpace | null): ms is MedSpace => ms !== null);
      } else if (data && Array.isArray(data.medspaces)) {
        console.log('Data has medspaces array:', data.medspaces);
        normalizedMedSpaces = data.medspaces.map(normalizeMedSpace).filter((ms: MedSpace | null): ms is MedSpace => ms !== null);
      } else if (data && data.medspace) {
        console.log('Data has single medspace:', data.medspace);
        const medSpace = normalizeMedSpace(data.medspace);
        if (medSpace) normalizedMedSpaces = [medSpace];
      }
      
      
      // For each MedSpace, normalize its projects
      const medSpacesWithProjects = normalizedMedSpaces.map(medSpace => {
        if (medSpace.projects && medSpace.projects.length > 0) {
          const normalizedProjects = medSpace.projects
            .map(normalizeProject)
            .filter((p: Project | null): p is Project => p !== null);
          
          return { ...medSpace, projects: normalizedProjects };
        }
        return medSpace;
      });
      
      setMedSpaces(medSpacesWithProjects);
      return medSpacesWithProjects;
    } catch (error) {
      console.error('Error fetching medspaces:', error);
      return [];
    }
  };

  const createMedSpace = async () => {
    if (!newMedSpaceName.trim()) {
      console.error('MedSpace name is required');
      return;
    }
    
    try {
      setLoading(true);
      const activeToken = await getActiveToken();
      
      const response = await fetch(`${API_URL}/api/decode/medspaces`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
        body: JSON.stringify({
          name: newMedSpaceName,
          description: newMedSpaceDescription
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to create medspace:', errorData.error || response.statusText);
        throw new Error(errorData.error || 'Failed to create medspace');
      }
      
      const data = await response.json();
      console.log('MedSpace created:', data);
      
      if (data && data.medspace) {
        const newMedSpace = normalizeMedSpace(data.medspace);
        if (newMedSpace) {
          setMedSpaces(prevMedSpaces => [...prevMedSpaces, newMedSpace]);
        }
      }
      
      // Reset form
      setNewMedSpaceName('');
      setNewMedSpaceDescription('');
      setIsCreatingMedSpace(false);
      
      // Refresh medspaces
      await fetchMedSpaces();
    } catch (error) {
      console.error('Error creating medspace:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateMedSpace = async (medSpaceId: number, name: string, description: string) => {
    try {
      setLoading(true);
      const activeToken = await getActiveToken();
      
      const response = await fetch(`${API_URL}/api/decode/medspaces/${medSpaceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
        body: JSON.stringify({
          name,
          description
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to update medspace:', errorData.error || response.statusText);
        throw new Error(errorData.error || 'Failed to update medspace');
      }
      
      const data = await response.json();
      console.log('MedSpace updated:', data);
      
      // Update the medspace in state
      setMedSpaces(prevMedSpaces => 
        prevMedSpaces.map(ms => 
          ms.id === medSpaceId ? { ...ms, name, description } : ms
        )
      );
      
      setEditingMedSpaceId(null);
    } catch (error) {
      console.error('Error updating medspace:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteMedSpace = async (medSpaceId: number) => {
    try {
      setLoading(true);
      const activeToken = await getActiveToken();
      
      const response = await fetch(`${API_URL}/api/decode/medspaces/${medSpaceId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to delete medspace:', errorData.error || response.statusText);
        throw new Error(errorData.error || 'Failed to delete medspace');
      }
      
      console.log('MedSpace deleted successfully');
      
      // Remove the medspace from state
      setMedSpaces(prevMedSpaces => prevMedSpaces.filter(ms => ms.id !== medSpaceId));
      
      // If the active medspace was deleted, reset to showing all projects
      if (activeMedSpace && activeMedSpace.id === medSpaceId) {
        setActiveMedSpace(null);
        setIsShowingAllProjects(true);
      }
      
      setConfirmationMedSpaceId(null);
    } catch (error) {
      console.error('Error deleting medspace:', error);
    } finally {
      setLoading(false);
    }
  };

  const addProjectToMedSpace = async (projectId: number, medSpaceId: number) => {
    try {
      setLoading(true);
      const activeToken = await getActiveToken();
      
      const response = await fetch(`${API_URL}/api/decode/medspaces/${medSpaceId}/projects`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${activeToken}`,
        },
        body: JSON.stringify({
          project_id: projectId
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to add project to medspace:', errorData.error || response.statusText);
        throw new Error(errorData.error || 'Failed to add project to medspace');
      }
      
      console.log('Project added to MedSpace successfully');
      
      // Update projects and medspaces
      await Promise.all([fetchProjects(), fetchMedSpaces()]);
    } catch (error) {
      console.error('Error adding project to medspace:', error);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle selecting a MedSpace
  const handleMedSpaceClick = (medSpace: MedSpace) => {
    setActiveMedSpace(medSpace);
    setIsShowingAllProjects(false);
  };
  
  // Function to show all projects (clear active MedSpace)
  const showAllProjects = () => {
    setActiveMedSpace(null);
    setIsShowingAllProjects(true);
  };
  

  
  // Function to handle MedSpace dropdown menu
  const handleMedSpaceDropdownClick = (medSpace: MedSpace) => {
    setMedSpaceWithOpenMenu(medSpace.id === medSpaceWithOpenMenu ? null : medSpace.id);
  };
  
  // Function to handle MedSpace delete confirmation
  const handleMedSpaceDelete = async (medSpaceId: number) => {
    setConfirmationMedSpaceId(medSpaceId);
  };
  
  // Function to confirm MedSpace deletion
  const confirmMedSpaceDelete = async () => {
    if (confirmationMedSpaceId) {
      await deleteMedSpace(confirmationMedSpaceId);
    }
  };
  
  // Function to cancel MedSpace deletion
  const cancelMedSpaceDelete = () => {
    setConfirmationMedSpaceId(null);
  };

  // Project editing state variables
  const [projectWithOpenMenu, setProjectWithOpenMenu] = useState<number | null>(null);
  const [editingProjectId, setEditingProjectId] = useState<number | null>(null);
  const [newProjectName, setNewProjectName] = useState('');
  const [confirmationProjectId, setConfirmationProjectId] = useState<number | null>(null);

  // Render the project form with a unified input for both file and YouTube URL
  const renderProjectForm = () => {
    return (
      <div className="mb-12 bg-transparent dark:bg-transparent p-8 rounded-lg dark:border-gray-700 max-w-4xl mx-auto text-center">
        <div className="flex flex-col justify-center items-center mb-6">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-3">{t('common.projectCreation.question')}</h2>
          
        </div>
        
        {newProjectError && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md dark:bg-red-900/30 dark:text-red-400">
            {newProjectError}
          </div>
        )}
        
        {redirectingToProject && (
          <div className="mb-4 p-3 bg-gray-100 text-gray-700 rounded-md dark:bg-gray-300/60 dark:text-gray-400 flex items-center justify-center">
            <Loader2 className="w-5 h-5 animate-spin text-gray-600 dark:text-gray-300 mr-2" />
            <span>Project created! Redirecting to editor...</span>
          </div>
        )}
        
        <div className="mb-4 max-w-2xl mx-auto">
          <div className="flex flex-col">
            {/* Tab Navigation */}
            <div className="flex justify-center mb-4">
              <button
                className={`flex items-center px-6 py-2 text-sm font-medium rounded-l-lg ${
                  activeTab === 'document'
                    ? 'bg-gray-100 dark:bg-[#1e1e1e] text-gray-900 dark:text-white rounded-lg '
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
                onClick={() => setActiveTab('document')}
              >
                <FileText className="w-4 h-4 mr-2" />
                {t('common.projectCreation.uploadFile')}
              </button>
              <button
                className={`flex items-center px-6 py-2 text-sm font-medium rounded-r-lg ${
                  activeTab === 'youtube'
                    ? 'bg-gray-100 dark:bg-[#1e1e1e] text-gray-900 dark:text-white  rounded-lg'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
                onClick={() => setActiveTab('youtube')}
              >
                <Youtube className="w-4 h-4 mr-2" />
                YouTube Video
              </button>
            </div>
            
            {activeTab === 'document' ? (
              <div>
               
                <div className={`flex items-center justify-center p-5 border-transparent ${
                  theme === 'dark' ? 'bg-[#1e1e1e] border-1' : 'bg-gray-100 border-gray-300'
                } rounded-3xl focus-within:ring-2 ${theme === 'dark' ? 'focus-within:ring-gray-500' : 'focus-within:ring-gray-400'} focus-within:border-transparent`}>
                  <label
                    htmlFor="document-file-input"
                    className="flex flex-col items-center justify-center cursor-pointer w-full relative"
                  >
                    {fileUploading ? (
                      <>
                        <Loader2 className={`h-16 w-16 mb-3 animate-spin ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`} />
                        <span className={`text-lg font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                          {t('common.projectCreation.uploading')}
                        </span>
                      </>
                    ) : fileUploadSuccess ? (
                      <>
                        <CheckCircle className={`h-16 w-16 mb-3 ${theme === 'dark' ? 'text-visual-400' : 'text-visual-600'}`} />
                        <span className={`text-lg font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                          {t('common.projectCreation.uploadSuccess')}
                        </span>
                        <span className={`text-sm flex items-center justify-center gap-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          <Loader2 size={16} className="animate-spin" />
                          {t('common.projectCreation.processing')}
                        </span>
                      </>
                    ) : (
                      <div className="flex flex-col items-center">
                        <File className={`h-16 w-16 mb-3 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`} />
                        <span className={`text-lg font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                          {t('common.projectCreation.uploadFile')}
                        </span>
                      </div>
                    )}
                    <input
                      id="document-file-input"
                      type="file"
                      className="hidden"
                      onChange={(e) => {
                        const selectedFile = e.target.files?.[0];
                        if (selectedFile) {
                          setYoutubeUrl('');
                          handleProjectSource({ type: 'file', file: selectedFile });
                        }
                      }}
                      accept=".pdf,.docx,.pptx,.txt"
                    />
                    
                    {/* Number of Units Input - Moved to bottom-right with custom controls */}
                    {!fileUploading && !fileUploadSuccess && (
                      <div className="absolute bottom-1 right-1  flex items-center space-x-2 bg-opacity-80 p-1 rounded-lg ${
                         theme === 'dark' ? 'bg-gray-900/50' : 'bg-gray-200/50' // Semi-transparent background
                      }">
                        <span className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                           Units
                        </span>
                        <div className="flex items-center">
                          <input
                            id="num-units-input"
                            type="number"
                            min="1"
                            max="200"
                            value={numUnits}
                            onClick={(e) => e.stopPropagation()} // Prevent label click trigger
                            onChange={(e) => {
                              e.stopPropagation(); // Prevent label click trigger
                              const value = parseInt(e.target.value, 10);
                              if (!isNaN(value) && value >= 1 && value <= 200) {
                                setNumUnits(value);
                              } else if (e.target.value === '') {
                                setNumUnits(6); // Reset to default if cleared
                              } else if (!isNaN(value) && value > 200) {
                                setNumUnits(200); // Cap at max (200)
                              } else if (!isNaN(value) && value < 1) {
                                 setNumUnits(1); // Cap at min
                              }
                            }}
                            // Hide default spinners
                            className={`w-12 text-center px-1 py-1 rounded-md focus:outline-none focus:ring-1 border-none appearance-none [-moz-appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${
                              theme === 'dark' 
                                ? 'bg-[#2a2a2a] text-white focus:ring-gray-500 placeholder-gray-400' 
                                : 'bg-gray-300 text-gray-900 focus:ring-gray-400 placeholder-gray-500'
                            }`}
                          />
                          {/* Custom Increment/Decrement Buttons */}
                          <div className="flex flex-col ml-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setNumUnits(prev => Math.min(200, prev + 1));
                              }}
                              className={`p-0.5 rounded-t-md ${theme === 'dark' ? 'bg-[#2a2a2a] hover:bg-gray-600 text-gray-200' : 'bg-gray-300 hover:bg-gray-400 text-gray-700'}`}
                              aria-label="Increment units"
                            >
                              <ChevronUp size={14} />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setNumUnits(prev => Math.max(1, prev - 1));
                              }}
                              className={`p-0.5 rounded-b-md ${theme === 'dark' ? 'bg-[#2a2a2a] hover:bg-gray-600 text-gray-200' : 'bg-gray-300 hover:bg-gray-400 text-gray-700'}`}
                              aria-label="Decrement units"
                            >
                              <ChevronDown size={14} />
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </label>
                </div>
              </div>
            ) : (
              <div className={`flex items-center justify-center p-10 border-transparent ${
                 theme === 'dark' ? 'bg-[#1e1e1e] border-1' : 'bg-gray-100 border-gray-300'
               } rounded-3xl focus-within:ring-2 ${theme === 'dark' ? 'focus-within:ring-gray-500' : 'focus-within:ring-gray-400'} focus-within:border-transparent`}>
                <div className={`flex items-center gap-2 w-full p-3 ${
                  theme === 'dark' ? 'bg-[#1e1e1e] ' : 'bg-gray-100 '
                } rounded-2xl `}>
                  <Youtube className="ml-2 text-gray-500" size={24} />
                  <input
                    type="text"
                    className={`flex-grow px-3 py-3 outline-none ${
                      theme === 'dark' ? 'bg-transparent text-white placeholder-gray-400' : 'bg-transparent text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Paste YouTube URL..."
                    value={youtubeUrl}
                    onChange={(e) => setYoutubeUrl(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && youtubeUrl.trim()) {
                        handleProjectSource({ type: 'youtube', url: youtubeUrl });
                      }
                    }}
                  />
                  <button
                    className={`px-5 py-3 rounded-md ${
                      !youtubeUrl.trim() ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    } ${
                      theme === 'dark' 
                        ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                        : 'bg-gray-400 hover:bg-gray-500 text-white'
                    }`}
                    disabled={!youtubeUrl.trim() || fileUploading || loading}
                    onClick={() => {
                      if (youtubeUrl.trim()) {
                        handleProjectSource({ type: 'youtube', url: youtubeUrl });
                      }
                    }}
                    title="Add project"
                  >
                    {loading || fileUploading ? (
                      <Loader2 className="animate-spin" size={20} />
                    ) : (
                      <span>Add</span>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* File preview if selected */}
        {file && (
          <div className={`mt-3 p-3 rounded-md flex items-center justify-between ${
            theme === 'dark' ? 'bg-gray-800/30' : 'bg-gray-100'
          }`}>
            <div className="flex items-center">
              <FileIcon className={`mr-2 h-5 w-5 ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`} />
              <span className={`text-sm ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {file.name}
              </span>
            </div>
            <button 
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              onClick={() => {
                setFile(null);
                setFileUploadSuccess(false);
              }}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>
    );
  };

  return (
    <MainLayout>
      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-7xl mx-auto">
          {/* -------------Dashboard Header------------- */}
          <div className="flex justify-between items-center mb-8">
            
          </div>
          
          {/* Project Creation Form */}
          {renderProjectForm()}
          
          {/* MedSpaces Section */}
          <MedSpacesSection 
            t={t}
            theme={theme}
            medSpaces={medSpaces}
            isCreatingMedSpace={isCreatingMedSpace}
            setIsCreatingMedSpace={setIsCreatingMedSpace}
            newMedSpaceName={newMedSpaceName}
            setNewMedSpaceName={setNewMedSpaceName}
            newMedSpaceDescription={newMedSpaceDescription}
            setNewMedSpaceDescription={setNewMedSpaceDescription}
            activeMedSpace={activeMedSpace}
            isShowingAllProjects={isShowingAllProjects}
            editingMedSpaceId={editingMedSpaceId}
            setEditingMedSpaceId={setEditingMedSpaceId}
            medSpaceWithOpenMenu={medSpaceWithOpenMenu}
            setMedSpaceWithOpenMenu={setMedSpaceWithOpenMenu}
            confirmationMedSpaceId={confirmationMedSpaceId}
            loading={loading}
            createMedSpace={createMedSpace}
            updateMedSpace={updateMedSpace}
            setMedSpaces={setMedSpaces}
            handleMedSpaceClick={handleMedSpaceClick}
            showAllProjects={showAllProjects}
            handleMedSpaceDropdownClick={handleMedSpaceDropdownClick}
            handleMedSpaceDelete={handleMedSpaceDelete}
            confirmMedSpaceDelete={confirmMedSpaceDelete}
            cancelMedSpaceDelete={cancelMedSpaceDelete}
          />
          
          {/* Combined Projects Grid - All Projects */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {/* All Project Cards */}
            {projects.length > 0 ? (
              projects
                .filter(project => {
                  // Apply MedSpace filtering only - no more type filtering
                  if (activeMedSpace) {
                    return project.med_space_id === activeMedSpace.id;
                  }
                  if (isShowingAllProjects) {
                    return true;
                  }
                  return !project.med_space_id;
                })
                .map(project => (
                  <div 
                    key={project.id}
                    className={`relative rounded-2xl overflow-hidden transition-all duration-300 ${
                      theme === 'dark' ? 'bg-transparent' : 'bg-white'
                    }`}
                  >
                    <div 
                      className="cursor-pointer flex flex-col" 
                      onClick={() => {
                        handleProjectClick(project);
                        const medspaceId = project.med_space_id || project.medSpaceID || (activeMedSpace ? activeMedSpace.id : null);
                        
                        if (medspaceId) {
                          router.push(`/decode/${medspaceId}/${project.id}`);
                        } else {
                          router.push(`/decode/0/${project.id}`);
                        }
                      }}
                    >
                      {/* Project Thumbnail */}
                      <div className="relative h-40 overflow-hidden bg-[#1e1e1e] rounded-2xl group">
                        {/* Hover overlay effect */}
                        <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0 ${
                          theme === 'dark' ? 'bg-black/20' : 'bg-white/20'
                        }`}></div>
                        
                        {thumbnailStatus[project.id] === 'loading' ? (
                          <div className="w-full h-full flex items-center justify-center">
                            <Loader2 size={64} className={`animate-spin ${
                              theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                            }`} />
                          </div>
                        ) : thumbnailStatus[project.id] === 'ready' ? (
                          <div className="w-full h-full relative overflow-hidden">
                            <Image 
                              src={project.thumbnailURL || projectThumbnails[project.id] || ''}
                              alt="Project thumbnail"
                              fill
                              priority
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              style={{ objectFit: 'cover', objectPosition: 'top center' }}
                              onLoad={() => {
                                setThumbnailStatus(prev => ({
                                  ...prev,
                                  [project.id]: 'ready'
                                }));
                              }}
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                const src = target.src;
                                
                                if (!target.dataset.retried) {
                                  setTimeout(() => {
                                    target.dataset.retried = 'true';
                                    target.src = src + '?retry=' + new Date().getTime();
                                  }, 1000); 
                                } else {
                                  setThumbnailStatus(prev => ({
                                    ...prev,
                                    [project.id]: 'error'
                                  }));
                                }
                              }}
                            />
                            
                            {/* YouTube play button overlay - only show for YouTube projects */}
                            {project.isYoutube && (
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-16 h-16 rounded-full bg-red-600 bg-opacity-80 flex items-center justify-center">
                                  <div className="w-0 h-0 border-t-[10px] border-b-[10px] border-l-[20px] ml-1 border-t-transparent border-b-transparent border-l-white"></div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : thumbnailStatus[project.id] === 'error' ? (
                          <div className="w-full h-full flex items-center justify-center">
                            <Image 
                              src={getPlaceholder('error')}
                              alt="Error loading thumbnail"
                              width={120}
                              height={120}
                              priority
                            />
                          </div>
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Image 
                              src={project.isYoutube ? getPlaceholder('youtube') : getPlaceholder('pdf')}
                              alt="Placeholder thumbnail"
                              width={120}
                              height={120}
                              priority
                            />
                          </div>
                        )}
                      </div>
                      
                      {/* Project Details */}
                      <div className="p-4">
                        <div className="flex items-center justify-between">
                          <h3 className={`text-base font-medium ${
                            theme === 'dark' ? 'text-white' : 'text-gray-800'
                          }`}>
                            {project.name}
                          </h3>
                          
                          {/* Three dots menu moved next to title */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleProjectDropdownClick(project);
                            }}
                            className={`p-1 rounded-full ${
                              theme === 'dark' 
                                ? 'hover:bg-[#2a2a2a] text-gray-300' 
                                : 'hover:bg-[#f0f0f0] text-gray-500'
                            }`}
                            aria-label="Project options"
                          >
                            <MoreVertical size={18} />
                          </button>
                          
                          {projectWithOpenMenu === project.id && (
                            <div 
                              className={`absolute right-4 top-16 mt-1 w-48 rounded-md shadow-lg py-1 ${
                                theme === 'dark' ? 'bg-[#1e1e1e] border border-[#333333]' : 'bg-[#1e1e1e] border border-[#333333]'
                              } z-20`}
                            >
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingProjectId(project.id);
                                  setNewProjectName(project.name);
                                  setProjectWithOpenMenu(null);
                                }}
                                className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                  theme === 'dark' ? 'text-gray-200 hover:bg-[#2a2a2a]' : 'text-gray-200 hover:bg-[#2a2a2a]'
                                }`}
                              >
                                <Pencil size={16} className="mr-2" />
                                Edit Project
                              </button>

                              <div className="relative">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setProjectsWithOpenMedSpaceDropdown(prev => ({
                                      ...prev,
                                      [project.id]: !prev[project.id]
                                    }));
                                  }}
                                  className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                    theme === 'dark' ? 'text-gray-200 hover:bg-[#2a2a2a]' : 'text-gray-200 hover:bg-[#2a2a2a]'
                                  }`}
                                >
                                  <FolderPlus size={16} className="mr-2" />
                                  Add to MedSpace
                                </button>

                                {projectsWithOpenMedSpaceDropdown[project.id] && (
                                  <div 
                                    className={`absolute left-full top-0 w-48 rounded-md shadow-lg py-1 ${
                                      theme === 'dark' ? 'bg-[#1e1e1e] border border-[#333333]' : 'bg-[#1e1e1e] border border-[#333333]'
                                    }`}
                                  >
                                    {medSpaces.length > 0 ? (
                                      medSpaces.map(medSpace => (
                                        <button
                                          key={medSpace.id}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            addProjectToMedSpace(project.id, medSpace.id);
                                            setProjectsWithOpenMedSpaceDropdown(prev => ({
                                              ...prev,
                                              [project.id]: false
                                            }));
                                            setProjectWithOpenMenu(null);
                                          }}
                                          className={`block w-full text-left px-4 py-2 text-sm ${
                                            theme === 'dark' ? 'text-gray-200 hover:bg-[#2a2a2a]' : 'text-gray-200 hover:bg-[#2a2a2a]'
                                          }`}
                                        >
                                          {medSpace.name}
                                        </button>
                                      ))
                                    ) : (
                                      <div className={`px-4 py-2 text-sm ${
                                        theme === 'dark' ? 'text-gray-400' : 'text-gray-400'
                                      }`}>
                                        No MedSpaces found
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>

                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleProjectDelete(project.id);
                                  setProjectWithOpenMenu(null);
                                }}
                                className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                  theme === 'dark' ? 'text-red-400 hover:bg-[#2a2a2a]' : 'text-red-400 hover:bg-[#2a2a2a]'
                                }`}
                              >
                                <Trash2 size={16} className="mr-2" />
                                Delete Project
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Project Edit Form */}
                    {editingProjectId === project.id && (
                      <div className={`absolute inset-0 flex items-center justify-center bg-opacity-75 ${
                        theme === 'dark' ? 'bg-black' : 'bg-gray-500'
                      }`}>
                        <div className={`p-4 rounded-lg shadow-lg max-w-md w-full ${
                          theme === 'dark' ? 'bg-[#333]' : 'bg-white'
                        }`}>
                          <h3 className={`text-lg font-medium mb-3 ${
                            theme === 'dark' ? 'text-white' : 'text-gray-800'
                          }`}>Edit Project</h3>
                          <div className="space-y-4">
                            <div>
                              <label className={`block text-sm font-medium mb-1 ${
                                theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                              }`}>
                                Project Name
                              </label>
                              <input
                                type="text"
                                value={newProjectName}
                                onChange={(e) => setNewProjectName(e.target.value)}
                                className={`block w-full p-2 rounded-md ${
                                  theme === 'dark' 
                                    ? 'bg-[#444] text-white border-gray-600' 
                                    : 'bg-white text-gray-900 border-gray-300'
                                } border focus:outline-none`}
                              />
                            </div>
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={() => setEditingProjectId(null)}
                                className={`px-3 py-1 rounded ${
                                  theme === 'dark' 
                                    ? 'bg-[#444] hover:bg-[#555] text-gray-200' 
                                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                                }`}
                              >
                                Cancel
                              </button>
                              <button
                                onClick={() => {
                                  updateProject();
                                  setEditingProjectId(null);
                                }}
                                disabled={!newProjectName.trim() || loading}
                                className={`px-3 py-1 rounded ${
                                  theme === 'dark' 
                                    ? 'bg-indigo-600 hover:bg-indigo-700 text-white disabled:bg-gray-600' 
                                    : 'bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-gray-300'
                                }`}
                              >
                                {loading ? 'Updating...' : 'Update'}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Delete confirmation */}
                    {confirmationProjectId === project.id && (
                      <div className={`absolute inset-0 flex items-center justify-center bg-opacity-75 ${
                        theme === 'dark' ? 'bg-black' : 'bg-gray-500'
                      }`}>
                        <div className={`p-4 rounded-lg shadow-lg max-w-md w-full ${
                          theme === 'dark' ? 'bg-[#333]' : 'bg-white'
                        }`}>
                          <h3 className={`text-lg font-medium mb-3 ${
                            theme === 'dark' ? 'text-white' : 'text-gray-800'
                          }`}>Delete Project</h3>
                          <p className={`mb-4 ${
                            theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                          }`}>
                            Are you sure you want to delete this project? This action cannot be undone.
                          </p>
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => setConfirmationProjectId(null)}
                              className={`px-3 py-1 rounded ${
                                theme === 'dark' 
                                  ? 'bg-[#444] hover:bg-[#555] text-gray-200' 
                                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                              }`}
                            >
                              Cancel
                            </button>
                            <button
                              onClick={() => {
                                handleProjectDelete(project.id);
                                setConfirmationProjectId(null);
                              }}
                              className={`px-3 py-1 rounded ${
                                theme === 'dark' 
                                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                                  : 'bg-red-500 hover:bg-red-600 text-white'
                              }`}
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
            ) : (
              <div className={`col-span-full p-8 text-center ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {t('common.projectCreation.noProjects')}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default DashboardContent;
