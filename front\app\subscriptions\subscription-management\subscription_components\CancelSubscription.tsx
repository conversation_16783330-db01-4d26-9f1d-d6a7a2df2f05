'use client';
import { useState, useEffect, useCallback } from 'react';
import { XCircle, Activity, CheckCircle } from 'lucide-react';
import { API_URL } from '@/app/utils/decode_api';
import ReactivateSubscription from './ReactivateSubscription';

interface CancelSubscriptionProps {
  token?: string;
  onCancellationComplete?: () => void;
  getToken?: () => Promise<string | null>;
}

interface SubscriptionStatus {
  status: string;
  details: {
    id: string;
    status: string;
    current_period_end: string;
    plan_type: string;
    cancel_at_period_end: boolean;
  } | null;
}

export default function CancelSubscription({ token, onCancellationComplete, getToken }: CancelSubscriptionProps) {
  const [status, setStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const checkSubscriptionStatus = useCallback(async () => {
    console.log('Checking subscription status...');
    try {
      // Get a fresh token for each request if getToken is provided
      const currentToken = getToken ? await getToken() : token;
      
      if (!currentToken) {
        console.error('No token available');
        throw new Error('Authentication required');
      }

      const response = await fetch(`${API_URL}/api/subscription-api/status`, {
        headers: {
          'Authorization': `Bearer ${currentToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include'
      });
      
      console.log('Response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.message || errorData.error || 'Failed to fetch subscription status');
      }
      
      const data = await response.json();
      console.log('Subscription data received:', data);
      setStatus(data);
    } catch (err) {
      console.error('Error checking subscription:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to check subscription status';
      setError(errorMessage);
    }
  }, [token, getToken]);

  useEffect(() => {
    console.log('CancelSubscription component mounted/updated');
    console.log('Current token:', token ? 'Present' : 'Missing');
    
    if (token || getToken) {
      checkSubscriptionStatus();
    }
  }, [token, getToken, checkSubscriptionStatus]);

  // Debug effect to log status changes
  useEffect(() => {
    console.log('Status updated:', {
      status: status?.status,
      details: status?.details,
      isActiveSubscription: status?.status === 'active' || status?.status === 'trialing',
      hasValidId: Boolean(status?.details?.id),
      isNotCancelled: !status?.details?.cancel_at_period_end
    });
  }, [status]);

  const handleCancelSubscription = async () => {
    console.log('Attempting to cancel subscription...');
    console.log('Current status:', status);
    
    // Get a fresh token for each request if getToken is provided
    const currentToken = getToken ? await getToken() : token;
    
    if (!currentToken || !status?.details?.id) {
      console.error('Missing required data:', {
        hasToken: Boolean(currentToken),
        hasSubscriptionId: Boolean(status?.details?.id)
      });
      return;
    }
    
    setLoading(true);
    setError(null);
    try {
      console.log('Sending cancel request for subscription:', status.details.id);
      const response = await fetch(`${API_URL}/api/subscription-api/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          subscription_id: status.details.id
        })
      });
  
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Cancel request failed:', errorData);
        throw new Error(errorData.message || errorData.error || 'Failed to cancel subscription');
      }
      
      console.log('Cancel request successful, refreshing status...');
      await checkSubscriptionStatus();
      setShowSuccess(true);
      if (onCancellationComplete) {
        onCancellationComplete();
      }
    } catch (err) {
      console.error('Error in cancel request:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error cancelling subscription';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center space-x-2">
        <Activity className="w-5 h-5" />
        <span>Processing cancellation...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 flex items-center justify-center space-x-2">
        <XCircle className="w-5 h-5" />
        <span>{error}</span>
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="flex flex-col space-y-4">
        <div className="text-green-500 flex items-center justify-center space-x-2">
          <CheckCircle className="w-5 h-5" />
          <span>Subscription successfully cancelled.</span>
        </div>
        <div className="text-yellow-400 flex items-center justify-center space-x-2">
          <span>
            Your subscription will remain active until {status?.details?.current_period_end ? new Date(status.details.current_period_end).toLocaleDateString() : 'N/A'}
          </span>
        </div>
      </div>
    );
  }

  const isActiveSubscription = status?.status === 'active' || status?.status === 'trialing';
  const shouldShowCancelButton = isActiveSubscription && !status?.details?.cancel_at_period_end && status?.details?.id;

  console.log('Render conditions:', {
    isActiveSubscription,
    hasValidId: Boolean(status?.details?.id),
    isNotCancelled: !status?.details?.cancel_at_period_end,
    shouldShowCancelButton
  });

  return (
    <>
      {status?.details?.cancel_at_period_end ? (
        <ReactivateSubscription
          token={token}
          subscriptionEndDate={status.details.current_period_end}
          onReactivationComplete={() => {
            setStatus(null);
            onCancellationComplete?.();
          }}
        />
      ) : shouldShowCancelButton ? (
        <button
          onClick={handleCancelSubscription}
          disabled={loading}
          className="inline-flex items-center space-x-2 bg-red-500 text-white py-3 px-6 rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <span>Processing...</span>
          ) : (
            <>
              <XCircle className="w-5 h-5" />
              <span>Cancel Subscription</span>
            </>
          )}
        </button>
      ) : (
        <div className="text-gray-500">
          {!isActiveSubscription ? "No active subscription found" :
           !status?.details?.id ? "Invalid subscription details" :
           status?.details?.cancel_at_period_end ? "Subscription is already scheduled for cancellation" :
           "Unable to display cancel button"}
        </div>
      )}
    </>
  );
}
