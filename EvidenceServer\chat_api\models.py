from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
import uuid

# Create your models here.

class ChatEntry(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_id = models.CharField(max_length=255)
    conversation_id = models.UUIDField()
    created_at = models.DateTimeField(auto_now_add=True)
    question = models.TextField(blank=True)
    response = models.TextField()
    sources = JSONField(default=list)

    def __str__(self):
        return f"{self.user_id}: {self.question[:50]}..."

    class Meta:
        ordering = ['-created_at']
        db_table = 'chat_history'
