import unittest
from unittest.mock import patch, MagicMock
from ..ai_models.search_ranking import rankArticles
import os
import asyncio
from dotenv import load_dotenv


# -----------Integration test----------------
class TestSearchRankingIntegration(unittest.IsolatedAsyncioTestCase):
    @classmethod
    def setUpClass(cls):
        load_dotenv()
        if not os.getenv("OPENAI_API_KEY"):
            raise ValueError("OPENAI_API_KEY environment variable is required")

    async def test_rank_articles_integration(self):
        # Test parameters
        publication_types = None
        question = "What is the treatment of choice for necrotizing fasciitis in pediatrics patients?"
        
        # Execute ranking with less restrictive parameters
        articles = await rankArticles(
            question=question,
            max_results=100,
            open_access=None,
            start_year=None,
            end_year=None,
            min_citations=0 
        )

        # Verify response
        self.assertIsNotNone(articles)
        self.assertTrue(len(articles) > 0, "No articles were returned")

        # Verify article structure
        first_article = articles[0]
        required_fields = ['title', 'abstract', 'authors', 'journal_title', 'publication_date']
        for field in required_fields:
            self.assertIn(field, first_article)
            self.assertIsNotNone(first_article[field])

        # Print results for manual inspection
        print("\nTest Results:")
        for i, article in enumerate(articles[:10], 1):
            print(f"\nRank {i}:")
            print(f"Title: {article['title']}")
            print(f"Abstract: {article['abstract']}")
            print(f"Journal: {article['journal_title']}")
            print(f"Publication Date: {article['publication_date']}")
            print("-" * 50)

if __name__ == '__main__':
    unittest.main()




# Example usage
async def main():
    question = "What is the impact of GLP-1 RAs on skin?"
    publication_types = ''
    articles = await rankArticles(
        question,
        max_results=100,
        open_access=True,
        license_type=None,
        start_year=2015,
        end_year=2023,
        min_citations=0
    )
    
    for i, article in enumerate(articles[:200], 1):
        print(f"\nRank {i}:")
        print(f"Title: {article['title']}")
        print(f"Authors: <AUTHORS>
        print(f"Journal: {article['journal_title']}")
        print(f"Publication Date: {article['publication_date']}")
        print("-" * 50)

if __name__ == "__main__":
    asyncio.run(main())
