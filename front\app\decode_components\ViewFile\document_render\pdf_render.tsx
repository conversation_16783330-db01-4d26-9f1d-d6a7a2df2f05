'use client';

import React, { useState, useEffect } from 'react';
import { FileText, ExternalLink, ZoomIn, ZoomOut, RotateCw, Download, Printer, ChevronLeft, ChevronRight, Layers, ChevronsUpDown } from 'lucide-react';
import { useTheme } from '@/components/theme/theme-provider';

// Import react-pdf components
import { Document, Page, pdfjs } from 'react-pdf';

// Import react-pdf styles for annotation and text layers
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

// Configure PDF.js worker using external CDN
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

// Add custom scrollbar styles with more specificity and !important
const customStyles = `
  /* Custom scrollbar styling with higher specificity */
  .pdf-scrollbar::-webkit-scrollbar,
  .rpv-core__viewer::-webkit-scrollbar,
  .rpv-core__inner-page::-webkit-scrollbar,
  .rpv-core__page-layer::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
  }
  
  .pdf-scrollbar::-webkit-scrollbar-track,
  .rpv-core__viewer::-webkit-scrollbar-track,
  .rpv-core__inner-page::-webkit-scrollbar-track,
  .rpv-core__page-layer::-webkit-scrollbar-track {
    background: var(--scrollbar-track, #f5f5f4) !important;
  }
  
  .pdf-scrollbar::-webkit-scrollbar-thumb,
  .rpv-core__viewer::-webkit-scrollbar-thumb,
  .rpv-core__inner-page::-webkit-scrollbar-thumb,
  .rpv-core__page-layer::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, #d6d3d1) !important;
    border-radius: 6px !important;
    border: 3px solid var(--scrollbar-track, #f5f5f4) !important;
  }
  
  .pdf-scrollbar::-webkit-scrollbar-thumb:hover,
  .rpv-core__viewer::-webkit-scrollbar-thumb:hover,
  .rpv-core__inner-page::-webkit-scrollbar-thumb:hover,
  .rpv-core__page-layer::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover, #a8a29e) !important;
  }
  
  /* Override PDF viewer background in dark mode with higher specificity */
  .rpv-core__viewer.rpv-core__viewer--dark,
  .rpv-core__inner-pages.rpv-core__inner-pages--dark {
    background-color: var(--pdf-bg, hsl(24 9.8% 10%)) !important;
    color: var(--foreground, white) !important;
  }
  
  /* Fix for the toolbar in dark mode */
  .rpv-core__viewer--dark .rpv-default-layout__toolbar {
    background-color: var(--scrollbar-track, hsl(20 5.9% 20%)) !important;
    border-bottom: 1px solid var(--scrollbar-thumb, hsl(22 5% 35%)) !important;
  }
  
  /* Make sure Firefox also gets scrollbar styling */
  * {
    scrollbar-color: var(--scrollbar-thumb, #d6d3d1) var(--scrollbar-track, #f5f5f4) !important;
    scrollbar-width: thin !important;
  }
`;

interface PdfViewerProps {
  pdfUrl: string | null;
  isLoadingPdf: boolean;
  pdfError: string | null;
  handlePdfAccessDenied: () => Promise<void>;
  handleFileView: (mode: 'preview' | 'external') => void;
}

// Define options outside of the component as a constant to prevent unnecessary reloads
const PDF_OPTIONS = {
  cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
  cMapPacked: true,
  standardFontDataUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts`
};

export function PdfViewer({ 
  pdfUrl, 
  isLoadingPdf, 
  pdfError, 
  handlePdfAccessDenied,
  handleFileView
}: PdfViewerProps) {
  const { theme } = useTheme();
  const [numPages, setNumPages] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [viewMode, setViewMode] = useState<'single' | 'all'>('single');
  // Use a unique key for each PDF URL to force remount when the URL changes
  const documentKey = React.useMemo(() => pdfUrl ? `pdf-${pdfUrl}` : 'no-pdf', [pdfUrl]);
  
  // We're using the constant PDF_OPTIONS directly to prevent unnecessary reloads
  
  // Handle zoom in functionality
  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.1, 2.5));
  };
  
  // Handle zoom out functionality
  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.1, 0.5));
  };
  
  // Handle rotation functionality
  const rotate = () => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  };
  
  // Handle page navigation
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  const goToNextPage = () => {
    if (numPages && currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Handle print functionality
  const handlePrint = () => {
    if (pdfUrl) {
      const printWindow = window.open(pdfUrl, '_blank');
      if (printWindow) {
        printWindow.addEventListener('load', () => {
          printWindow.print();
        });
      }
    }
  };
  
  // Handle download functionality
  const handleDownload = () => {
    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = pdfUrl.split('/').pop() || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Inject custom styles for scrollbars and dark mode with higher priority
  useEffect(() => {
    // Create style element if it doesn't exist
    let styleEl = document.getElementById('pdf-custom-styles');
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = 'pdf-custom-styles';
      // Insert at the beginning of head for higher priority
      document.head.insertBefore(styleEl, document.head.firstChild);
    }
    
    // Function to update styles based on current theme
    const updateStyles = () => {
      styleEl.textContent = customStyles;
    };
    
    // Initial update
    updateStyles();
    
    // Set up a MutationObserver to detect theme changes on the root element
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          updateStyles();
        }
      });
    });
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    // Cleanup on unmount
    return () => {
      observer.disconnect();
      if (styleEl && document.head.contains(styleEl)) {
        document.head.removeChild(styleEl);
      }
    };
  }, []);
  
  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
    setCurrentPage(1); // Reset to first page when a new document loads
  }
  
  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      setNumPages(null);
    };
  }, []);
  
  function onDocumentLoadError(error: Error): void {
    console.error('PDF viewer error:', error);
    
    // Handle different types of errors
    if (error.message?.includes('Invalid PDF') || 
        error.message?.includes('access denied') || 
        error.message?.includes('Failed to fetch')) {
      // Access issues or invalid PDF - try our proxy solution
      handlePdfAccessDenied();
    } else if (error.message?.includes('Missing PDF')) {
      // PDF not found
      handlePdfAccessDenied(); // Try to access through proxy anyway
    } else if (error.message?.includes('Unexpected response')) {
      // Unexpected server response
      handlePdfAccessDenied(); // Try to access through proxy anyway
    } else if (error.message?.includes('Password')) {
      // Password protected PDF
      handleFileView('external'); // Try external view for password-protected files
    } else {
      // Generic error - use handlePdfAccessDenied
      handlePdfAccessDenied();
    }
  }

  if (isLoadingPdf) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className={`${theme === 'dark' ? 'bg-neutral-800 text-neutral-200' : 'bg-white text-neutral-800'} p-4 rounded shadow-md text-center`}>
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4">Loading document...</p>
        </div>
      </div>
    );
  }

  if (pdfError) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <div className={`${theme === 'dark' ? 'bg-neutral-800 text-neutral-200' : 'bg-white text-neutral-800'} p-4 rounded shadow-md text-center max-w-md`}>
          <FileText className="h-16 w-16 mx-auto text-red-500" />
          <h2 className="text-xl font-bold mt-4">Error Loading PDF</h2>
          <p className="mt-2">{pdfError}</p>
          <div className="mt-4 space-x-2">
            <button
              onClick={() => handleFileView('external')}
              className="px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            >
              Open Externally
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!pdfUrl) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className={`${theme === 'dark' ? 'bg-neutral-800 text-neutral-200' : 'bg-white text-neutral-800'} p-4 rounded shadow-md text-center`}>
          <FileText className={`h-10 w-10 mx-auto ${theme === 'dark' ? 'text-neutral-400' : 'text-neutral-500'}`} />
          <p className="mt-4">Select a project to view its file</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col items-center justify-center overflow-hidden">
      {/* PDF Container with rounded corners */}
      <div className="w-full h-full flex flex-col rounded-2xl overflow-hidden">
      {/* PDF Toolbar */}
      <div className={`w-full flex items-center justify-between px-4 py-2 ${theme === 'dark' ? 'bg-[#1e1e1e] text-neutral-200' : 'bg-neutral-100 text-neutral-800'} border-b ${theme === 'dark' ? 'border-neutral-700' : 'border-neutral-300'}`}>
        <div className="flex items-center space-x-2">
          {/* Only show page navigation in single page mode */}
          {viewMode === 'single' && (
            <>
              <button 
                onClick={goToPreviousPage} 
                disabled={currentPage <= 1}
                className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'} ${currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Previous Page"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <span className="text-sm">{currentPage} / {numPages || '?'}</span>
              <button 
                onClick={goToNextPage} 
                disabled={!numPages || currentPage >= numPages}
                className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'} ${!numPages || currentPage >= numPages ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Next Page"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </>
          )}
          {/* Show a different indicator when in all pages mode */}
          {viewMode === 'all' && numPages && (
            <span className="text-sm">All Pages ({numPages})</span>
          )}
        </div>
        
        <div className="flex items-center space-x-3">
          {/* View Mode Toggle */}
          <button 
            onClick={() => setViewMode(viewMode === 'single' ? 'all' : 'single')}
            className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'}`}
            title={viewMode === 'single' ? 'Show All Pages' : 'Single Page View'}
          >
            {viewMode === 'single' ? <ChevronsUpDown className="h-5 w-5" /> : <Layers className="h-5 w-5" />}
          </button>
          
          <div className="flex items-center space-x-1">
            <button 
              onClick={zoomOut} 
              disabled={scale <= 0.5}
              className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'} ${scale <= 0.5 ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Zoom Out"
            >
              <ZoomOut className="h-5 w-5" />
            </button>
            <span className="text-sm w-16 text-center">{Math.round(scale * 100)}%</span>
            <button 
              onClick={zoomIn} 
              disabled={scale >= 2.5}
              className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'} ${scale >= 2.5 ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Zoom In"
            >
              <ZoomIn className="h-5 w-5" />
            </button>
          </div>
          
          <button 
            onClick={rotate} 
            className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'}`}
            title="Rotate"
          >
            <RotateCw className="h-5 w-5" />
          </button>
          
          <button 
            onClick={handlePrint} 
            disabled={!pdfUrl}
            className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'} ${!pdfUrl ? 'opacity-50 cursor-not-allowed' : ''}`}
            title="Print"
          >
            <Printer className="h-5 w-5" />
          </button>
          
          <button 
            onClick={handleDownload} 
            disabled={!pdfUrl}
            className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'} ${!pdfUrl ? 'opacity-50 cursor-not-allowed' : ''}`}
            title="Download"
          >
            <Download className="h-5 w-5" />
          </button>
          
          <button 
            onClick={() => handleFileView('external')}
            className={`p-1 rounded-md ${theme === 'dark' ? 'hover:bg-neutral-700' : 'hover:bg-neutral-200'}`}
            title="Open Externally"
          >
            <ExternalLink className="h-5 w-5" />
          </button>
        </div>
      </div>
      
      <div 
        className={`w-full h-full overflow-auto pdf-scrollbar ${theme === 'dark' ? 'bg-neutral-900' : 'bg-white'}`}
        style={{
          // Add inline scrollbar styles that update with each render based on theme
          '--scrollbar-track': theme === 'dark' ? 'hsl(20 5.9% 15%)' : '#f5f5f4',
          '--scrollbar-thumb': theme === 'dark' ? 'hsl(22 5% 35%)' : '#d6d3d1',
          '--scrollbar-thumb-hover': theme === 'dark' ? 'hsl(24 5% 45%)' : '#a8a29e',
          '--pdf-bg': theme === 'dark' ? 'hsl(24 9.8% 10%)' : '#ffffff',
        } as React.CSSProperties}
      >
        <div className="flex flex-col items-center py-4">
          <Document
            key={documentKey} /* Key ensures proper remounting */
            file={pdfUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            onItemClick={({ pageNumber }) => setCurrentPage(pageNumber)}
            loading={(
              <div className="flex justify-center items-center h-20 w-full">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
              </div>
            )}
            noData={(
              <div className="text-center p-4">
                <p>No PDF file specified.</p>
              </div>
            )}
            error={(
              <div className="text-center p-4">
                <p>An error occurred!</p>
              </div>
            )}
            options={PDF_OPTIONS}
          >
            {/* Display all pages at once with a vertically scrollable layout */}
            {/* Render based on view mode */}
            {numPages ? (
              viewMode === 'single' ? (
                // Single page view - only show current page
                <div className="mb-8">
                  <Page
                    pageNumber={currentPage}
                    className={`shadow-lg ${theme === 'dark' ? 'bg-neutral-800' : 'bg-white'}`}
                    renderTextLayer={true}
                    renderAnnotationLayer={true}
                    width={Math.min(window.innerWidth * 0.9, 800) * scale}
                    rotate={rotation}
                    loading={(
                      <div className="flex justify-center items-center h-20 w-full">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                      </div>
                    )}
                    error={null} // Prevent default error handling at page level
                    onLoadError={(error) => {
                      // Silently handle AbortException errors to prevent console warnings
                      if (!error.message?.includes('AbortException')) {
                        console.error('Page load error:', error);
                      }
                    }}
                  />
                </div>
              ) : (
                // All pages view - show all pages for scrolling
                Array.from(new Array(numPages), (_, index) => (
                  <div key={`page_${index + 1}`} className="mb-8 last:mb-0">
                    <Page
                      pageNumber={index + 1}
                      className={`shadow-lg ${theme === 'dark' ? 'bg-neutral-800' : 'bg-white'}`}
                      renderTextLayer={true}
                      renderAnnotationLayer={true}
                      width={Math.min(window.innerWidth * 0.9, 800) * scale}
                      rotate={rotation}
                      loading={(
                        <div className="flex justify-center items-center h-20 w-full">
                          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                        </div>
                      )}
                      error={null}
                      onLoadError={(error) => {
                        if (!error.message?.includes('AbortException')) {
                          console.error('Page load error:', error);
                        }
                      }}
                    />
                  </div>
                ))
              )
            ) : null}
          </Document>
        </div>
      </div>
      </div>
    </div>
  );
}

export function ExternalViewer({ pdfUrl }: { pdfUrl: string | null }) {
  return (
    <div className="flex justify-center items-center h-full">
      <div className="bg-white dark:bg-neutral-800 p-8 rounded-2xl shadow-md text-center max-w-md">
        <ExternalLink className="h-16 w-16 mx-auto text-blue-500" />
        <h2 className="text-xl font-bold mt-4 text-neutral-800 dark:text-neutral-200">External View</h2>
        <p className="mt-2 text-neutral-600 dark:text-neutral-400">
          The file will open in a new tab. Click the button below to continue.
        </p>
        <a
          href={pdfUrl || '#'}
          target="_blank"
          rel="noopener noreferrer"
          className={`mt-6 inline-block px-6 py-3 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors ${!pdfUrl ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={(e) => !pdfUrl && e.preventDefault()}
        >
          Open File
        </a>
      </div>
    </div>
  );
}
