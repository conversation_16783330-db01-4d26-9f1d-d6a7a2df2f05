'use client';
import { useState, useEffect, useRef, useCallback } from 'react';
import { Search} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import React from 'react';
import { useTheme } from '@/components/theme/theme-provider';
import { getWebSocketUrl } from '@/app/utils/search_api';
import { useLanguage } from '@/app/providers/language-provider';

// Components Imports
import MainLayout from '@/components/NavLayout';


// Types
// Article interface types
interface Article {
  article_link: string;
  title: string;
  authors: string[];
  publisher: string;
  publication_date: string;
}

interface ReferenceProps {
  number: number;
  messageIndex: number;
  scrollToReference: (messageIndex: number, referenceNumber: number) => void;
}

interface ChatProps {
  token: string;
}

// Chat component
export default function Chat({ token }: ChatProps) {
  const [question, setQuestion] = useState('');
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const chatSocket = useRef<WebSocket | null>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [userQuestions, setUserQuestions] = useState<
    Array<{
      type: 'user' | 'assistant' | 'articles';
      content: string;
      isStreaming?: boolean;
      articles?: Article[];
    }>
  >([]);
  
  const { theme } = useTheme();
  const { t } = useLanguage();

  // Track if this is initial state (no searches yet)
  const isInitialState = userQuestions.length === 0;

  const [subscriptionError, setSubscriptionError] = useState<string | null>(null);

  useEffect(() => {
    const handleResize = () => {
    };

    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle chat messages from the websocket
  const handleChatMessage = useCallback(async (event: MessageEvent) => {
    const data = JSON.parse(event.data);
    switch (data.type) {
      case 'complete':
        setIsSearching(false);
        setUserQuestions(prev => {
          const updated = [...prev];
          const lastIndex = updated.length - 1;
          if (lastIndex >= 0) {
            updated[lastIndex] = {
              ...updated[lastIndex],
              isStreaming: false
            };
          }
          return updated;
        });
        setConversationId(data.conversationId);
        break;
      case 'error':
        console.error('Error:', data.content);
        setIsSearching(false);
        if (data.message === 'Limit reached, please upgrade') {
          setSubscriptionError(data.content);
        }
        break;
      case 'response':
        setIsSearching(false);
        setUserQuestions(prev => {
          const updated = [...prev];
          const lastIndex = updated.length - 1;
          
          // If there's no assistant message yet, or the last message isn't an assistant message
          if (lastIndex < 0 || updated[lastIndex].type !== 'assistant') {
            return [
              ...updated,
              { 
                type: 'assistant', 
                content: data.content, 
                isStreaming: true,
                articles: data.articles
              }
            ];
          }
          
          // Append to existing assistant message
          updated[lastIndex] = {
            ...updated[lastIndex],
            content: updated[lastIndex].content + data.content,
            isStreaming: true,
            articles: data.articles
          };
          
          return updated;
        });
        break;
    }
  }, []);

  useEffect(() => {
    const chatWebSocketUrl = getWebSocketUrl(`/ws/chat/?token=${token}`);
    chatSocket.current = new WebSocket(chatWebSocketUrl);
    chatSocket.current.onopen = () => console.log('Chat WebSocket connected');
    chatSocket.current.onmessage = handleChatMessage;

    return () => {
      if (chatSocket.current) chatSocket.current.close();
    };
  }, [token, handleChatMessage]);

  // ------------------------ Send the SEARCH QUESTION to the chat server ------------------------
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!question.trim() || !token) return;

    const currentConversationId = conversationId || uuidv4();
    
    // Set searching state to true
    setIsSearching(true);
    
    // Add user's question to userQuestions
    setUserQuestions(prev => [...prev, { type: 'user', content: question }]);

    setQuestion('');

    if (chatSocket.current) {
      chatSocket.current.send(JSON.stringify({
        question,
        token,
        conversationId: currentConversationId,
      }));
    }
  };

  const Reference: React.FC<ReferenceProps> = ({ number, messageIndex, scrollToReference }) => (
    <a
      href={`#reference-${messageIndex}-${number}`}
      onClick={(e) => {
        e.preventDefault();
        scrollToReference(messageIndex, number);
      }}
      className={`text-xs rounded-full ${
        theme === 'dark' 
          ? 'bg-gray-600 text-gray-100 hover:bg-gray-500' 
          : 'bg-[#091225] text-white hover:bg-[#1e315a]'
      } inline-flex items-center justify-center w-5 h-5 min-w-[20px]`}
    >
      {number}
    </a>
  );

  const scrollToReference = (messageIndex: number, referenceNumber: number) => {
    const element = document.getElementById(`reference-${messageIndex}-${referenceNumber}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <MainLayout>
      <div className={`flex flex-col h-screen ${theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'} ${theme === 'dark' ? 'text-gray-100' : 'text-black'}`}>
        {/*------------------ MAIN CONTENT ------------------*/}
        <div className={`flex-1 flex flex-col items-center ${
          theme === 'dark' ? 'bg-[hsl(0_0%_7.0%)]' : 'bg-white'
        } border-none overflow-y-auto pt-7 transition-all duration-300
          [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:${
            theme === 'dark' ? 'bg-[#050912]' : 'bg-gray-100'
          } [&::-webkit-scrollbar-thumb]:${
            theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
          } [&::-webkit-scrollbar-thumb]:rounded-full`}>
          
          {/* Hero Section - only shown when no questions have been asked */}
          {isInitialState ? (
            <div className="flex flex-col items-center justify-center h-full w-full px-4 text-center">
              <div className="max-w-4xl w-full">
                <h1 className={`text-4xl font-bold mb-6 ${theme === 'dark' ? 'text-white' : 'text-[#091225]'}`}>
                  {t('common.projectCreation.chatTitle')}
                </h1>
                <p className={`text-xl mb-8 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {t('common.projectCreation.chatSubtitle')}
                </p>
                
                {/* Hero search input - larger and more rectangular */}
                <div className={`${theme === 'dark' ? 'bg-transparent' : 'bg-white'} 
                  mx-auto w-full max-w-2xl p-8 rounded-2xl shadow-transparent
                  ${theme === 'dark' ? 'border border-transparent' : 'border border-transparent'}`}>
                  
                  <form onSubmit={handleSubmit} className="flex items-center w-full relative">
                    <textarea
                      ref={textareaRef}
                      value={question}
                      onChange={(e) => {
                        setQuestion(e.target.value);
                      }}
                      className={`flex-grow border ${
                        theme === 'dark' 
                          ? 'border-transparent bg-[#1e1e1e] text-gray-100 hover:bg-[#242424]' 
                          : 'border-transparent text-black bg-gray-100 hover:bg-gray-200'
                      } focus:outline-none rounded-3xl px-6 py-4 w-full pr-24 resize-none overflow-hidden text-lg`}
                      placeholder="Ask a medical research question..."
                      rows={2}
                      style={{ minHeight: '80px', maxHeight: '200px' }}
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                      <button 
                        onClick={handleSubmit}
                        className={`p-3 rounded-full ${
                          theme === 'dark' 
                            ? 'text-white hover:bg-neutral-700 bg-neutral-800' 
                            : 'text-white hover:bg-[#1e315a] bg-[#091225]'
                        } focus:outline-none`}
                      >
                        <Search size={24} />
                      </button>
                    </div>
                  </form>
                </div>
                
                {/* Example medical research questions with icons - moved outside the search input component */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                  <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                    ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                    onClick={() => {
                      setQuestion("What are the latest treatments for Alzheimer's disease?");
                      if (textareaRef.current) textareaRef.current.focus();
                    }}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                      theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                    }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <div>
                      <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                        {t('common.projectCreation.exampleQuestions.alzheimer')}
                      </h3>
                    </div>
                  </div>
                  <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                    ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                    onClick={() => {
                      setQuestion("How effective is immunotherapy for treating lung cancer?");
                      if (textareaRef.current) textareaRef.current.focus();
                    }}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                      theme === 'dark' ? 'text-green-400' : 'text-green-600'
                    }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                    <div>
                      <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                        {t('common.projectCreation.exampleQuestions.lungCancer')}
                      </h3>
                    </div>
                  </div>
                  <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                    ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                    onClick={() => {
                      setQuestion(t('common.projectCreation.exampleQuestions.microbiome'));
                      if (textareaRef.current) textareaRef.current.focus();
                    }}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                      theme === 'dark' ? 'text-purple-400' : 'text-purple-600'
                    }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <div>
                      <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                        {t('common.projectCreation.exampleQuestions.microbiome')}
                      </h3>
                    </div>
                  </div>
                  <div className={`flex items-start p-3 rounded-xl cursor-pointer transition-colors
                    ${theme === 'dark' ? 'hover:text-gray-200' : 'hover:text-gray-900'}`}
                    onClick={() => {
                      setQuestion("What are the long-term effects of COVID-19 infection?");
                      if (textareaRef.current) textareaRef.current.focus();
                    }}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mt-0.5 mr-3 ${
                      theme === 'dark' ? 'text-red-400' : 'text-red-600'
                    }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <div>
                      <h3 className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-900'}`}>
                        {t('common.projectCreation.exampleQuestions.covid')}
                      </h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Regular chat display when there are messages */
            <div ref={chatContainerRef} className="flex-1 p-4 w-full max-w-3xl border-none shadow-none">
              {Array.isArray(userQuestions) && userQuestions.map((message, messageIndex) => (
                <div key={messageIndex} className="mb-4 flex justify-center">
                  <div className={`max-w-[100%] ${
                    message.type === 'user' 
                      ? `${theme === 'dark' ? 'bg-[#333333] text-gray-100' : 'bg-gray-100 text-black'} text-xl text-medium font-medium` 
                      : `${theme === 'dark' ? 'bg-transparent text-gray-200' : 'bg-transparent text-black'} shadow-none border-none`
                  } rounded-lg p-3 shadow`}>
                    {message.type === 'assistant' ? (
                      <>
                        <ReactMarkdown 
                          remarkPlugins={[remarkGfm]}
                          components={{
                            li: ({ ...props}) => {
                              const children = React.Children.toArray(props.children);
                              const processedChildren = children.map((child) => {
                                if (typeof child === 'string') {
                                  const parts = child.split(/(\[\d+\])/g);
                                  return parts.map((part, index) => {
                                    const match = part.match(/\[(\d+)\]/);
                                    if (match) {
                                      const number = parseInt(match[1]);
                                      return (
                                        <Reference 
                                          key={index}
                                          number={number} 
                                          messageIndex={messageIndex}
                                          scrollToReference={scrollToReference} 
                                        />
                                      );
                                    }
                                    return part;
                                  });
                                }
                                return child;
                              });
                              return <li {...props}>{processedChildren}</li>;
                            },
                            text: ({children}) => {
                              if (typeof children === 'string') {
                                const parts = children.split(/(\[\d+\])/g);
                                return (
                                  <>
                                    {parts.map((part, index) => {
                                      const match = part.match(/\[(\d+)\]/);
                                      if (match) {
                                        const number = parseInt(match[1]);
                                        return (
                                          <Reference 
                                            key={index}
                                            number={number} 
                                            messageIndex={messageIndex}
                                            scrollToReference={scrollToReference} 
                                          />
                                        );
                                      }
                                      return part;
                                    })}
                                  </>
                                );
                              }
                              return <>{children}</>;
                            },
                            p: ({ ...props}) => {
                              const children = React.Children.toArray(props.children);
                              const processedChildren = children.map((child) => {
                                if (typeof child === 'string') {
                                  const parts = child.split(/(\[\d+\])/g);
                                  return parts.map((part, index) => {
                                    const match = part.match(/\[(\d+)\]/);
                                    if (match) {
                                      const number = parseInt(match[1]);
                                      return (
                                        <Reference 
                                          key={index}
                                          number={number} 
                                          messageIndex={messageIndex}
                                          scrollToReference={scrollToReference} 
                                        />
                                      );
                                    }
                                    return part;
                                  });
                                }
                                return child;
                              });
                              return <p {...props}>{processedChildren}</p>;
                            },
                            h1: ({ ...props}) => (
                              <h1 {...props} className={`${theme === 'dark' ? 'text-white' : 'text-black'} text-2xl font-bold`} />
                            ),
                            h2: ({ ...props}) => (
                              <h2 {...props} className={`${theme === 'dark' ? 'text-white' : 'text-black'} text-xl font-bold`} />
                            ),
                            h3: ({ ...props}) => (
                              <h3 {...props} className={`${theme === 'dark' ? 'text-white' : 'text-black'} text-lg font-bold`} />
                            ),
                            a: ({ ...props}) => (
                              <a 
                                {...props} 
                                className={`${theme === 'dark' ? 'text-gray-400 hover:text-gray-300' : 'text-gray-700 hover:text-gray-900'}`}
                              />
                            ),
                            strong: ({ ...props}) => (
                              <strong {...props} className={`${theme === 'dark' ? 'text-gray-200' : 'text-black'} font-bold`} />
                            ),
                          }}
                        >
                          {message.content}
                        </ReactMarkdown>
                        {message.articles && message.articles.length > 0 && (
                          <div className="mt-2 relative">
                            <div className="overflow-x-auto" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                              <div className="flex space-x-3 pb-3 px-1">
                                {message.articles.map((article: Article, i: number) => (
                                  <a 
                                    key={i} 
                                    id={`reference-${messageIndex}-${i + 1}`} 
                                    href={article.article_link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className={`flex-shrink-0 w-64 ${
                                      theme === 'dark' 
                                        ? 'bg-[#333333] hover:bg-[#2e2e2e]'
                                        : 'bg-gray-50'
                                    } p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer group`}
                                  >
                                    <div className="flex items-start">
                                      <span className={`text-xs rounded-full ${
                                        theme === 'dark' ? 'bg-gray-600 text-gray-100' : 'bg-gray-800 text-white'
                                      } group-hover:bg-gray-500 inline-flex items-center justify-center w-5 h-5 min-w-[20px] mr-2`}>
                                        {i + 1}
                                      </span>
                                      <div>
                                        <h3 className={`font-bold text-sm ${
                                          theme === 'dark' ? 'text-gray-100 group-hover:text-white' : 'group-hover:bg-gray-200'
                                        } rounded-md line-clamp-2`}>
                                          {article.title}
                                        </h3>
                                        <p className={`text-xs ${
                                          theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                                        } mt-1`}>
                                          {article.authors.slice(0, 3).join(', ')}
                                        </p>
                                        <p className={`text-xs ${
                                          theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                                        } mt-0.5`}>
                                          {article.publisher} | {article.publication_date}
                                        </p>
                                      </div>
                                    </div>
                                  </a>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      message.content
                    )}
                  </div>
                </div>
              ))}
              
              {/* Loading spinner - shown when a search is submitted and waiting for response */}
              {isSearching && (
                <div className="flex justify-center items-center mt-8 mb-4">
                  <div className={`rounded-full h-10 w-10 border-t-2 border-b-2 ${
                    theme === 'dark' ? 'border-gray-300' : 'border-gray-700'
                  } animate-spin`}></div>
                  <span className={`ml-3 text-lg ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>Searching...</span>
                </div>
              )}
            </div>
          )}

          {/* ----------SEARCH BAR----------- */}
          {!isInitialState && (
            <div className="w-full max-w-3xl fixed bottom-4 transform px-4 sm:px-8 lg:px-0">
              <div className={`relative ${theme === 'dark' ? 'bg-[#202124]' : 'bg-white'}`}>
                <form onSubmit={handleSubmit} className="flex items-center w-full">
                  <textarea
                    ref={textareaRef}
                    value={question}
                    onChange={(e) => {
                      setQuestion(e.target.value);
                    }}
                    className={`flex-grow border ${
                      theme === 'dark' 
                        ? 'border-[#3a3a3a] bg-[#202124] text-gray-100 hover:bg-[#242424]' 
                        : 'border-gray-300 text-black hover:bg-gray-200'
                    } focus:outline-none rounded-3xl px-4 py-3 w-full pr-24 resize-none overflow-hidden`}
                    placeholder="Ask a research question..."
                    rows={1}
                    style={{ minHeight: '48px', maxHeight: '200px' }}
                  />
                </form>
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                  <button 
                    onClick={handleSubmit}
                    className={`p-2 rounded-full ${
                      theme === 'dark' 
                        ? 'text-white hover:bg-gray-700' 
                        : 'text-[#091225] hover:bg-gray-200'
                    } focus:outline-none`}
                  >
                    <Search size={24} />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Add subscription error message */}
        {subscriptionError && (
          <div className="w-full max-w-3xl fixed bottom-20 left-1/2 transform -translate-x-1/2 px-4">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Subscription limit reached! </strong>
              <span className="block sm:inline">{subscriptionError}</span>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
