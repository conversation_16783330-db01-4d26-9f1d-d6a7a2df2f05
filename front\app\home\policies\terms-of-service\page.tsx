'use client'
import React from 'react'
import { HeroHeader } from '@/containers/main/hero5-header'
import { MainThemeProvider, useMainTheme } from '@/components/theme/main-theme-provider'

const TermsPageContent = () => {
  const { theme } = useMainTheme()
  return (
    <div className={`min-h-screen flex flex-col bg-background ${theme === 'dark' ? 'dark' : ''}`}>
      <HeroHeader />
      {/* Terms of Service Section */}
      <section className="pt-40 pb-20 bg-neutral-100 dark:bg-neutral-900">
        <div className="container mx-auto px-6 relative">
          <div className="relative z-10">
            <h1 className="text-4xl md:text-6xl font-bold text-[#091225] dark:text-white text-center">
              Terms of Service
            </h1>
            <p className="text-xl text-neutral-500 dark:text-neutral-400 max-w-2xl mx-auto text-center">
              Disclaimer of Medical Advice and Use Limitation
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 px-6 bg-background">
        <div className="container mx-auto max-w-6xl">
          <div className="space-y-12">
            {/* Version and Last Updated */}
            <div className="text-neutral-600 dark:text-neutral-400 text-center">
              <p>Version: 1.0</p>
              <p>Last Updated: April 7, 2025</p>
            </div>

            {/* Welcome Section */}
            <div>
              <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-8">
                Welcome to DecodeMed! These Terms of Service govern your access to and use of the DecodeMed platform, including all content, functionality, and services offered through the platform. By accessing or using DecodeMed, you agree to comply with and be bound by these Terms.
              </p>
            </div>

            {/* Sections */}
            <div className="space-y-10">
              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">1. Acceptance of Terms</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  By creating an account or using DecodeMed, you agree to these Terms and acknowledge our Privacy Policy. If you do not agree to these Terms, you may not access or use the platform.
                </p>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">2. Eligibility</h2>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed space-y-2">
                  <li>DecodeMed is intended for medical students, residents, biomedical researchers, clinicians, and healthcare professionals.</li>
                  <li>Users must be at least 18 years old to access the platform.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">3. Account Registration</h2>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed space-y-2">
                  <li>To use the platform, you must create an account using Google Authentication or password and email.</li>
                  <li>You are responsible for maintaining the confidentiality of your login credentials and any activity conducted through your account.</li>
                  <li>Notify us <NAME_EMAIL> if you suspect unauthorized access to your account.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">4. Permitted Use</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">You agree to use DecodeMed only for lawful purposes, including but not limited to:</p>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">
                  <li>Medical education and learning.</li>
                  <li>Conducting biomedical research</li>
                  <li>Accessing and utilizing AI-powered search and learning functionalities for students, residents, and professionals.</li>
                </ul>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">You must not:</p>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
  
                  <li>Use the platform for any illegal, harmful, or unauthorized purposes.</li>
                  <li>Reverse engineer, modify, or attempt to hack the platform.</li>
                  <li>Share, copy, or redistribute platform content without permission.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">5. Intellectual Property</h2>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed space-y-2">
                  <li>All content, design, code, and features of DecodeMed are owned or licensed by DecodeMed and are protected by copyright, trademark, and other intellectual property laws.</li>
                  <li>You are granted a limited, non-exclusive license to access and use the platform for its intended purposes.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">6. User-Generated Content</h2>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed space-y-2">
                  <li>Any project, research quey, or work you generate using DecodeMed is your own responsibility.</li>
                  <li>By using DecodeMed, you agree that you will not upload, share, or provide any content that is:</li>
                  <li>Unauthorized, infringing, or containing material that violates intellectual property rights.</li>
                  <li>We reserve the right to remove or block any content that violates these terms.</li>
                  <li>We respect all intellectual property rights and will not use any content that is protected by copyright, trademark, or other intellectual property rights.</li>
                  <li>Any input (e.g., search queries) you provide to DecodeMed is used to improve platform functionality.</li>
                  <li>You retain ownership of your data but grant DecodeMed a non-exclusive, royalty-free license to use anonymized data for research and development purposes.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">7. Disclaimer of Warranties</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">DecodeMed is provided on an as-is and as-available basis.</p>
                <h3 className="text-2xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">Important Disclaimer</h3>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">
                  DecodeMed is not intended to be used for medical diagnosis, treatment, or clinical decision-making. The platform provides tools and information to support medical learning, biomedical research and professional analysis, but it should not be relied upon as a substitute for professional medical advice or judgment.
                </p>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">We do not guarantee:</p>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
  
                  <li>You will have high scores or results at university or any other academic institution by only using this platform </li>
                  <li>The accuracy, reliability, or completeness of search results or platform outputs.</li>
                  <li>You will be able to pass any exam or test by only using this platform </li>
                  <li>Uninterrupted or error-free access to the platform.</li>
                </ul>
              </div>

              {/* Continue with remaining sections */}
              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">8. Limitation of Liability</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">To the fullest extent permitted by law, DecodeMed and its affiliates will not be liable for:</p>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  <li>Any direct, indirect, incidental, or consequential damages arising from your use of the platform.</li>
                  <li>Loss of data, business interruption, or any other damages, even if foreseeable.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">9. Third-Party Services</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  DecodeMed integrates with third-party services such as Google Cloud, Vercel Analytics, and Clerk. By using the platform, you acknowledge and agree to their terms and policies, which may differ from ours.
                </p>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">10. Termination of Use</h2>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  <li>DecodeMed reserves the right to suspend or terminate your account or access at our discretion, with or without notice, if you violate these Terms.</li>
                  <li>You may terminate your account at any time <NAME_EMAIL>.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">11. Refund Policy</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">
                  We offer a 7-day refund policy for our subscription services. If you are not satisfied with your subscription, you may request a refund within 7 days of your purchase by contacting our support <NAME_EMAIL>. Please provide your order details and the reason for your refund request.
                </p>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">12. Modification of Terms</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  We reserve the right to update or modify these Terms at any time. Changes will be effective upon posting, and continued use of the platform constitutes your acceptance of the updated Terms.
                </p>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">13. Governing Law and Dispute Resolution</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mb-4">
                  These Terms are governed by and construed under the laws of the State of Wyoming, without regard to conflict of law principles.
                </p>
                <ul className="list-disc pl-6 text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  <li>Any disputes arising from these Terms will be resolved through arbitration in Delaware.</li>
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">14. Indemnification</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  You agree to indemnify and hold DecodeMed, its affiliates, and service providers harmless from any claims, damages, losses, or expenses resulting from your use of the platform or violation of these Terms.
                </p>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">15. Contact Information</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  For questions or concerns about these Terms, contact us at:<br />
                  Email: <EMAIL>
                </p>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">16. Entire Agreement</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  These Terms, along with our Privacy Policy, constitute the entire agreement between you and DecodeMed concerning your use of the platform.
                </p>
              </div>
              <div>
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">17. Acceptance of Updated Terms</h2>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed">
                  By accessing or using DecodeMed, you automatically accept and agree to the most recent version of this Agreement. Each time you access or use the Site and/or Services, you reaffirm your acceptance and agreement to the Terms of Service as they may be updated or modified.
                </p>
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed mt-4">
                  If you do not accept and agree to these Terms in their entirety, you are strictly prohibited from accessing or using the Site and/or Services.
                </p>
              </div>

              <div className="border-t pt-8">
                <p className="text-neutral-700 dark:text-neutral-300 text-lg leading-relaxed text-center">
                  By using DecodeMed, you acknowledge that you have read, understood, and agree to these Terms of Service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

const TermsPage = () => {
  return (
    <MainThemeProvider>
      <TermsPageContent />
    </MainThemeProvider>
  )
}

export default TermsPage;
