import os
from openai import AsyncOpenAI
from dotenv import load_dotenv
from .articles_data import get_pubmed_results
from .question_query import generate_search_query 


load_dotenv()

api_key = os.environ.get("OPENAI_API_KEY")
client = AsyncOpenAI(api_key=api_key)

async def generate_abstract_summaries(question, articles):
    abstract_summaries = []
    for article in articles:
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant that summarizes scientific abstracts in one concise sentence, do not use the word abstract in the summary."
                },
                {
                    "role": "user",
                    "content": f"Summarize the following abstract in one short sentence, max 20 words, focusing on how it responds to this question: '{question}'\n\nAbstract: {article['abstract']}"
                }
            ]
        )
        summary = response.choices[0].message.content.strip()
        abstract_summaries.append({
            'title': article['title'],
            'summary': summary
        })
    return abstract_summaries



