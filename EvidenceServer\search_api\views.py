from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from asgiref.sync import async_to_sync, sync_to_async
import asyncio
from django.db import transaction

from .ai_models.articles_data import get_pubmed_results # Add this import
from .ai_models.question_query import generate_search_query
from .ai_models.articles_summary import generate_summary
from .ai_models.abstract_summary import generate_abstract_summaries
from .ai_models.study_design import extract_study_design
from .ai_models.evidence_report import generate_evidence_report
from .ai_models.search_ranking import rankArticles  # Add this import at the top

from django.http import Http404
from django.core.cache import cache, caches
from django.conf import settings
from django.utils.crypto import get_random_string
import json
import hashlib
from .models import SearchHistory
from .serializers import SearchHistorySerializer
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from subscriptions.middleware import SubscriptionMiddleware, AuthenticationMiddleware
# Search query API view
class SearchQueryView(APIView):
    @async_to_sync
    async def post(self, request):
        question = request.data.get('question', '')
        
        publication_types = request.data.get('publicationTypes', [])
        user_id = request.user_id
        if not user_id:
            return Response({'error': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check subscription MIDDLEWARE
        middleware = SubscriptionMiddleware(None)
        allowed, error_message = await middleware.check_subscription(user_id, 'search')
        
        if not allowed:
            return Response({
                'error': error_message,
                'message': 'Limit reached, please upgrade '
            }, status=status.HTTP_403_FORBIDDEN)
   
        if not question:
            return Response({'error': 'Question parameter is required'}, status=status.HTTP_400_BAD_REQUEST)

        query = await generate_search_query(question, publication_types)
        if not query:
            return Response({'error': 'Failed to generate search query'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        print(f"Query: {query}")
        return Response({
            'query': query,

        })


# Articles API view
class ArticlesView(APIView):
    @async_to_sync
    async def post(self, request):
        # ----- EXTRACT PARAMETERS  FROM FRONTEND -----
        question = request.data.get('question', '')
        query = request.data.get('query', '')
        cursor = request.data.get('cursor', '*')
        max_results = request.data.get('maxResults')
        start_year = request.data.get('startYear')
        end_year = request.data.get('endYear')
        min_citations = request.data.get('minCitations')
        max_citations = request.data.get('maxCitations')
        user_id = request.user_id

        if not user_id:
            return Response({'error': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        if not question and not query:
            return Response({'error': 'Question or query parameter is required'}, status=status.HTTP_400_BAD_REQUEST)
#----- CACHE KEY FOR ARTICLES -----
        # Generate a cache key using the user_id
        cache_key = f"articles_search:{user_id}"

        # If this is a new search (cursor is '*'), clear the previous cache
        if cursor == '*':
            cache.delete(cache_key)

        # Extract the page number from the request
        page = request.data.get('page', 1)
        articles_per_page = 10

        # Try to get the results from cache
        cached_results = cache.get(cache_key)
        if cached_results:
            cached_data = json.loads(cached_results)
            start_index = (page - 1) * articles_per_page
            end_index = start_index + articles_per_page
            paginated_articles = cached_data['articles'][start_index:end_index]
            
            return Response({
                'articles': paginated_articles,
                'total_results': cached_data['total_results'],
                'has_more': end_index < len(cached_data['articles']),
            })
#----- FETCH NEW ARTICLES IF NOT IN CACHE -----
        # If not in cache, fetch and rank the articles
        ranked_articles = await rankArticles(
            question=question or query,
            max_results=100,
            start_year=start_year,
            end_year=end_year,
            cursor=cursor,
            min_citations=min_citations,
            max_citations=max_citations
        )

        if ranked_articles is None:
            return Response({'error': 'Failed to fetch articles'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
#----- PAGINATE NEW ARTICLES -----
        # Prepare the response
        start_index = (page - 1) * articles_per_page
        end_index = start_index + articles_per_page
        paginated_articles = ranked_articles[start_index:end_index]
#----- FINAL RESPONSE AFTER PAGINATION OF NEW ARTICLES -----
        response_data = {
            'articles': paginated_articles,
            'total_results': len(ranked_articles),
            'has_more': end_index < len(ranked_articles),
        }
#----- CACHE NEW ARTICLES AFTER PAGINATION -----
        # Cache all articles
        cache_data = {
            'articles': ranked_articles,
            'total_results': len(ranked_articles),
        }
        cache.set(cache_key, json.dumps(cache_data), timeout=3600)  # Cache for 1 hour

        return Response(response_data)

# Parallel processing API view
class ParallelProcessingView(APIView):
    @async_to_sync
    async def post(self, request):
        question = request.data.get('question', '')
        articles = request.data.get('articles', [])
       
        # Limit to 10 articles for processing
        articles = articles[:10]

        abstract_summaries_task = asyncio.create_task(generate_abstract_summaries(question, articles))
        study_designs_task = asyncio.create_task(extract_study_design(question, articles))

        abstract_summaries, study_designs = await asyncio.gather(
            abstract_summaries_task, study_designs_task
        )

        return Response({
            'abstract_summaries': abstract_summaries,
            'study_designs': study_designs,
        })

# Evidence report API view
class EvidenceReportView(APIView):
    @async_to_sync
    async def post(self, request):
          # Check authentication
        user_id = request.user_id
        if not user_id:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)
        article = request.data.get('article', {})

        if not article or 'abstract' not in article:
            return Response({'error': 'Article with abstract is required'}, status=status.HTTP_400_BAD_REQUEST)

        evidence_report = await generate_evidence_report([article])

        return Response({
            'evidence_report': evidence_report[0] if evidence_report else None,
        })

# Search history API view
class SearchHistoryView(APIView):
    def get(self, request):
        user_id = request.user_id
        if not user_id:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)
        
        try:
            history = SearchHistory.objects.filter(user_id=user_id).order_by('-created_at')[:20]
            serializer = SearchHistorySerializer(history, many=True)
            return Response(serializer.data)
        except Exception as e:
            print(f"Error fetching search history: {str(e)}")
            return Response({
                'error': 'Failed to fetch search history',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
      
        user_id = request.user_id
        if not user_id:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            data = request.data.copy()
            data['user_id'] = user_id
            
            serializer = SearchHistorySerializer(data=data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print(f"Error saving search history: {str(e)}")
            return Response({
                'error': 'Failed to save search history',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)







