'use client'
import React from 'react'
import { HeroHeader } from '@/containers/main/hero5-header'
import { MainThemeProvider, useMainTheme } from '@/components/theme/main-theme-provider'


const PrivacyPageContent = () => {
  const { theme } = useMainTheme()
  return (
    <div className={`min-h-screen items-start text-left bg-background ${theme === 'dark' ? 'dark' : ''}`}>
      <HeroHeader />
      {/* Privacy Policy Section */}
      <section className="pt-40 pb-20 bg-neutral-100 dark:bg-neutral-900">
        <div className="container mx-auto px-6 relative">
          <div className="relative z-10">
            <h1 className="text-4xl md:text-6xl font-bold text-[#091225] dark:text-white text-center">
              Privacy Policy
            </h1>
            <p className="text-xl text-neutral-500 dark:text-neutral-400 max-w-2xl mx-auto text-center">
              Your privacy is important to us
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 px-6 bg-background">
        <div className="container mx-auto max-w-6xl">
          <div className="space-y-12">
            {/* Version and Date */}
            <div>
              <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">DecodeMed Privacy Policy </h2>
              <p className="text-neutral-600 dark:text-neutral-400">Version: 1.0</p>
              <p className="text-neutral-600 dark:text-neutral-400">Last Updated: April 7, 2025</p>
              <p className="text-neutral-700 dark:text-neutral-300 mt-4">
                At DecodeMed, we are committed to protecting your privacy and ensuring the security of your personal information. 
                This Privacy Policy explains how we collect, use, and safeguard your data. By using our platform, you agree to the 
                practices outlined in this Privacy Policy.
              </p>
            </div>

            {/* 1. General Information */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">1. General Information</h2>
              <div className="space-y-4">
                <p className="text-neutral-700 dark:text-neutral-300"><strong>Purpose:</strong> DecodeMed provides an AI-powered medical education and biomedical search platform to support biomedical researchers, clinicians, medical doctors, and other healthcare professionals worldwide.</p>
                <p className="text-neutral-700 dark:text-neutral-300"><strong>International Users:</strong> DecodeMed serves users globally, and data may be processed in countries where we or our service providers operate.</p>
              </div>
            </div>

            {/* 2. Data Collection */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">2. Data Collection</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">We collect the following types of user data:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Personal Identifiers: Name and email address provided during sign-up.</li>
                <li>User Inputs: Search queries and other inputs for usage history.</li>
                <li>Usage Data: Information such as the number and type of queries submitted.</li>
                <li>The amount of usage of our platform by the user.</li>
              </ul>
              <p className="text-neutral-700 dark:text-neutral-300 mt-4"><strong>Account Requirement:</strong> Users must create an account using Clerk or Google Authentication to access the platform.</p>
            </div>

            {/* 3. How We Use Your Data */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">3. How We Use Your Data</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">The data we collect is used for the following purposes:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>To communicate with users for marketing and product updates.</li>
                <li>To conduct research and analytics for product development and improvement.</li>
                <li>To improve the platform and provide better user experience.</li>
                <li>To prevent fraud and security issues.</li>
                <li>To comply with legal and regulatory requirements.</li>
                <li>To provide personalized recommendations based on user behavior and preferences.</li>
              </ul>
            </div>

            {/* 4. Third-Party Integrations */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">4. Third-Party Integrations</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">We use third-party services to operate our platform effectively:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Google Cloud API: Used for user authentication.</li>
                <li>Cloud Hosting Providers: Used for data storage and hosting.</li>
                <li>Analytics Tools: Vercel Analytics is used to collect anonymized data about user behavior.</li>
                <li>Stripe: Used for payment processing and billing.</li>
              

              </ul>
              <p className="text-neutral-700 dark:text-neutral-300 mt-4">Aggregated or anonymized data may be shared with third parties for analytics or system improvement.</p>
            </div>

            {/* 5. Cookies and Tracking */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">5. Cookies and Tracking</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">We use cookies and similar technologies to enhance user experience and monitor platform usage:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Cookies collect anonymized data about user behavior.</li>
                <li>Users can opt out of cookies by configuring their browser settings.</li>
              </ul>
            </div>

            {/* 6. User Rights */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">6. User Rights</h2>
              <p className="text-neutral-700 dark:text-neutral-300 mb-4">Users have the following rights regarding their data:</p>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Access and Modification: You can request access to or corrections of your personal data.</li>
                <li>Data Deletion: You can request the deletion of your personal data.</li>
                <li>Opt-Out: Users may opt out of marketing communications at any time by contacting us.</li>
              </ul>
            </div>

            {/* 7. Data Storage and Security */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">7. Data Storage and Security</h2>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>Storage Locations: Data is stored on secure cloud servers provided by Vercel and Render.</li>
                <li>Security Measures: We rely on the robust security measures of our cloud providers to protect your data.</li>
              </ul>
            </div>

            {/* 8. Compliance */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">8. Compliance</h2>
              <ul className="list-disc pl-6 space-y-2 text-neutral-700 dark:text-neutral-300">
                <li>PHI Exclusion: DecodeMed does not process or handle protected health information (PHI) or other sensitive medical data.</li>
                <li>CCPA Compliance: DecodeMed complies with the California Consumer Privacy Act (CCPA) and similar laws to protect user rights.</li>
              </ul>
            </div>

            {/* 9. Minors and Restricted Users */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">9. Minors and Restricted Users</h2>
              <p className="text-neutral-700 dark:text-neutral-300">DecodeMed is not intended for use by individuals under 18 years old.</p>
            </div>

            {/* 10. Contact Us */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">10. Contact Us</h2>
              <p className="text-neutral-700 dark:text-neutral-300">For privacy-related inquiries or concerns, please contact us at:</p>
              <p className="text-neutral-700 dark:text-neutral-300 mt-2">Email: <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a></p>
            </div>

            {/* Updates Section */}
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">Updates to This Privacy Policy</h2>
              <p className="text-neutral-700 dark:text-neutral-300">
                We may update this Privacy Policy periodically. Changes will be communicated by updating the Last Updated date at the top of the policy.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

const PrivacyPage = () => {
  return (
    <MainThemeProvider>
      <PrivacyPageContent />
    </MainThemeProvider>
  )
}

export default PrivacyPage;
