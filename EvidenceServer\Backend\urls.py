"""
URL configuration for Backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse

# Add this function to handle root URL
def api_root(request):
    return HttpResponse("DecodeMed API is running", content_type="text/plain")

urlpatterns = [
    path('', api_root, name='api-root'),  # Add this line for root URL
    path('admin/', admin.site.urls),
    path('search-api/', include('search_api.urls')),  # Include search app URLs
    path('chat-api/', include('chat_api.urls')),  # Include chat_api app URLs
    path('subscription-api/', include('subscriptions.urls')),
   
]
